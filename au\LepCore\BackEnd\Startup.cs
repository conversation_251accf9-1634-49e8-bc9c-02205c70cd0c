using AutoMapper;
using lep.user;
using LepCore.Services;
using LepCore.Setup;
//using Log4net.Extensions.Logging;
using Microsoft.AspNetCore.Authentication.Cookies;
//using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.Features;
using Microsoft.AspNetCore.Mvc.Authorization;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.FileProviders;
using Microsoft.Extensions.Hosting;
using Microsoft.IdentityModel.Logging;
using Microsoft.IdentityModel.Tokens;
using Microsoft.Net.Http.Headers;
using Microsoft.OpenApi.Models;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using NHibernate;
using Serilog;
using System;
using System.IdentityModel.Tokens.Jwt;
using System.IO;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;
using static NHibernate.Cfg.Environment;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.DependencyInjection;

using IHostingEnvironment = Microsoft.AspNetCore.Hosting.IHostingEnvironment;
using System.Text.Json;
using System.Text.Json.Serialization;
using lep;
using System.Linq.Expressions;


namespace LepCore
{
	public class AsIsNamingPolicy : JsonNamingPolicy
	{
		public override string ConvertName(string name) => name;
	}

	public class Startup
	{
		private const string CORS_ALLOW_ALL = "CorsPolicyAllowAll";
		public static IServiceProvider _serviceProvider;
		private readonly IHostEnvironment _hostingEnv;
		private readonly bool faster = false;
		public static TokenValidationParameters _JWTTokenValidationParameters;

		//public static IHttpContextAccessor HttpContextAccessor { get; set; }

		public static ClaimsPrincipal ValidateJWT(string jwt)
		{
			try
			{
				var claimsPrincipal = new JwtSecurityTokenHandler()
					.ValidateToken(jwt, _JWTTokenValidationParameters, out var rawValidatedToken);

				return claimsPrincipal;
			}
			catch (SecurityTokenValidationException stvex)
			{
				// The token failed validation!
				// TODO: Log it or display an error.
				throw new Exception($"Token failed validation: {stvex.Message}");
			}
			catch (ArgumentException argex)
			{
				// The token was not well-formed or was invalid for some other reason.
				// TODO: Log it or display an error.
				throw new Exception($"Token was invalid: {argex.Message}");
			}
		}


		public Startup(IHostEnvironment env)
		{

			CryptoProviderFactory.DefaultCacheSignatureProviders = false;
			CryptoProviderFactory.Default.CacheSignatureProviders = false;

			_hostingEnv = env;
			var builder = new ConfigurationBuilder()
				.SetBasePath(env.ContentRootPath)
				.AddJsonFile($"appsettings.json", false, true)
				.AddJsonFile($"appsettings.{System.Environment.MachineName}.json", false, true);
			//.AddJsonFile(@"Properties/launchSettings.json", true, true);
			//.AddJsonFile($"appsettings.{env.EnvironmentName}.json", true);



			if (env.IsDevelopment())
			{
				//faster = true;
				// For more details on using the user secret store see http://go.microsoft.com/fwlink/?LinkID=532709
				// builder.AddUserSecrets();

				// This will push telemetry data through Application Insights pipeline faster, allowing you to view results immediately.
				// builder.AddApplicationInsightsSettings(developerMode: true);
			}

			builder.AddEnvironmentVariables();
			Configuration = builder.Build();

			// SqlDependency.Start(Configuration["Nhibernate:Con"]);
		}

		public IConfigurationRoot Configuration { get; }

		public void ConfigureServices(IServiceCollection services)

		{
			IdentityModelEventSource.ShowPII = true;

			var testBox = bool.Parse(Configuration["TestBox"]);
			services.Configure<CookiePolicyOptions>(options =>
			{
				// This lambda determines whether user consent for non-essential cookies is needed for a given request.
				options.CheckConsentNeeded = context => true;
				options.MinimumSameSitePolicy = Microsoft.AspNetCore.Http.SameSiteMode.None;
			});

			//services.AddHsts(options =>
			//{
			//	options.Preload = true;
			//	options.IncludeSubDomains = true;
			//	options.MaxAge = TimeSpan.FromDays(60);
			//	options.ExcludedHosts.Add("example.com");
			//	options.ExcludedHosts.Add("www.example.com");
			//});

			//services.AddHttpsRedirection(options =>
			//{
			//	options.RedirectStatusCode = StatusCodes.Status307TemporaryRedirect;
			//	options.HttpsPort = 5001;
			//});
			services.AddHttpContextAccessor();
			services.AddMemoryCache();
			services.AddHttpClient();

			// Add framework services.
			//services.AddApplicationInsightsTelemetry(Configuration);
			services.AddResponseCompression();
			services.Configure<FormOptions>(_ =>
			{
				_.BufferBody = true;
				_.BufferBodyLengthLimit = long.MaxValue;
				_.MultipartBoundaryLengthLimit = int.MaxValue;
				_.MultipartHeadersLengthLimit = int.MaxValue;
				_.MultipartHeadersCountLimit = int.MaxValue;
				_.MultipartBodyLengthLimit = long.MaxValue; //3GB wuould be enough to accept 500MB Upload
															// base64 enc increase file size
															// function s(n) { return Math.ceil(n / 3) * 4; }
															// 524288000  (500MB) ->  699050668 (667MB)
			});

			var settings = new JsonSerializerSettings
			{
				ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
				Formatting = Formatting.Indented,
				DateTimeZoneHandling = DateTimeZoneHandling.Local,
			};

			//var serializer = JsonSerializer.Create(settings);
			//services.Add(new ServiceDescriptor(typeof(JsonSerializer), provider => serializer, ServiceLifetime.Singleton));

			//services.AddHangfire(_ => {
			//	_.UseStorage(new Hangfire.MemoryStorage.MemoryStorage(new Hangfire.MemoryStorage.MemoryStorageOptions() { }));
			//	//_.UseSqlServerStorage(Configuration["Nhibernate:Con"]);
			//});

			//GlobalJobFilters.Filters.Add(new DisableMultipleQueuedItemsFilter());


			services.AddLogging(loggingBuilder =>
			loggingBuilder.AddSerilog(dispose: true));

			// Add SignalR with consistent JSON serialization
			services.AddSignalR(options =>
			{
				options.EnableDetailedErrors = true;
				options.KeepAliveInterval = TimeSpan.FromSeconds(15);
				options.ClientTimeoutInterval = TimeSpan.FromSeconds(30);
			})
			.AddNewtonsoftJsonProtocol(options =>
			{
				// Use camelCase property naming to match API controller behavior
				options.PayloadSerializerSettings.ContractResolver = new CamelCasePropertyNamesContractResolver();
				options.PayloadSerializerSettings.ReferenceLoopHandling = ReferenceLoopHandling.Ignore;
				options.PayloadSerializerSettings.Formatting = Formatting.Indented;
				options.PayloadSerializerSettings.DateFormatHandling = DateFormatHandling.IsoDateFormat;
				options.PayloadSerializerSettings.DateTimeZoneHandling = DateTimeZoneHandling.Local;
			});

			var lepProfile = new LepAutoMapperProfile(() => _serviceProvider);
			var configAm = new MapperConfiguration(cfg => cfg.AddProfile(lepProfile));

			//config.AssertConfigurationIsValid();

			var mapper = configAm.CreateMapper();
			services.AddSingleton<IMapper>(mapper);

			//services.AddSingleton<IMemoryCache, MemoryCache>();
			services.AddSingleton<IConfigurationRoot>(provider => Configuration);
			services.AddTransient<IHttpContextAccessor, HttpContextAccessor>();
			//services.AddSingleton<ISqlDepSetup, SqlDepSetup>();
			//services.AddLepNHibernate(Configuration, _hostingEnv);


			//services.AddSingleton<AppSessionFactory>(sp =>

			//=================================================

			var config = new NHibernate.Cfg.Configuration();

			config.SetProperty(Dialect, typeof(NHibernate.Dialect.MsSql2005Dialect).AssemblyQualifiedName);
			config.SetProperty(ConnectionDriver, typeof(NHibernate.Driver.SqlClientDriver).AssemblyQualifiedName);
			config.SetProperty(Isolation, "ReadCommitted");
			config.SetProperty(PrepareSql, "false");
			config.SetProperty(CacheProvider, typeof(NHibernate.Caches.CoreMemoryCache.CoreMemoryCacheProvider).AssemblyQualifiedName);
			config.SetProperty(UseSecondLevelCache, "true");
			config.SetProperty(UseQueryCache, "true");

			config.SetProperty(Hbm2ddlAuto, "none");
			config.SetProperty(Hbm2ddlKeyWords, "none");

			var connstr = Configuration["Nhibernate:Con"];
			config.SetProperty(ConnectionString, connstr);
			config.SetProperty(CommandTimeout, "600");

			// call session context works for both HangFire and Web
			config.SetProperty(CurrentSessionContextClass, "call");

			config.SetProperty(DefaultBatchFetchSize, "128");
			config.SetProperty(BatchSize, "50");

			config.SetProperty(NHibernate.Cfg.Environment.ShowSql, "false");
			config.SetProperty(NHibernate.Cfg.Environment.FormatSql, "false");
			//nhConfigurationCache.SetProperty(Environment.CurrentSessionContextClass, typeof(UnitTestSessionContext).AssemblyQualifiedName);
			try
			{
				config.AddAssembly(typeof(ICustomerUser).Assembly);
			}
			catch (Exception ex)
			{
				var m = ex.Message;
			}
			//config.AppendListeners(NHibernate.Event.ListenerType.PostUpdate, new[] { new AuditEventListener() });


			config.AppendListeners(NHibernate.Event.ListenerType.PostUpdate, new[] { new NhAuditEventListener() });
			config.AppendListeners(NHibernate.Event.ListenerType.PostInsert, new[] { new NhAuditEventListener() });
			config.AppendListeners(NHibernate.Event.ListenerType.PostDelete, new[] { new NhAuditEventListener() });

			//nhconfig.AppendListeners(NHibernate.Event.ListenerType.Flush, new[] { new AuditEventListener() });

			////config.SetInterceptor(new NoUpdateInterceptor());
			foreach (NHibernate.Mapping.PersistentClass persistentClass in config.ClassMappings)
			{
				persistentClass.DynamicUpdate = true;
			}


			services.AddSingleton<NHibernate.Cfg.Configuration>(config);
			services.AddSingleton<ISessionFactory>(s =>
			{
				var sf = s.GetRequiredService<NHibernate.Cfg.Configuration>().BuildSessionFactory();
				return sf;
			});
			services.AddScoped(s => s.GetRequiredService<ISessionFactory>()
				.WithOptions().Interceptor(new NHDIInterceptor(s)).OpenSession());


			//==================================================

			//services.AddScoped<NHibernate.ISession>(x => x.GetService<AppSessionFactory>().OpenSession());

			services.AddLepDependencyInjection(Configuration);
			//services.AddScoped<ISession>(s => (s.CreateScope()).GetRequiredService<ISessionFactory>()
			//.WithOptions().Interceptor(new DependencyInjectionInterceptor(s)).OpenSession() as IAuditedSession);


			if (!LepGlobal.Instance.TestBox)
			{
				services.AddSingleton<IHostedService, LepCron3Min>();
				services.AddSingleton<IHostedService, LepCron30Min>();
			}
			services.AddSingleton<IHostedService, LepCron3Min>();

			//services.AddSingleton<IAssemblyLocator, HubCouldNotBeResolvedWorkaround>();
			//services.AddSignalR(options => { options.Hubs.EnableDetailedErrors = true; });

			//services.AddSignalR(options =>
			//{
			//	// Faster pings for testing
			//	options.KeepAliveInterval = TimeSpan.FromSeconds(5);
			//});

			services.AddCors(o =>
			{
				o.AddPolicy(CORS_ALLOW_ALL, p =>
				{
					p.AllowAnyHeader()
						.AllowAnyMethod()
						.AllowCredentials()
						.WithOrigins("https://http://my.lepcolourprinters.com.au",
								"https://printportal.cloud",
								"http://localhost:5000/",
								"https://localhost:5000/"
							);
				});
			});



			services.AddResponseCaching();

			services.AddAuthorization(o =>
			{
				o.AddPolicy(LepRoles.Staff, _ => _.RequireRole(LepRoles.Staff));
				o.AddPolicy(LepRoles.Customer, _ => _.RequireRole(LepRoles.Customer));
				o.AddPolicy(LepRoles.AnonymousWLCustomer, _ => _.RequireRole(LepRoles.AnonymousWLCustomer));
				o.AddPolicy(LepRoles.LoggedInWLCustomer, _ => _.RequireRole(LepRoles.LoggedInWLCustomer));
			});

			var key = Encoding.ASCII.GetBytes(Program.secretKey);

			var loginPath = PathString.FromUriComponent("/index.html/#!/login");


			_JWTTokenValidationParameters = new TokenValidationParameters
			{
				ValidateIssuer = true,
				ValidateAudience = true,
				ValidateLifetime = true,
				ValidateIssuerSigningKey = true,
				ValidIssuer = "Issuer",
				ValidAudience = "Audience",
				IssuerSigningKey = new SymmetricSecurityKey(key),
				TokenDecryptionKey = new SymmetricSecurityKey(key),
				ClockSkew = TimeSpan.FromMinutes(1),
			};

			services.AddAuthentication(options =>
			{
				//options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
				//options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;

				options.DefaultScheme = "JWT_OR_COOKIE";
				options.DefaultChallengeScheme = "JWT_OR_COOKIE";

			})
				// cookies are being deprecated, remains only for old delphi apps
				.AddCookie(_ =>
				{
					_.Cookie.SameSite = Microsoft.AspNetCore.Http.SameSiteMode.None;
					//secure cookie
					_.Cookie.SecurePolicy = Microsoft.AspNetCore.Http.CookieSecurePolicy.Always;
					_.LoginPath = new PathString("/login");
					_.SlidingExpiration = true;
					_.ExpireTimeSpan = TimeSpan.FromHours(3);
					_.Cookie.Path = "/";
					//options.Cookie.Domain =   new Uri(Configuration["AbsolutePathURL"]).Host;
					_.LoginPath = loginPath;
					_.Events = new CookieAuthenticationEvents
					{
						OnRedirectToLogin = ctx =>
						{
							ctx.Response.StatusCode = 401;
							return Task.CompletedTask;
						}
					};
				})
				// Angular frontend uses Encrypted JWT now
				.AddJwtBearer(_ =>
				{
					_.RequireHttpsMetadata = false;
					_.SaveToken = true;
					_.TokenValidationParameters = _JWTTokenValidationParameters;
					_.Challenge = "tell me your token";
				})

				// this is the key piece!
				.AddPolicyScheme("JWT_OR_COOKIE", "JWT_OR_COOKIE", options =>
				{
					// runs on each request
					options.ForwardDefaultSelector = context =>
					{
						// filter by auth type
						string authorization = context.Request.Headers[HeaderNames.Authorization];
						if (!string.IsNullOrEmpty(authorization) && authorization.StartsWith("Bearer "))
							return "Bearer";

						// otherwise always check for cookie auth
						return "Cookies";
					};
				});

			var builder = services.AddMvcCore()
				//.AddApiExplorer()
				.AddAuthorization()
				.AddFormatterMappings()
				.AddNewtonsoftJson(_ =>
				{
					_.SerializerSettings.ContractResolver = new NHibernateContractResolver();
					_.SerializerSettings.ReferenceLoopHandling = ReferenceLoopHandling.Ignore;
					_.SerializerSettings.Formatting = Formatting.Indented;
					_.SerializerSettings.DateFormatHandling = DateFormatHandling.IsoDateFormat;
					_.SerializerSettings.DateTimeZoneHandling = DateTimeZoneHandling.Local;
				})

				// .AddJsonFormatters(_ => {
				// 	_.ContractResolver = new NHibernateContractResolver();
				// 	_.ReferenceLoopHandling = ReferenceLoopHandling.Ignore;
				// 	_.Formatting = Formatting.Indented;
				// 	_.DateFormatHandling = DateFormatHandling.IsoDateFormat;
				// 	_.DateTimeZoneHandling = DateTimeZoneHandling.Local;
				// })
				.AddMvcOptions(_ =>
				{
					var policy = new AuthorizationPolicyBuilder(new[] {
							JwtBearerDefaults.AuthenticationScheme,
							CookieAuthenticationDefaults.AuthenticationScheme
					})
						.RequireAuthenticatedUser()
						.Build();
					_.Filters.Add(new AuthorizeFilter(policy));
					_.MaxModelValidationErrors = 500;
					_.InputFormatters.Insert(0, new PatchInputFormatter());
				});


			//services.AddSpaStaticFiles();
			/*
			services.AddSwaggerGen(_ =>
			{
				//_.OperationFilter<SecurityRequirementsOperationFilter>();
				//_.DocumentFilter<AuthorizeRoleFilter>();
				//_.ResolveConflictingActions(ad => ad.First());

				_.SwaggerDoc("customer", new OpenApiInfo {
					Version = "customer",
					Title = "LEP Colour Printers - Rest API",
					Description = "LEP Colour Printers is a print service company dedicated to providing the fastest turnaround time in Trade Only offset and digital printing throughout Australasia.",
				});

				_.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme {
					Description = "JWT Authorization header using the Bearer scheme (Example: 'Bearer 12345abcdef')",
					Name = "Authorization",
					In = ParameterLocation.Header,
					Type = SecuritySchemeType.ApiKey,
					Scheme = "Bearer"
				});

				_.AddSecurityRequirement(new OpenApiSecurityRequirement {
					{
						new OpenApiSecurityScheme {
							Reference = new OpenApiReference {
								Type = ReferenceType.SecurityScheme,
								Id = "Bearer"
							}
						},
						System.Array.Empty<string>()
					}
				});

			//	_.DescribeAllEnumsAsStrings();
			});
			*/
			_serviceProvider = services.BuildServiceProvider();
		}

		//IApplicationLifetime applicationLifetime,
		//IHttpContextAccessor httpContextAccessor,
		////IServiceProvider serviceProvider,
		//IServiceScopeFactory scopeFactory,

		// This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
		public void Configure(IApplicationBuilder app, Microsoft.AspNetCore.Hosting.IWebHostEnvironment env, Microsoft.Extensions.Logging.ILoggerFactory loggerFactory, IConfigurationRoot config)
		{
			//HttpContextAccessor = httpContextAccessor;
			//app.UseHsts();
			//app.UseHttpsRedirection();
			//app.UseTimingMiddleware();
			if (!faster)
			{
				//applicationLifetime.ApplicationStarted.Register(() => OnStartUp(serviceProvider, scopeFactory));
				//applicationLifetime.ApplicationStopping.Register(() =>
				//{
				//	// SqlDependency.Stop(Configuration["Nhibernate:Con"]); // Stop SqlDependency
				//});
			}
			app.UseResponseCompression();

			// Add global exception handling with console logging
			app.UseMiddleware<ErrorHandlingMiddleware>();

			////// switch to HTTPS://  if not already
			//app.Use(async (ctx1, next) => {//Before RC1, this was called
			//    var request = ctx1.Request;
			//    if (request.IsHttps) {
			//        await next();
			//    } else {
			//        var devPort = Configuration.GetValue<int>("iisSettings:iisExpress:sslPort");
			//        var host = env.IsDevelopment()
			//            ? new HostString(request.Host.Host, 5001)
			//            : new HostString(request.Host.Host);
			//        string newUrl = $"https://{host}{request.PathBase}{request.Path}{request.QueryString}";
			//        ctx1.Response.Redirect(newUrl, true);
			//    }
			//});

			//app.UseMiddleware<CompressionMiddleware>();


			//loggerFactory.AddConsole(Configuration.GetSection("Logging"));
			//loggerFactory.AddDebug();




			app.UseResponseCaching();

			loggerFactory.AddSerilog();

			var contentRoot = Directory.GetCurrentDirectory();
			var wwwRoot = Path.Combine(contentRoot, "wwwroot");
			app.UseDefaultFiles();
			app.UseStaticFiles(new StaticFileOptions
			{
				ServeUnknownFileTypes = true,
				OnPrepareResponse = context =>
				{
					context.Context.Response.Headers["X-UA-Compatible"] = "IE=edge,chrome=1";


					if (context.File.Name.Contains(".html") || context.File.Name.Contains(".js") || context.File.Name.Contains(".css"))
					{
						context.Context.Response.Headers["Cache-Control"] = "no-cache, no-store";
						context.Context.Response.Headers["Pragma"] = "no-cache";
						context.Context.Response.Headers["Expires"] = "-1";
					}
					else
					{
						context.Context.Response.Headers[HeaderNames.CacheControl] = "private,max-age=" + TimeSpan.FromDays(1).TotalSeconds;
					}
				}
			});

			//images
			string imagesFolder = Path.Combine(Path.Combine(config["StaticAssets"], "images"));
			app.UseStaticFiles(new StaticFileOptions
			{
				FileProvider = new PhysicalFileProvider(imagesFolder),
				RequestPath = new PathString("/images"),
				ServeUnknownFileTypes = true,
				OnPrepareResponse = context =>
				{
					context.Context.Response.Headers[HeaderNames.CacheControl] = "private,max-age=" + TimeSpan.FromDays(10).TotalSeconds;
				}
			});

			string logFolder = Path.Combine(Directory.GetCurrentDirectory(), @"logs");
			if (!Directory.Exists(logFolder))
			{
				Directory.CreateDirectory(logFolder);
			}
			app.UseFileServer(new FileServerOptions
			{
				FileProvider = new PhysicalFileProvider(logFolder),
				RequestPath = new PathString("/2019"),
				EnableDirectoryBrowsing = true
			});


			try
			{

				string phoneAudioFolder = Path.Combine(config["DataDirectory"], "3cxRecordings");
				if (!Directory.Exists(phoneAudioFolder))
				{
					Directory.CreateDirectory(phoneAudioFolder);
				}
				app.UseFileServer(new FileServerOptions
				{
					FileProvider = new PhysicalFileProvider(phoneAudioFolder),
					RequestPath = new PathString("/recordings"),
					EnableDirectoryBrowsing = false
				});
			}
			catch (Exception ex)
			{
				Log.Error("3cxRecordings folder problem.");
			}

			app.UseCors(CORS_ALLOW_ALL);
			// In the Configure method, use the UseAuthentication method to invoke the Authentication Middleware that
			// sets the HttpContext.User property. Call the UseAuthentication method before calling UseMvcWithDefaultRoute or UseMvc

			app.UseAuthentication();
			app.UseRouting();
			app.UseAuthorization();
			//app.UseSession(); // IMPORTANT: This session call MUST go before UseMvc()
			//app.UseMiddleware<ApiRequest>();
			//app.UseMiddleware<ResponseTimeMiddleware>();
			//app.UseMiddleware<UserCountMiddleware>();
			//app.UseMiddleware<ResponseTimeMiddleware>();
			//app.UseMiddleware<UserCountMiddleware>();
			//app.UseMiddleware<ErrorHandlingMiddleware>();
			// app.UseMiddleware<UOWTransactionMiddleware>();
			//app.UseMvc(routes =>
			//{
			//	routes.MapRoute("default", "{controller=Home}/{action=Index}/{id?}");
			//	routes.MapRoute("staff", "staff/{controller}/{action}/{id?}");
			//	routes.MapRoute("client", "client/{controller}/{action}/{id?}");
			//});


			app.UseEndpoints(endpoints =>
			{
				endpoints.MapDefaultControllerRoute();
				endpoints.MapHub<LepCore.BackEnd.Hubs.LepCoreHub>("/lepcorehub");
			});

			//      app.UseMvc(routes =>
			//{
			// //routes.MapRoute("SetJobStatus", "setstatus.aspx", new { controller = "SetJobStatus", action = "Index" });
			// //routes.MapRoute("SetOrderStatus", "setorderstatus.aspx", new { controller = "SetOrderStatus", action = "Index" });
			// //routes.MapRoute("SetRunStatus", "setrunstatus.aspx", new { controller = "SetRunStatus", action = "Index" });
			// routes.MapRoute("default", "{controller=Home}/{action=Index}/{id?}");

			// //routes.MapRoute("staff", "staff/{controller}/{action}/{id?}");
			// //routes.MapRoute("client", "client/{controller}/{action}/{id?}");
			//});

			//         //app.UseSignalR(routes =>
			//{
			//	routes.MapHub<LepHub>("/LepHub");
			//});
			//app.UseWebSockets();
			//app.UseSignalR();
			//	app.UseSwagger();
			//	app.UseSwaggerUI(c =>
			//	{
			//		c.SwaggerEndpoint("/swagger/customer/swagger.json", "3.0.0");
			//	});
		}
	}
}
