Changed database context to 'PRD_AU'.
=== CUSTOMER LIST OPTIMIZATION STARTING ===
Database: PRD_AU
Target: Customer list view with projections
Date: 2025-06-08 00:17:43
 
1. CREATING PRIMARY CUSTOMER LIST INDEXES...
Creating Customer list projection index...
ERROR: Column name 'CustomerNr' does not exist in the target table or view.
Creating Customer search prefix index...
ERROR: Column name '<PERSON>rna<PERSON>' does not exist in the target table or view.
Creating Customer filtering index...
ERROR: Column name 'IsEnabled' does not exist in the target table or view.
 
2. CREATING SORTING AND PA<PERSON>NATION INDEXES...
Creating Customer Name sorting index...
ERROR: Column name 'CustomerNr' does not exist in the target table or view.
Creating Customer PaymentTerms sorting index...
ERROR: Column name 'CustomerNr' does not exist in the target table or view.
Creating Customer LastOrderDate sorting index...
ERROR: Column name 'CustomerNr' does not exist in the target table or view.
 
3. CREATING SUPPORTING RELATIONSHIP INDEXES...
Creating SalesRegion LEP_Region index...
SUCCESS: IX_SalesRegion_LEP_Region_PostCode created
  - Optimizes: RegionLep subquery filtering
Creating CustomerNote search index...
ERROR: Cannot find the object "dbo.CustomerNote" because it does not exist or you do not have permissions.
Creating Order Customer relationship index...
ERROR: Column name 'CustomerId' does not exist in the target table or view.
Creating Job Order relationship index...
ERROR: CREATE INDEX failed because the following SET options have incorrect settings: 'QUOTED_IDENTIFIER'. Verify that SET options are correct for use with indexed views and/or indexes on computed columns and/or filtered indexes and/or query notifications and/or XML data type methods and/or spatial index operations.
 
4. CREATING PARENT CUSTOMER FILTERING INDEX...
Creating Customer ParentCustomer index...
ERROR: Column name 'CustomerNr' does not exist in the target table or view.
 
5. UPDATING STATISTICS...
Customer statistics updated
SalesRegion statistics updated
Msg 2706, Level 16, State 6, Server SRV03, Line 248
Table 'CustomerNote' does not exist.
