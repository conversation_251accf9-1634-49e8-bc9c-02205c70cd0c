﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
  </PropertyGroup>

  <ItemGroup>
    <Compile Include="Types.fs" />
    <Compile Include="Configuration.fs" />
    <Compile Include="Database.fs" />
    <Compile Include="MYOB.fs" />
    <Compile Include="OrderProcessing.fs" />
    <Compile Include="Program.fs" />
  </ItemGroup>

  <ItemGroup>
    <!-- Core F# Functional Libraries -->
    <PackageReference Include="FSharp.Data" Version="6.4.0" />
    <PackageReference Include="FSharp.Data.SqlClient" Version="2.0.7" />
    <PackageReference Include="FsToolkit.ErrorHandling" Version="4.15.3" />

    <!-- Configuration and Logging -->
    <PackageReference Include="Microsoft.Extensions.Logging" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Console" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="8.0.0" />

    <!-- Database -->
    <PackageReference Include="Microsoft.Data.SqlClient" Version="5.1.2" />

    <!-- JSON -->
    <PackageReference Include="FSharp.SystemTextJson" Version="1.3.13" />

    <!-- HTTP Client for MYOB -->
    <PackageReference Include="FSharp.Data.Http" Version="6.4.0" />
  </ItemGroup>

</Project>
