-- =============================================
-- Database Optimization Rollback Script
-- Target: [SRV03].[PRD_AU]
-- Purpose: Rollback optimization changes if needed
-- Author: Database Optimization Assistant
-- Date: $(date)
-- =============================================

USE [PRD_AU];
GO

SET NOCOUNT ON;
GO

PRINT '=== DATABASE OPTIMIZATION ROLLBACK STARTING ===';
PRINT 'Database: ' + DB_NAME();
PRINT 'Server: ' + @@SERVERNAME;
PRINT 'Rollback Date: ' + CONVERT(VARCHAR(20), GETDATE(), 120);
PRINT '';
PRINT 'WARNING: This script will remove indexes created by the optimization process.';
PRINT 'Ensure you have verified that these indexes are safe to remove.';
PRINT '';

-- =============================================
-- SECTION 1: IDENTIFY OPTIMIZATION INDEXES
-- =============================================
PRINT '1. IDENTIFYING INDEXES CREATED BY OPTIMIZATION...';

-- Find indexes that match the optimization naming pattern
SELECT 
    OBJECT_SCHEMA_NAME(object_id) AS schema_name,
    OBJECT_NAME(object_id) AS table_name,
    name AS index_name,
    'DROP INDEX [' + name + '] ON [' + OBJECT_SCHEMA_NAME(object_id) + '].[' + OBJECT_NAME(object_id) + '];' AS drop_script,
    create_date,
    modify_date
FROM sys.indexes
WHERE name LIKE 'IX_%'
    AND object_id IN (SELECT object_id FROM sys.tables WHERE is_ms_shipped = 0)
    AND is_primary_key = 0
    AND is_unique_constraint = 0
    AND create_date >= DATEADD(hour, -24, GETDATE()) -- Indexes created in last 24 hours
ORDER BY create_date DESC;

PRINT '';

-- =============================================
-- SECTION 2: ROLLBACK CONFIRMATION
-- =============================================
PRINT '2. ROLLBACK CONFIRMATION REQUIRED';
PRINT 'The following section contains rollback commands.';
PRINT 'UNCOMMENT the sections below to execute rollback operations.';
PRINT '';

/*
-- =============================================
-- SECTION 3: REMOVE OPTIMIZATION INDEXES
-- =============================================
PRINT '3. REMOVING OPTIMIZATION INDEXES...';

-- Create cursor to drop optimization indexes
DECLARE @drop_sql NVARCHAR(MAX);
DECLARE @index_name NVARCHAR(128);
DECLARE @table_name NVARCHAR(128);

DECLARE rollback_cursor CURSOR FOR
SELECT 
    'DROP INDEX [' + name + '] ON [' + OBJECT_SCHEMA_NAME(object_id) + '].[' + OBJECT_NAME(object_id) + '];',
    name,
    OBJECT_NAME(object_id)
FROM sys.indexes
WHERE name LIKE 'IX_%'
    AND object_id IN (SELECT object_id FROM sys.tables WHERE is_ms_shipped = 0)
    AND is_primary_key = 0
    AND is_unique_constraint = 0
    AND create_date >= DATEADD(hour, -24, GETDATE()) -- Only recent optimization indexes
ORDER BY create_date DESC;

OPEN rollback_cursor;
FETCH NEXT FROM rollback_cursor INTO @drop_sql, @index_name, @table_name;

WHILE @@FETCH_STATUS = 0
BEGIN
    PRINT 'Dropping optimization index: ' + @index_name + ' on table: ' + @table_name;
    
    BEGIN TRY
        EXEC sp_executesql @drop_sql;
        PRINT 'SUCCESS: Index dropped successfully.';
    END TRY
    BEGIN CATCH
        PRINT 'ERROR: ' + ERROR_MESSAGE();
        PRINT 'Failed SQL: ' + @drop_sql;
    END CATCH
    
    PRINT '';
    FETCH NEXT FROM rollback_cursor INTO @drop_sql, @index_name, @table_name;
END

CLOSE rollback_cursor;
DEALLOCATE rollback_cursor;

PRINT '';
*/

-- =============================================
-- SECTION 4: MANUAL ROLLBACK COMMANDS
-- =============================================
PRINT '4. MANUAL ROLLBACK COMMANDS';
PRINT 'If you prefer manual rollback, use the commands below:';
PRINT '';

-- Generate individual DROP commands for manual execution
DECLARE @manual_rollback NVARCHAR(MAX) = '';

SELECT @manual_rollback = @manual_rollback + 
    '-- Drop index: ' + name + ' (Created: ' + CONVERT(VARCHAR(20), create_date, 120) + ')' + CHAR(13) + CHAR(10) +
    'DROP INDEX [' + name + '] ON [' + OBJECT_SCHEMA_NAME(object_id) + '].[' + OBJECT_NAME(object_id) + '];' + CHAR(13) + CHAR(10) + CHAR(13) + CHAR(10)
FROM sys.indexes
WHERE name LIKE 'IX_%'
    AND object_id IN (SELECT object_id FROM sys.tables WHERE is_ms_shipped = 0)
    AND is_primary_key = 0
    AND is_unique_constraint = 0
    AND create_date >= DATEADD(hour, -24, GETDATE())
ORDER BY create_date DESC;

PRINT @manual_rollback;

-- =============================================
-- SECTION 5: RESTORE ORIGINAL INDEX SETTINGS
-- =============================================
PRINT '5. RESTORE ORIGINAL INDEX SETTINGS (if needed)';
PRINT 'The following commands can restore indexes to their original fragmentation state:';
PRINT '(This is typically not necessary unless specific fill factors are required)';
PRINT '';

/*
-- Reset fill factor to default (0) for rebuilt indexes
DECLARE @restore_sql NVARCHAR(MAX);
DECLARE restore_cursor CURSOR FOR
SELECT 'ALTER INDEX [' + name + '] ON [' + OBJECT_SCHEMA_NAME(object_id) + '].[' + OBJECT_NAME(object_id) + '] REBUILD WITH (FILLFACTOR = 0);'
FROM sys.indexes
WHERE fill_factor = 90  -- Indexes that were rebuilt with 90% fill factor
    AND object_id IN (SELECT object_id FROM sys.tables WHERE is_ms_shipped = 0)
    AND is_primary_key = 0
    AND type > 0;

OPEN restore_cursor;
FETCH NEXT FROM restore_cursor INTO @restore_sql;

WHILE @@FETCH_STATUS = 0
BEGIN
    PRINT 'Restoring original fill factor: ' + @restore_sql;
    
    BEGIN TRY
        EXEC sp_executesql @restore_sql;
        PRINT 'SUCCESS: Fill factor restored.';
    END TRY
    BEGIN CATCH
        PRINT 'ERROR: ' + ERROR_MESSAGE();
    END CATCH
    
    PRINT '';
    FETCH NEXT FROM restore_cursor INTO @restore_sql;
END

CLOSE restore_cursor;
DEALLOCATE restore_cursor;
*/

-- =============================================
-- SECTION 6: VERIFICATION AFTER ROLLBACK
-- =============================================
PRINT '6. POST-ROLLBACK VERIFICATION';
PRINT 'Run the following queries to verify rollback completion:';
PRINT '';

-- Check for remaining optimization indexes
SELECT 
    'REMAINING_OPTIMIZATION_INDEXES' AS check_type,
    COUNT(*) AS count,
    'SELECT name, create_date FROM sys.indexes WHERE name LIKE ''IX_%'' AND create_date >= DATEADD(hour, -24, GETDATE())' AS verification_query
FROM sys.indexes
WHERE name LIKE 'IX_%'
    AND object_id IN (SELECT object_id FROM sys.tables WHERE is_ms_shipped = 0)
    AND is_primary_key = 0
    AND is_unique_constraint = 0
    AND create_date >= DATEADD(hour, -24, GETDATE());

-- Check index fragmentation status
SELECT 
    'INDEX_FRAGMENTATION_STATUS' AS check_type,
    COUNT(*) AS total_indexes,
    AVG(avg_fragmentation_in_percent) AS avg_fragmentation,
    'SELECT COUNT(*), AVG(avg_fragmentation_in_percent) FROM sys.dm_db_index_physical_stats(DB_ID(), NULL, NULL, NULL, ''LIMITED'')' AS verification_query
FROM sys.dm_db_index_physical_stats(DB_ID(), NULL, NULL, NULL, 'LIMITED')
WHERE index_id > 0;

PRINT '';
PRINT '=== DATABASE OPTIMIZATION ROLLBACK COMPLETED ===';
PRINT 'Verify that all optimization changes have been successfully rolled back.';
PRINT 'Monitor database performance to ensure stability.';
PRINT '';
PRINT 'IMPORTANT NOTES:';
PRINT '1. Statistics updates cannot be rolled back (they improve over time)';
PRINT '2. Index maintenance (rebuild/reorganize) effects cannot be undone';
PRINT '3. Only newly created indexes can be dropped';
PRINT '4. Always test rollback procedures in non-production environments first';
