-- Exception Report - Long Running or Abandoned Sessions
-- Find sessions that have been running too long or appear abandoned

SELECT
    s.SessionId,
    s.JobId,
    j.Name AS JobName,
    o.Id AS OrderId,
    c.UserName AS CustomerName,
    s.Status,
    u.UserName,
    s.StartTime,
    s.LastEventTime,
    s.CurrentState,
    CASE s.CurrentState
        WHEN 0 THEN 'Ready'
        WHEN 1 THEN 'Playing'
        WHEN 2 THEN 'Paused'
        WHEN 3 THEN 'Finished'
    END AS StateDescription,
    -- Calculate current working duration
    CASE
        WHEN s.CurrentState = 1 THEN -- Playing
            DATEDIFF(SECOND, s.StartTime, GETDATE()) - s.TotalPausedDuration
        ELSE
            DATEDIFF(SECOND, s.StartTime, s.LastEventTime) - s.TotalPausedDuration
    END AS CurrentWorkingDurationSeconds,
    FORMAT(DATEADD(SECOND,
        CASE
            WHEN s.CurrentState = 1 THEN
                DATEDIFF(SECOND, s.StartTime, GETDATE()) - s.TotalPausedDuration
            ELSE
                DATEDIFF(SECOND, s.StartTime, s.LastEventTime) - s.TotalPausedDuration
        END, 0), 'HH:mm:ss') AS FormattedDuration,
    DATEDIFF(HOUR, s.LastEventTime, GETDATE()) AS HoursSinceLastUpdate,
    DATEDIFF(HOUR, s.StartTime, GETDATE()) AS HoursSinceStart,
    CASE
        WHEN s.CurrentState = 1 AND DATEDIFF(HOUR, s.LastEventTime, GETDATE()) > 2 THEN 'Possibly Abandoned (Playing)'
        WHEN s.CurrentState = 2 AND DATEDIFF(HOUR, s.LastEventTime, GETDATE()) > 8 THEN 'Long Pause'
        WHEN DATEDIFF(HOUR, s.StartTime, GETDATE()) > 24 THEN 'Very Long Session'
        WHEN (CASE
            WHEN s.CurrentState = 1 THEN DATEDIFF(SECOND, s.StartTime, GETDATE()) - s.TotalPausedDuration
            ELSE DATEDIFF(SECOND, s.StartTime, s.LastEventTime) - s.TotalPausedDuration
        END) > 14400 THEN 'Over 4 Hours Work Time'
        ELSE 'Normal'
    END AS ExceptionType
FROM JobStepActiveSession s
    INNER JOIN Job j ON s.JobId = j.Id
    INNER JOIN [Order] o ON j.OrderId = o.Id
    INNER JOIN LepUser c ON o.UserId = c.Id
    INNER JOIN LepUser u ON s.UserId = u.Id
WHERE s.IsActive = 1
    AND (
        (s.CurrentState = 1 AND DATEDIFF(HOUR, s.LastEventTime, GETDATE()) > 2) OR
        (s.CurrentState = 2 AND DATEDIFF(HOUR, s.LastEventTime, GETDATE()) > 8) OR
        (DATEDIFF(HOUR, s.StartTime, GETDATE()) > 24) OR
        ((CASE
            WHEN s.CurrentState = 1 THEN DATEDIFF(SECOND, s.StartTime, GETDATE()) - s.TotalPausedDuration
            ELSE DATEDIFF(SECOND, s.StartTime, s.LastEventTime) - s.TotalPausedDuration
        END) > 14400)
    )
ORDER BY s.StartTime;
