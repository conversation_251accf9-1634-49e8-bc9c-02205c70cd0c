-- Fix NumberOfMagnets column to match NHibernate mapping
-- This script will:
-- 1. Update any NULL values to 0 (the default)
-- 2. Make the column NOT NULL to match the NHibernate mapping

USE PRD_AU;
GO

-- Step 1: Check for NULL values
SELECT COUNT(*) as NullCount 
FROM Job 
WHERE NumberOfMagnets IS NULL;

-- Step 2: Update NULL values to 0 (the default value)
UPDATE Job 
SET NumberOfMagnets = 0 
WHERE NumberOfMagnets IS NULL;

-- Step 3: Make the column NOT NULL
ALTER TABLE Job 
ALTER COLUMN NumberOfMagnets int NOT NULL;

-- Step 4: Verify the change
SELECT COUNT(*) as TotalJobs, 
       COUNT(NumberOfMagnets) as NonNullCount,
       MIN(NumberOfMagnets) as MinValue,
       MAX(NumberOfMagnets) as MaxValue
FROM Job;

PRINT 'NumberOfMagnets column has been updated to NOT NULL with default value 0';
