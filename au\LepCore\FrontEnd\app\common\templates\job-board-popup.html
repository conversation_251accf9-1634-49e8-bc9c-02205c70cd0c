<!-- Job Board Timing Popup Template -->
<div class="modal-header">
    <button type="button" class="close" ng-click="$dismiss()" aria-label="Close">
        <span aria-hidden="true">&times;</span>
    </button>
    <h4 class="modal-title">
        <i class="glyphicon glyphicon-time"></i>
        Production Step: {{status}}
        <span ng-if="jobData">- Job #{{jobData.Id}}</span>
    </h4>
</div>

<div class="modal-body">
    <!-- Loading State -->
    <div ng-if="loading" class="text-center">
        <i class="fa fa-spinner fa-spin fa-2x"></i>
        <p>Loading job details...</p>
    </div>

    <!-- Error State -->
    <div ng-if="error" class="alert alert-danger">
        <i class="glyphicon glyphicon-exclamation-sign"></i>
        {{error}}
    </div>

    <!-- Main Content -->
    <div ng-if="!loading && !error">
        <!-- Tabs for different views -->
        <ul class="nav nav-tabs" role="tablist">
            <li role="presentation" class="active">
                <a href="#checkpoints" aria-controls="checkpoints" role="tab" data-toggle="tab">
                    <i class="glyphicon glyphicon-check"></i>
                    Quick Checkpoints
                </a>
            </li>
            <li role="presentation">
                <a href="#job-details" aria-controls="job-details" role="tab" data-toggle="tab">
                    <i class="glyphicon glyphicon-file"></i>
                    Job Details
                </a>
            </li>
            <li role="presentation" ng-if="hasDetailedInstructions">
                <a href="#detailed-instructions" aria-controls="detailed-instructions" role="tab" data-toggle="tab">
                    <i class="glyphicon glyphicon-book"></i>
                    Detailed Instructions
                </a>
            </li>
        </ul>

        <!-- Tab Content -->
        <div class="tab-content" style="margin-top: 15px;">
            <!-- Quick Checkpoints Tab -->
            <div role="tabpanel" class="tab-pane active" id="checkpoints">
                <div ng-if="checkpointsInstructions">
                    <!-- Production Instructions (Boss's Checkpoints) -->
                    <div class="production-instructions">
                        <h5>{{checkpointsInstructions.title}}</h5>
                        <div ng-bind-html="checkpointsInstructions.instructions"></div>
                        
                        <!-- Timing Controls -->
                        <div class="timing-controls" style="margin-top: 20px;">
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-success" ng-click="startTiming()" ng-if="!isActive">
                                    <i class="glyphicon glyphicon-play"></i> Start
                                </button>
                                <button type="button" class="btn btn-warning" ng-click="pauseTiming()" ng-if="isActive && !isPaused">
                                    <i class="glyphicon glyphicon-pause"></i> Pause
                                </button>
                                <button type="button" class="btn btn-info" ng-click="resumeTiming()" ng-if="isActive && isPaused">
                                    <i class="glyphicon glyphicon-play"></i> Resume
                                </button>
                                <button type="button" class="btn btn-primary" ng-click="finishTiming()" ng-if="isActive">
                                    <i class="glyphicon glyphicon-stop"></i> Finish
                                </button>
                            </div>
                            
                            <!-- Timer Display -->
                            <div class="timer-display" ng-if="isActive" style="margin-top: 10px;">
                                <span class="label label-info">
                                    <i class="glyphicon glyphicon-time"></i>
                                    {{formatElapsedTime(elapsedTime)}}
                                    <span ng-if="isPaused">(Paused)</span>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
                <div ng-if="!checkpointsInstructions" class="alert alert-info">
                    <i class="glyphicon glyphicon-info-sign"></i>
                    No quick checkpoints available for this status.
                </div>
            </div>

            <!-- Job Details Tab -->
            <div role="tabpanel" class="tab-pane" id="job-details">
                <div ng-if="jobData">
                    <!-- Use the compact job details component -->
                    <lep-job-details-compact job="jobData" show-order-info="true"></lep-job-details-compact>
                </div>
                <div ng-if="!jobData" class="alert alert-warning">
                    <i class="glyphicon glyphicon-warning-sign"></i>
                    Job details not available.
                </div>
            </div>

            <!-- Detailed Instructions Tab -->
            <div role="tabpanel" class="tab-pane" id="detailed-instructions" ng-if="hasDetailedInstructions">
                <div ng-if="detailedInstructions">
                    <h5>{{detailedInstructions.title}}</h5>
                    <div ng-bind-html="detailedInstructions.instructions"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal-footer">
    <div class="pull-left">
        <span ng-if="jobData" class="text-muted">
            Job #{{jobData.Id}} • {{jobData.Name}}
        </span>
    </div>
    <div class="pull-right">
        <button type="button" class="btn btn-default" ng-click="$dismiss()">Close</button>
        <button type="button" class="btn btn-primary" ng-click="viewFullJob()" ng-if="jobData">
            <i class="glyphicon glyphicon-new-window"></i>
            View Full Job
        </button>
    </div>
</div>

<style>
.production-instructions {
    max-height: 400px;
    overflow-y: auto;
}

.timing-controls {
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #dee2e6;
}

.timer-display {
    text-align: center;
}

.timer-display .label {
    font-size: 14px;
    padding: 8px 12px;
}

.tab-content {
    min-height: 300px;
}

.modal-body {
    max-height: 70vh;
    overflow-y: auto;
}
</style>
