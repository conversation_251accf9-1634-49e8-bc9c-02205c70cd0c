{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/invoicer-.txt", "rollingInterval": "Day", "retainedFileCountLimit": 30, "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {Message:lj}{NewLine}{Exception}"}}]}, "Invoicer": {"ConnectionString": "Data Source=SRV03;user id=sa; password=*************; Initial Catalog=PRD_AU", "InvoiceBatchSize": 20, "RefundBatchSize": 10, "CreateOrderInvoice": true, "CreateRefundInvoice": true, "CreatePdfInvoice": true, "EmailPdfInvoice": true, "EmailPdfAddress": null, "DataDirectoryFullName": "\\\\dfs01\\resource", "PdfFolder": "\\\\dfs01\\resource\\invoices", "FontListFolder": "C:\\LEPDATA\\FONTS", "DateFormat": "yyyy-MM-dd HH:mm:ss", "MinimumFinishDate": "2024-02-01T00:00:00", "IgnoreCustomers": ["LEP Colour Printers Pty Ltd", "LEP Marketing", "LEP TEST J", "LEP TEST T", "lepde<PERSON>"], "Smtp": {"Host": "smtp.office365.com", "Port": 587, "EnableSsl": true, "Username": "<EMAIL>", "Password": "Kor72522", "FromAddress": "<EMAIL>", "FromName": "LEP Colour Printers"}, "MYOB": {"DeveloperKey": "", "DeveloperSecret": "", "ConfirmationUrl": "", "CompanyFileName": "LEP Colour Printers Pty Ltd"}}}