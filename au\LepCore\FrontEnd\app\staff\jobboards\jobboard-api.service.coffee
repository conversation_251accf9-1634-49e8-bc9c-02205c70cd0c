appStaff = angular.module('app.staff')

appStaff.service 'JobBoardApiService', [
    '$http', '$q', 'lepApi2'
    ($http, $q, lepApi2) ->

        @getJobs = (facilityStr, board) ->
            params =
                facilityStr: facilityStr || 'FG'
                board: board || 'All'

            $http.get('/api/jobboard/GetJson', { params: params })
                .then (response) -> response.data

        @getVersion = () ->
            $http.get('/api/jobboard/Version')
                .then (response) -> response.data

        # Timing API methods (HTTP only)
        @startTiming = (jobId, status, notes, instructionsViewed, qualityChecksPassed) ->
            request =
                JobId: jobId
                Status: status
                Notes: notes || null
                InstructionsViewed: instructionsViewed || false
                QualityChecksPassed: qualityChecksPassed || false
                WorkstationId: @getWorkstationId()

            console.log("🌐 API SERVICE: START TIMING REQUEST:", request)

            $http.post('/api/jobtiming/play', request)
                .then (response) ->
                    console.log("🌐 API SERVICE: START TIMING RESPONSE:", response.data)
                    response.data
                .catch (error) ->
                    console.error("🌐 API SERVICE: START TIMING ERROR:", error)
                    throw error

        @pauseTiming = (jobId, status, notes) ->
            request =
                JobId: jobId
                Status: status
                Notes: notes || null
                WorkstationId: @getWorkstationId()

            $http.post('/api/jobtiming/pause', request)
                .then (response) -> response.data

        @resumeTiming = (jobId, status, notes) ->
            request =
                JobId: jobId
                Status: status
                Notes: notes || null
                WorkstationId: @getWorkstationId()

            $http.post('/api/jobtiming/resume', request)
                .then (response) -> response.data

        @finishTiming = (jobId, status, notes) ->
            request =
                JobId: jobId
                Status: status
                Notes: notes || null
                WorkstationId: @getWorkstationId()

            $http.post('/api/jobtiming/finish', request)
                .then (response) -> response.data

        @getTimingState = (jobId, status) ->
            console.log("🌐 SERVICE: GET TIMING STATE REQUEST - JobId: #{jobId}, Status: #{status}")

            $http.get("/api/jobtiming/state/#{jobId}/#{encodeURIComponent(status)}")
                .then (response) ->
                    console.log("🌐 SERVICE: GET TIMING STATE RESPONSE:", response.data)
                    response.data
                .catch (error) ->
                    console.error("🌐 SERVICE: GET TIMING STATE ERROR:", error)
                    throw error

        @stopAllSessions = () ->
            $http.post('/api/jobtiming/stop-all')
                .then (response) -> response.data

        @getProductionInstructions = (status) ->
            $http.get("/api/production-instructions/#{encodeURIComponent(status)}")
                .then (response) -> response.data
                .catch (error) ->
                    console.warn('Production instructions not available:', error)
                    null

        @getWorkstationId = () ->
            workstationId = localStorage.getItem('workstationId')
            if !workstationId
                workstationId = "WS-#{Date.now()}-#{Math.random().toString(36).substr(2, 9)}"
                localStorage.setItem('workstationId', workstationId)
            workstationId

        @
]
