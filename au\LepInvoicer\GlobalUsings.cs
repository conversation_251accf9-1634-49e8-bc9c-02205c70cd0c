﻿global using System;
global using System.Collections.Generic;
global using System.Linq;
global using System.Text;
global using System.Threading.Tasks;

global using System;
global using System.Collections.Generic;
global using System.Data.SqlClient;
global using System.IO;
global using System.Linq;
global using System.Net.Mail;
global using System.Net;
global using System.Text;
global using System.Text.RegularExpressions;
global using lep.job;
global using lep.order;
global using lep.user;
global using lep;

global using LepCore.Dto;
global using MYOB.AccountRight.SDK;
global using MYOB.AccountRight.SDK.Contracts;
global using MYOB.AccountRight.SDK.Services.Sale;
global using MYOB.AccountRight.SDK.Services.Contact;
global using MYOB.AccountRight.SDK.Services.Inventory;
global using MYOB.AccountRight.SDK.Services.GeneralLedger;
global using MYOB.AccountRight.SDK.Contracts.Version2.GeneralLedger;
global using MYOB.AccountRight.SDK.Contracts.Version2.Contact;
global using AutoMapper;
global using NHibernate.Linq;
global using MYOB.AccountRight.SDK.Services;
global using myob = MYOB.AccountRight.SDK.Services;
global using MYOB.AccountRight.SDK.Contracts.Version2;
global using Newtonsoft.Json;
global using System.Windows.Forms;
global using MYOB.AccountRight.SDK.Contracts.Version2.Sale;
global using FastReport.Export.PdfSimple;
global using FastReport.Export.Html;
global using NHibernate.Criterion;

// New service-related usings
global using LepInvoicer.Services;
global using LepInvoicer.Configuration;
global using LepInvoicer.Constants;
global using LepInvoicer.Utilities;

// Stub types for compilation (TODO: Replace with actual types)
global using IOrder = System.Object;
global using OrderCredit = System.Object;
global using AccountLink = System.Object;
global using IJob = System.Object;
global using ICustomerUser = System.Object;
