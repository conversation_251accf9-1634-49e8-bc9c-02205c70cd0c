using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using NHibernate;
using NHibernate.Criterion;
using Serilog;
using lep.timing.dto;
using lep.timing.entities;
using lep.timing.enums;
using lep.timing.exceptions;
using lep.security;
using lep.job;

namespace lep.timing.impl
{
    public class TimingApplication : BaseApplication, ITimingApplication
    {
        private readonly IJobApplication _jobApp;
        private readonly IProductionInstructionsService _instructionsService;

        public TimingApplication(ISession session, ISecurityApplication securityApp,
            IJobApplication jobApp, IProductionInstructionsService instructionsService)
            : base(session, securityApp)
        {
            _jobApp = jobApp;
            _instructionsService = instructionsService;
        }

        #region Core Timing Operations

        public async Task<TimingActionResult> Play(TimingActionRequest request, int userId)
        {
            try
            {
                Log.Information("🎬 TIMING APPLICATION: PLAY REQUEST - JobId: {JobId}, Status: {Status}, UserId: {UserId}, WorkstationId: {WorkstationId}",
                    request.JobId, request.Status, userId, request.WorkstationId);

                // Validate request
                ValidateTimingRequest(request, userId);
                Log.Information("🎬 TIMING APPLICATION: Request validation passed");

                // Check if session already exists
                var existingSession = await GetActiveSessionInternalAsync(request.JobId, userId, request.Status);
                if (existingSession != null)
                {
                    Log.Warning("⚠️ TIMING APPLICATION: Session already exists - SessionId: {SessionId}, State: {State}",
                        existingSession.SessionId, existingSession.CurrentState);
                    throw TimingValidationException.SessionAlreadyExists(request.JobId, userId, request.Status);
                }

                // Get instructions if required
                var instructions = await _instructionsService.GetInstructions(request.Status);

                if (TimingConfiguration.REQUIRE_INSTRUCTIONS && !request.InstructionsViewed)
                {
                    return new TimingActionResult
                    {
                        Success = false,
                        Message = "Instructions must be viewed before starting",
                        Instructions = instructions,
                        RequiresInstructions = true
                    };
                }

                // Create new session
                var session = new JobStepActiveSession
                {
                    JobId = request.JobId,
                    UserId = userId,
                    Status = request.Status,
                    CurrentState = TimingSessionState.PLAYING,
                    StartTime = DateTime.Now,
                    LastEventTime = DateTime.Now
                };

                Session.Save(session);
                Log.Information("🎬 TIMING APPLICATION: Session created - SessionId: {SessionId}", session.SessionId);

                // Create timing event
                var timingEvent = new JobStepTimingEvent
                {
                    JobId = request.JobId,
                    UserId = userId,
                    Status = request.Status,
                    EventType = TimingEventType.PLAY,
                    SessionId = session.SessionId,
                    Notes = request.Notes,
                    InstructionsViewed = request.InstructionsViewed,
                    InstructionsViewedAt = request.InstructionsViewed ? DateTime.Now : (DateTime?)null,
                    WorkstationId = request.WorkstationId,
                    IPAddress = GetClientIPAddress(),
                    UserAgent = GetClientUserAgent()
                };

                Session.Save(timingEvent);
                Session.Flush();
                Log.Information("🎬 TIMING APPLICATION: Timing event saved");

                var buttonState = TimingButtonState.Playing(TimeSpan.Zero, session.SessionId);
                Log.Information("🎬 TIMING APPLICATION: Button state created: {@ButtonState}", buttonState);

                Log.Information("✅ TIMING APPLICATION: Timing started successfully for Job {JobId}, User {UserId}, Status {Status}, SessionId: {SessionId}",
                    request.JobId, userId, request.Status, session.SessionId);

                var result = new TimingActionResult
                {
                    Success = true,
                    Message = "Timing started successfully",
                    SessionId = session.SessionId,
                    ButtonState = buttonState,
                    CurrentDuration = TimeSpan.Zero
                };

                Log.Information("🎬 TIMING APPLICATION: Returning result: {@Result}", result);
                return result;
            }
            catch (TimingValidationException ex)
            {
                Log.Warning(ex, "Timing validation error for PLAY action");
                return TimingActionResult.Failure(ex.Message, ex.ErrorCode);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error starting timing for Job {JobId}, User {UserId}", request.JobId, userId);
                return TimingActionResult.Failure("An error occurred while starting timing");
            }
        }

        public async Task<TimingActionResult> Pause(TimingActionRequest request, int userId)
        {
            try
            {
                var session = await GetActiveSessionInternalAsync(request.JobId, userId, request.Status);
                if (session == null)
                {
                    throw TimingValidationException.SessionNotFound(request.JobId, userId, request.Status);
                }

                if (session.CurrentState != TimingSessionState.PLAYING)
                {
                    throw TimingValidationException.InvalidStateTransition(
                        session.CurrentState.ToString(), TimingSessionState.PAUSED.ToString());
                }

                // Update session state
                session.CurrentState = TimingSessionState.PAUSED;
                session.LastEventTime = DateTime.Now;
                session.DateModified = DateTime.Now;

                Session.Update(session);

                // Create timing event
                var timingEvent = new JobStepTimingEvent
                {
                    JobId = request.JobId,
                    UserId = userId,
                    Status = request.Status,
                    EventType = TimingEventType.PAUSE,
                    SessionId = session.SessionId,
                    Notes = request.Notes,
                    WorkstationId = request.WorkstationId,
                    IPAddress = GetClientIPAddress(),
                    UserAgent = GetClientUserAgent()
                };

                Session.Save(timingEvent);
                Session.Flush();

                Log.Information("Timing paused for Job {JobId}, User {UserId}, Status {Status}",
                    request.JobId, userId, request.Status);

                return new TimingActionResult
                {
                    Success = true,
                    Message = "Timing paused successfully",
                    SessionId = session.SessionId,
                    ButtonState = TimingButtonState.Paused(session.CurrentWorkingDuration, session.SessionId),
                    CurrentDuration = session.CurrentWorkingDuration
                };
            }
            catch (TimingValidationException ex)
            {
                Log.Warning(ex, "Timing validation error for PAUSE action");
                return TimingActionResult.Failure(ex.Message, ex.ErrorCode);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error pausing timing for Job {JobId}, User {UserId}", request.JobId, userId);
                return TimingActionResult.Failure("An error occurred while pausing timing");
            }
        }

        public async Task<TimingActionResult> Resume(TimingActionRequest request, int userId)
        {
            try
            {
                var session = await GetActiveSessionInternalAsync(request.JobId, userId, request.Status);
                if (session == null)
                {
                    throw TimingValidationException.SessionNotFound(request.JobId, userId, request.Status);
                }

                if (session.CurrentState != TimingSessionState.PAUSED)
                {
                    throw TimingValidationException.InvalidStateTransition(
                        session.CurrentState.ToString(), TimingSessionState.PLAYING.ToString());
                }

                // Calculate paused duration and add to total
                var pausedDuration = (int)(DateTime.Now - session.LastEventTime).TotalSeconds;
                session.TotalPausedDuration += pausedDuration;
                session.CurrentState = TimingSessionState.PLAYING;
                session.LastEventTime = DateTime.Now;
                session.DateModified = DateTime.Now;

                Session.Update(session);

                // Create timing event
                var timingEvent = new JobStepTimingEvent
                {
                    JobId = request.JobId,
                    UserId = userId,
                    Status = request.Status,
                    EventType = TimingEventType.RESUME,
                    SessionId = session.SessionId,
                    Notes = request.Notes,
                    WorkstationId = request.WorkstationId,
                    IPAddress = GetClientIPAddress(),
                    UserAgent = GetClientUserAgent()
                };

                Session.Save(timingEvent);
                Session.Flush();

                Log.Information("Timing resumed for Job {JobId}, User {UserId}, Status {Status}",
                    request.JobId, userId, request.Status);

                return new TimingActionResult
                {
                    Success = true,
                    Message = "Timing resumed successfully",
                    SessionId = session.SessionId,
                    ButtonState = TimingButtonState.Playing(session.CurrentWorkingDuration, session.SessionId),
                    CurrentDuration = session.CurrentWorkingDuration
                };
            }
            catch (TimingValidationException ex)
            {
                Log.Warning(ex, "Timing validation error for RESUME action");
                return TimingActionResult.Failure(ex.Message, ex.ErrorCode);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error resuming timing for Job {JobId}, User {UserId}", request.JobId, userId);
                return TimingActionResult.Failure("An error occurred while resuming timing");
            }
        }

        public async Task<TimingActionResult> Finish(TimingActionRequest request, int userId)
        {
            try
            {
                var session = await GetActiveSessionInternalAsync(request.JobId, userId, request.Status);
                if (session == null)
                {
                    throw TimingValidationException.SessionNotFound(request.JobId, userId, request.Status);
                }

                if (session.CurrentState == TimingSessionState.FINISHED)
                {
                    throw TimingValidationException.InvalidStateTransition(
                        session.CurrentState.ToString(), "FINISH");
                }

                if (TimingConfiguration.REQUIRE_QUALITY_CHECKS && !request.QualityChecksPassed)
                {
                    return new TimingActionResult
                    {
                        Success = false,
                        Message = "Quality checks must be completed before finishing",
                        RequiresQualityChecks = true
                    };
                }

                // If currently paused, add the paused time
                if (session.CurrentState == TimingSessionState.PAUSED)
                {
                    var pausedDuration = (int)(DateTime.Now - session.LastEventTime).TotalSeconds;
                    session.TotalPausedDuration += pausedDuration;
                }

                // Finalize session
                session.CurrentState = TimingSessionState.FINISHED;
                session.LastEventTime = DateTime.Now;
                session.IsActive = false;
                session.DateModified = DateTime.Now;

                Session.Update(session);

                // Create timing event
                var timingEvent = new JobStepTimingEvent
                {
                    JobId = request.JobId,
                    UserId = userId,
                    Status = request.Status,
                    EventType = TimingEventType.FINISH,
                    SessionId = session.SessionId,
                    Notes = request.Notes,
                    QualityChecksPassed = request.QualityChecksPassed,
                    CompletionNotes = request.CompletionNotes,
                    WorkstationId = request.WorkstationId,
                    IPAddress = GetClientIPAddress(),
                    UserAgent = GetClientUserAgent()
                };

                Session.Save(timingEvent);

                // Create summary record
                await CreateTimingSummaryAsync(session);

                Session.Flush();

                Log.Information("Timing finished for Job {JobId}, User {UserId}, Status {Status}",
                    request.JobId, userId, request.Status);

                return new TimingActionResult
                {
                    Success = true,
                    Message = "Timing completed successfully",
                    SessionId = session.SessionId,
                    ButtonState = TimingButtonState.Finished(session.CurrentWorkingDuration),
                    CurrentDuration = session.CurrentWorkingDuration
                };
            }
            catch (TimingValidationException ex)
            {
                Log.Warning(ex, "Timing validation error for FINISH action");
                return TimingActionResult.Failure(ex.Message, ex.ErrorCode);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error finishing timing for Job {JobId}, User {UserId}", request.JobId, userId);
                return TimingActionResult.Failure("An error occurred while finishing timing");
            }
        }

        #endregion

        #region State and Validation

        public async Task<TimingButtonState> GetButtonStateAsync(int jobId, int userId, string status)
        {
            try
            {
                var session = await GetActiveSessionInternalAsync(jobId, userId, status);
                if (session == null)
                {
                    return TimingButtonState.Initial();
                }

                switch (session.CurrentState)
                {
                    case TimingSessionState.PLAYING:
                        return TimingButtonState.Playing(session.CurrentWorkingDuration, session.SessionId);
                    case TimingSessionState.PAUSED:
                        return TimingButtonState.Paused(session.CurrentWorkingDuration, session.SessionId);
                    case TimingSessionState.FINISHED:
                        return TimingButtonState.Finished(session.CurrentWorkingDuration);
                    default:
                        return TimingButtonState.Initial();
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error getting button state for Job {JobId}, User {UserId}, Status {Status}",
                    jobId, userId, status);
                return TimingButtonState.Initial();
            }
        }

        public async Task<bool> CanUserStartTiming(int jobId, int userId, string status)
        {
            try
            {
                // Timing is always enabled (controlled by constants)

                // Check if job exists and is accessible
                var job = _jobApp.GetJob(jobId);
                if (job == null)
                    return false;

                // Check if user has permission (implement based on your security model)
                // For now, assume all authenticated users can time jobs

                // Check if there's already an active session
                var existingSession = await GetActiveSessionInternalAsync(jobId, userId, status);
                return existingSession == null;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error checking if user can start timing");
                return false;
            }
        }

        public async Task<List<JobStepActiveSession>> GetUserActiveSessionsAsync(int userId)
        {
            try
            {
                var sessions = await Session.CreateCriteria<JobStepActiveSession>()
                    .Add(Restrictions.Eq("UserId", userId))
                    .Add(Restrictions.Eq("IsActive", true))
                    .ListAsync<JobStepActiveSession>();

                return sessions.ToList();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error getting active sessions for user {UserId}", userId);
                return new List<JobStepActiveSession>();
            }
        }

        public async Task<JobStepActiveSession> GetActiveSessionAsync(int jobId, int userId, string status)
        {
            return await GetActiveSessionInternalAsync(jobId, userId, status);
        }

        #endregion

        #region Private Helper Methods

        private async Task<JobStepActiveSession> GetActiveSessionInternalAsync(int jobId, int userId, string status)
        {
            try
            {
                var session = await Session.CreateCriteria<JobStepActiveSession>()
                    .Add(Restrictions.Eq("JobId", jobId))
                    .Add(Restrictions.Eq("UserId", userId))
                    .Add(Restrictions.Eq("Status", status))
                    .Add(Restrictions.Eq("IsActive", true))
                    .UniqueResultAsync<JobStepActiveSession>();

                return session;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error getting active session");
                return null;
            }
        }

        private void ValidateTimingRequest(TimingActionRequest request, int userId)
        {
            if (request.JobId <= 0)
                throw new TimingValidationException("Invalid Job ID");

            if (string.IsNullOrWhiteSpace(request.Status))
                throw new TimingValidationException("Status is required");

            if (userId <= 0)
                throw new TimingValidationException("Invalid User ID");

            // Check if job exists
            var job = _jobApp.GetJob(request.JobId);
            if (job == null)
                throw TimingValidationException.JobNotFound(request.JobId);

            // Additional validation can be added here
        }

        private async Task CreateTimingSummaryAsync(JobStepActiveSession session)
        {
            try
            {
                // Get all events for this session
                var events = await Session.CreateCriteria<JobStepTimingEvent>()
                    .Add(Restrictions.Eq("SessionId", session.SessionId))
                    .ListAsync<JobStepTimingEvent>();

                var summary = new JobStepTimingSummary
                {
                    JobId = session.JobId,
                    UserId = session.UserId,
                    Status = session.Status,
                    SessionId = session.SessionId,
                    TotalDuration = (int)session.CurrentWorkingDuration.TotalSeconds,
                    TotalPausedTime = session.TotalPausedDuration,
                    SessionStart = session.StartTime,
                    SessionEnd = DateTime.Now,
                    EventCount = events.Count,
                    IsCompleted = true
                };

                Session.Save(summary);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error creating timing summary for session {SessionId}", session.SessionId);
                // Don't throw - this is not critical for the main operation
            }
        }

        private string GetClientIPAddress()
        {
            // Implementation depends on your web framework
            // This is a placeholder
            return "127.0.0.1";
        }

        private string GetClientUserAgent()
        {
            // Implementation depends on your web framework
            // This is a placeholder
            return "Unknown";
        }

        #endregion

        #region State and Validation

        public async Task<TimingButtonState> GetButtonState(int jobId, int userId, string status)
        {
            try
            {
                Log.Information("🔍 TIMING APPLICATION: GET BUTTON STATE - JobId: {JobId}, UserId: {UserId}, Status: {Status}",
                    jobId, userId, status);

                var session = await GetActiveSessionInternalAsync(jobId, userId, status);

                if (session == null)
                {
                    Log.Information("🔍 TIMING APPLICATION: No active session found - returning Initial state");
                    return TimingButtonState.Initial();
                }

                Log.Information("🔍 TIMING APPLICATION: Found session - SessionId: {SessionId}, CurrentState: {CurrentState}, Duration: {Duration}",
                    session.SessionId, session.CurrentState, session.CurrentWorkingDuration);

                TimingButtonState buttonState;
                switch (session.CurrentState)
                {
                    case TimingSessionState.PLAYING:
                        buttonState = TimingButtonState.Playing(session.CurrentWorkingDuration, session.SessionId);
                        break;
                    case TimingSessionState.PAUSED:
                        buttonState = TimingButtonState.Paused(session.CurrentWorkingDuration, session.SessionId);
                        break;
                    case TimingSessionState.FINISHED:
                        buttonState = TimingButtonState.Finished(session.CurrentWorkingDuration);
                        break;
                    default:
                        buttonState = TimingButtonState.Initial();
                        break;
                }

                Log.Information("🔍 TIMING APPLICATION: Returning button state: {@ButtonState}", buttonState);
                return buttonState;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "❌ TIMING APPLICATION: Error getting button state for Job {JobId}, User {UserId}, Status {Status}", jobId, userId, status);
                return TimingButtonState.Initial();
            }
        }

        public async Task<List<JobStepActiveSession>> GetUserActiveSessions(int userId)
        {
            try
            {
                var sessions = await Session.CreateCriteria<JobStepActiveSession>()
                    .Add(Restrictions.Eq("UserId", userId))
                    .Add(Restrictions.Eq("IsActive", true))
                    .ListAsync<JobStepActiveSession>();

                return sessions.ToList();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error getting active sessions for user {UserId}", userId);
                return new List<JobStepActiveSession>();
            }
        }

        public async Task<JobStepActiveSession> GetActiveSession(int jobId, int userId, string status)
        {
            return await GetActiveSessionInternalAsync(jobId, userId, status);
        }

        #endregion

        #region Instructions Management

        public async Task<ProductionInstructionsDto> GetInstructions(string status, int? jobTypeId = null)
        {
            return await _instructionsService.GetInstructions(status);
        }

        public async Task<ProductionInstructionsDto> GetInstructionsForJob(int jobId, string status)
        {
            return await _instructionsService.GetInstructionsForJobStep(jobId, status);
        }

        public async Task<List<ProductionInstructionsDto>> GetAllInstructions()
        {
            return await _instructionsService.GetAllInstructions();
        }

        public async Task<ProductionInstructionsDto> SaveInstructions(ProductionInstructionsDto dto, int userId)
        {
            return await _instructionsService.CreateInstructions(dto, userId);
        }

        public async Task DeleteInstructions(int id, int userId)
        {
            await _instructionsService.DeleteInstructions(id, userId);
        }

        #endregion

        #region Cleanup and Maintenance

        public async Task CleanupAbandonedSessions()
        {
            try
            {
                var cutoffTime = DateTime.Now.AddHours(-TimingConfiguration.MAX_SESSION_HOURS);

                var abandonedSessions = await Session.CreateCriteria<JobStepActiveSession>()
                    .Add(Restrictions.Eq("IsActive", true))
                    .Add(Restrictions.Lt("LastEventTime", cutoffTime))
                    .ListAsync<JobStepActiveSession>();

                foreach (var session in abandonedSessions)
                {
                    session.IsActive = false;
                    session.CurrentState = TimingSessionState.FINISHED;
                    session.DateModified = DateTime.Now;
                    Session.Update(session);

                    // Create summary for abandoned session
                    await CreateTimingSummaryAsync(session);
                }

                if (abandonedSessions.Any())
                {
                    Session.Flush();
                    Log.Information("Cleaned up {Count} abandoned timing sessions", abandonedSessions.Count);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error cleaning up abandoned sessions");
            }
        }

        public async Task AutoPauseInactiveSessions()
        {
            try
            {
                var cutoffTime = DateTime.Now.AddMinutes(-TimingConfiguration.AUTO_PAUSE_MINUTES);

                var inactiveSessions = await Session.CreateCriteria<JobStepActiveSession>()
                    .Add(Restrictions.Eq("IsActive", true))
                    .Add(Restrictions.Eq("CurrentState", TimingSessionState.PLAYING))
                    .Add(Restrictions.Lt("LastEventTime", cutoffTime))
                    .ListAsync<JobStepActiveSession>();

                foreach (var session in inactiveSessions)
                {
                    var pausedDuration = (int)(DateTime.Now - session.LastEventTime).TotalSeconds;
                    session.TotalPausedDuration += pausedDuration;
                    session.CurrentState = TimingSessionState.PAUSED;
                    session.LastEventTime = DateTime.Now;
                    session.DateModified = DateTime.Now;
                    Session.Update(session);

                    // Create auto-pause event
                    var timingEvent = new JobStepTimingEvent
                    {
                        JobId = session.JobId,
                        UserId = session.UserId,
                        Status = session.Status,
                        EventType = TimingEventType.PAUSE,
                        SessionId = session.SessionId,
                        Notes = "Auto-paused due to inactivity",
                        WorkstationId = TimingConfiguration.SYSTEM_USER
                    };
                    Session.Save(timingEvent);
                }

                if (inactiveSessions.Any())
                {
                    Session.Flush();
                    Log.Information("Auto-paused {Count} inactive timing sessions", inactiveSessions.Count);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error auto-pausing inactive sessions");
            }
        }

        public async Task<int> StopAllUserSessions(int userId)
        {
            try
            {
                var activeSessions = await Session.CreateCriteria<JobStepActiveSession>()
                    .Add(Restrictions.Eq("UserId", userId))
                    .Add(Restrictions.Eq("IsActive", true))
                    .ListAsync<JobStepActiveSession>();

                foreach (var session in activeSessions)
                {
                    session.IsActive = false;
                    session.CurrentState = TimingSessionState.FINISHED;
                    session.DateModified = DateTime.Now;
                    Session.Update(session);

                    // Create finish event
                    var timingEvent = new JobStepTimingEvent
                    {
                        JobId = session.JobId,
                        UserId = session.UserId,
                        Status = session.Status,
                        EventType = TimingEventType.FINISH,
                        SessionId = session.SessionId,
                        Notes = "Stopped by system/admin",
                        WorkstationId = TimingConfiguration.SYSTEM_USER
                    };
                    Session.Save(timingEvent);

                    await CreateTimingSummaryAsync(session);
                }

                if (activeSessions.Any())
                {
                    Session.Flush();
                    Log.Information("Stopped {Count} active sessions for user {UserId}", activeSessions.Count, userId);
                }

                return activeSessions.Count;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error stopping all sessions for user {UserId}", userId);
                return 0;
            }
        }

        public async Task<int> StopAllSessionsForJob(int jobId)
        {
            try
            {
                var activeSessions = await Session.CreateCriteria<JobStepActiveSession>()
                    .Add(Restrictions.Eq("JobId", jobId))
                    .Add(Restrictions.Eq("IsActive", true))
                    .ListAsync<JobStepActiveSession>();

                foreach (var session in activeSessions)
                {
                    session.IsActive = false;
                    session.CurrentState = TimingSessionState.FINISHED;
                    session.DateModified = DateTime.Now;
                    Session.Update(session);

                    await CreateTimingSummaryAsync(session);
                }

                if (activeSessions.Any())
                {
                    Session.Flush();
                    Log.Information("Stopped {Count} active sessions for job {JobId}", activeSessions.Count, jobId);
                }

                return activeSessions.Count;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error stopping all sessions for job {JobId}", jobId);
                return 0;
            }
        }

        #endregion

        #region Reporting and History

        public async Task<List<JobStepTimingEvent>> GetJobTimingHistoryAsync(int jobId)
        {
            try
            {
                var events = await Session.CreateCriteria<JobStepTimingEvent>()
                    .Add(Restrictions.Eq("JobId", jobId))
                    .AddOrder(Order.Desc("EventTime"))
                    .ListAsync<JobStepTimingEvent>();

                return events.ToList();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error getting timing history for job {JobId}", jobId);
                return new List<JobStepTimingEvent>();
            }
        }

        public async Task<List<JobStepTimingSummary>> GetJobTimingSummaryAsync(int jobId)
        {
            try
            {
                var summaries = await Session.CreateCriteria<JobStepTimingSummary>()
                    .Add(Restrictions.Eq("JobId", jobId))
                    .AddOrder(Order.Desc("SessionStart"))
                    .ListAsync<JobStepTimingSummary>();

                return summaries.ToList();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error getting timing summary for job {JobId}", jobId);
                return new List<JobStepTimingSummary>();
            }
        }

        public async Task<List<JobStepTimingEvent>> GetUserTimingHistoryAsync(int userId, DateTime from, DateTime to)
        {
            try
            {
                var events = await Session.CreateCriteria<JobStepTimingEvent>()
                    .Add(Restrictions.Eq("UserId", userId))
                    .Add(Restrictions.Between("EventTime", from, to))
                    .AddOrder(Order.Desc("EventTime"))
                    .ListAsync<JobStepTimingEvent>();

                return events.ToList();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error getting user timing history for user {UserId}", userId);
                return new List<JobStepTimingEvent>();
            }
        }

        public async Task<Dictionary<string, TimeSpan>> GetAverageTimeByStatusAsync(DateTime from, DateTime to)
        {
            try
            {
                var summaries = await Session.CreateCriteria<JobStepTimingSummary>()
                    .Add(Restrictions.Between("SessionStart", from, to))
                    .Add(Restrictions.Eq("IsCompleted", true))
                    .ListAsync<JobStepTimingSummary>();

                var averages = summaries
                    .GroupBy(s => s.Status)
                    .ToDictionary(
                        g => g.Key,
                        g => TimeSpan.FromSeconds(g.Average(s => s.TotalDuration))
                    );

                return averages;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error getting average times by status");
                return new Dictionary<string, TimeSpan>();
            }
        }

        public async Task<List<JobStepTimingEvent>> GetJobTimingHistory(int jobId)
        {
            try
            {
                var events = await Session.CreateCriteria<JobStepTimingEvent>()
                    .Add(Restrictions.Eq("JobId", jobId))
                    .AddOrder(Order.Desc("EventTime"))
                    .ListAsync<JobStepTimingEvent>();

                return events.ToList();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error getting timing history for job {JobId}", jobId);
                return new List<JobStepTimingEvent>();
            }
        }

        public async Task<List<JobStepTimingSummary>> GetJobTimingSummary(int jobId)
        {
            try
            {
                var summaries = await Session.CreateCriteria<JobStepTimingSummary>()
                    .Add(Restrictions.Eq("JobId", jobId))
                    .AddOrder(Order.Asc("Status"))
                    .ListAsync<JobStepTimingSummary>();

                return summaries.ToList();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error getting timing summary for job {JobId}", jobId);
                return new List<JobStepTimingSummary>();
            }
        }

        public async Task<List<JobStepTimingEvent>> GetUserTimingHistory(int userId, DateTime from, DateTime to)
        {
            try
            {
                var events = await Session.CreateCriteria<JobStepTimingEvent>()
                    .Add(Restrictions.Eq("UserId", userId))
                    .Add(Restrictions.Between("EventTime", from, to))
                    .AddOrder(Order.Desc("EventTime"))
                    .ListAsync<JobStepTimingEvent>();

                return events.ToList();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error getting user timing history for user {UserId}", userId);
                return new List<JobStepTimingEvent>();
            }
        }

        public async Task<Dictionary<string, TimeSpan>> GetAverageTimeByStatus(DateTime from, DateTime to)
        {
            try
            {
                var summaries = await Session.CreateCriteria<JobStepTimingSummary>()
                    .Add(Restrictions.Between("DateCreated", from, to))
                    .ListAsync<JobStepTimingSummary>();

                var averages = summaries
                    .GroupBy(s => s.Status)
                    .ToDictionary(
                        g => g.Key,
                        g => TimeSpan.FromSeconds(g.Average(s => s.TotalDuration))
                    );

                return averages;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error getting average time by status");
                return new Dictionary<string, TimeSpan>();
            }
        }

        public async Task<List<JobStepTimingEvent>> GetTimingEventsByDateRange(DateTime from, DateTime to)
        {
            try
            {
                var events = await Session.CreateCriteria<JobStepTimingEvent>()
                    .Add(Restrictions.Between("EventTime", from, to))
                    .AddOrder(Order.Desc("EventTime"))
                    .ListAsync<JobStepTimingEvent>();

                return events.ToList();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error getting timing events by date range");
                return new List<JobStepTimingEvent>();
            }
        }

        #endregion

        #region Configuration

        public Task<bool> IsTimingEnabled()
        {
            return Task.FromResult(true); // Always enabled
        }

        public Task<bool> RequiresInstructions()
        {
            return Task.FromResult(TimingConfiguration.REQUIRE_INSTRUCTIONS);
        }

        public Task<bool> RequiresQualityChecks()
        {
            return Task.FromResult(TimingConfiguration.REQUIRE_QUALITY_CHECKS);
        }

        public Task<int> GetMaxSessionHours()
        {
            return Task.FromResult(TimingConfiguration.MAX_SESSION_HOURS);
        }

        public Task<int> GetAutoPauseMinutes()
        {
            return Task.FromResult(TimingConfiguration.AUTO_PAUSE_MINUTES);
        }

        #endregion

        #region Helper Methods

        // Helper methods for timing operations (MapToDto removed as instructions are now file-based)

        #endregion
    }
}