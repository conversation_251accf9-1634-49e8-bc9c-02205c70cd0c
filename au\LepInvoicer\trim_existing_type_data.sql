-- Trim existing Type data to remove trailing spaces
-- This updates the existing data after changing the column type

USE PRD_AU;
GO

-- Check current data before trimming
SELECT 
    'BEFORE TRIM' as Status,
    Type,
    LEN(Type) as TypeLength,
    DATALENGTH(Type) as TypeDataLength,
    COUNT(*) as RecordCount
FROM OrderCredit
GROUP BY Type, LEN(Type), DATALENGTH(Type)
ORDER BY Type;

-- Update all Type values to remove trailing spaces
UPDATE OrderCredit 
SET Type = RTRIM(Type);

-- Check data after trimming
SELECT 
    'AFTER TRIM' as Status,
    Type,
    LEN(Type) as TypeLength,
    DATALENGTH(Type) as TypeDataLength,
    COUNT(*) as RecordCount
FROM OrderCredit
GROUP BY Type, LEN(Type), DATALENGTH(Type)
ORDER BY Type;

-- Show some sample records to verify
SELECT TOP 5
    Id,
    Type,
    LEN(Type) as TypeLength,
    DATALENGTH(Type) as TypeDataLength
FROM OrderCredit
ORDER BY Id;

PRINT 'Existing OrderCredit.Type data successfully trimmed';
