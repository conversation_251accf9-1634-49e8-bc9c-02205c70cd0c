namespace LepInvoicer;

//--------------------


public class OAuthKeyService2 : IOAuthKeyService
{
	private const string CsTokensFile = @"c:\myob\Tokens.json";

	private OAuthTokens _tokens;

	/// <summary>
	/// On creation read any settings from file
	/// </summary>
	/// <remarks></remarks>
	public OAuthKeyService2()
	{
		ReadFromFile();
	}

	#region IOAuthKeyService Members

	/// <summary>
	/// Implements the property for OAuthResponse which holdes theTokens
	/// </summary>
	/// <value>object containing OAuthTokens</value>
	/// <returns>Contracts.OAuthTokens</returns>
	/// <remarks>Saves to isolated storage when set</remarks>
	public OAuthTokens OAuthResponse
	{
		get { return _tokens; }
		set
		{
			_tokens = value;
			SaveToFile();
		}
	}

	#endregion

	/// <summary>
	/// Method to read Tokens from Isolated storage
	/// </summary>
	/// <remarks></remarks>
	private void ReadFromFile()
	{
		try
		{

			// Get an isolated store for user and application 
			//			IsolatedStorageFile isoStore = IsolatedStorageFile.GetStore(
			//				IsolatedStorageScope.User| IsolatedStorageScope.Domain | IsolatedStorageScope.Assembly, null, null);
			//
			//			var isoStream = new IsolatedStorageFileStream(CsTokensFile, FileMode.Open,
			//														  FileAccess.Read, FileShare.Read);


			var isoStream = new FileStream(CsTokensFile, FileMode.Open, FileAccess.Read, FileShare.Read);
			var reader = new StreamReader(isoStream);
			// Read the data.

			_tokens = JsonConvert.DeserializeObject<OAuthTokens>(reader.ReadToEnd());
			reader.Close();


			isoStream.Close(); isoStream.Dispose();
		}
		catch (FileNotFoundException)
		{
			// Expected exception if a file cannot be found. This indicates that we have a new user.
			_tokens = null;
		}
	}


	/// <summary>
	/// Method to save tokens to isolated storage
	/// </summary>
	/// <remarks></remarks>
	private void SaveToFile()
	{
		//		// Get an isolated store for user and application 
		//		IsolatedStorageFile isoStore = IsolatedStorageFile.GetStore(
		//			IsolatedStorageScope.User | IsolatedStorageScope.Domain | IsolatedStorageScope.Assembly, null, null);
		//
		//		// Create a file
		//		var isoStream = new IsolatedStorageFileStream(CsTokensFile, FileMode.OpenOrCreate,
		//													  FileAccess.Write, isoStore);

		var isoStream = new FileStream(CsTokensFile, FileMode.OpenOrCreate, FileAccess.Write, FileShare.Write);
		//Position to overwrite the old data.

		isoStream.SetLength(0);

		// Write tokens to file
		var writer = new StreamWriter(isoStream);
		writer.Write(JsonConvert.SerializeObject(_tokens));
		writer.Close();


		isoStream.Close(); isoStream.Dispose();
	}
}
