namespace LepInvoicer.Services;

/// <summary>
/// Main invoicer service interface
/// </summary>
public interface IInvoicerService
{
    /// <summary>
    /// Run the invoicing process
    /// </summary>
    /// <returns>Exit code (0 = success, 1 = failure)</returns>
    Task<int> RunInvoicerAsync();
}

/// <summary>
/// MYOB integration service interface
/// </summary>
public interface IMYOBService
{
    /// <summary>
    /// Initialize MYOB connection and services
    /// </summary>
    Task InitializeAsync();

    /// <summary>
    /// Create an order invoice in MYOB
    /// </summary>
    Task<bool> CreateOrderInvoiceAsync(IOrder order);

    /// <summary>
    /// Create a credit invoice in MYOB
    /// </summary>
    Task<bool> CreateCreditInvoiceAsync(OrderCredit orderCredit);

    /// <summary>
    /// Create a refund invoice in MYOB
    /// </summary>
    Task<bool> CreateRefundInvoiceAsync(OrderCredit orderCredit);

    /// <summary>
    /// Delete existing invoice by number
    /// </summary>
    Task<bool> DeleteInvoiceAsync(string invoiceNumber);

    /// <summary>
    /// Get account link for a job
    /// </summary>
    AccountLink GetAccountLinkFromJob(IJob job);
}

/// <summary>
/// Email service interface
/// </summary>
public interface IEmailService
{
    /// <summary>
    /// Send invoice email with PDF attachment
    /// </summary>
    Task<bool> SendInvoiceEmailAsync(string toAddress, string invoiceNumber, string pdfFilePath);

    /// <summary>
    /// Create a new email message
    /// </summary>
    MailMessage CreateEmail();
}

/// <summary>
    /// PDF generation service interface
/// </summary>
public interface IPdfService
{
    /// <summary>
    /// Generate PDF invoice for an order
    /// </summary>
    Task<string> GenerateOrderInvoicePdfAsync(IOrder order, string outputPath);

    /// <summary>
    /// Generate PDF invoice for a credit/refund
    /// </summary>
    Task<string> GenerateCreditInvoicePdfAsync(OrderCredit orderCredit, string outputPath);
}

/// <summary>
/// Database service interface
/// </summary>
public interface IDatabaseService
{
    /// <summary>
    /// Initialize database connection
    /// </summary>
    Task InitializeAsync();

    /// <summary>
    /// Get orders to invoice
    /// </summary>
    Task<List<KeyValuePair<int, string>>> GetOrdersToInvoiceAsync(int batchSize);

    /// <summary>
    /// Get credits to invoice
    /// </summary>
    Task<List<OrderCredit>> GetCreditsToInvoiceAsync(int batchSize);

    /// <summary>
    /// Get refunds to invoice
    /// </summary>
    Task<List<OrderCredit>> GetRefundsToInvoiceAsync(int batchSize);

    /// <summary>
    /// Get order by ID
    /// </summary>
    Task<IOrder> GetOrderAsync(int orderId);

    /// <summary>
    /// Execute SQL command
    /// </summary>
    Task ExecuteSqlAsync(string sql);

    /// <summary>
    /// Mark order as invoiced
    /// </summary>
    Task MarkOrderAsInvoicedAsync(int orderId);

    /// <summary>
    /// Mark order as failed
    /// </summary>
    Task MarkOrderAsFailedAsync(int orderId, string errorMessage);

    /// <summary>
    /// Mark credit as invoiced
    /// </summary>
    Task MarkCreditAsInvoicedAsync(int creditId);

    /// <summary>
    /// Log invoicing result
    /// </summary>
    Task LogInvoicingResultAsync(int orderId, int jobCount, decimal total, DateTime finishDate, bool success, string? details);

    /// <summary>
    /// Cleanup old invoicer logs
    /// </summary>
    Task CleanupInvoicerLogsAsync();

    /// <summary>
    /// Dispose resources
    /// </summary>
    void Dispose();
}
