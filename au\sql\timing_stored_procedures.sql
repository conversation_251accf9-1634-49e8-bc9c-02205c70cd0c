-- Timing System Stored Procedures
-- These procedures handle timing operations more efficiently than application code
-- by reducing network round-trips and handling complex logic in the database

USE PRD_AU
GO

-- =============================================
-- Procedure: sp_TimingStart
-- Description: Start timing for a job step
-- =============================================
CREATE OR ALTER PROCEDURE [dbo].[sp_TimingStart]
    @JobId INT,
    @UserId INT,
    @Status VARCHAR(24),
    @Notes VARCHAR(500) = NULL,
    @SessionId UNIQUEIDENTIFIER OUTPUT,
    @Success BIT OUTPUT,
    @Message VARCHAR(500) OUTPUT
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        BEGIN TRANSACTION;
        
        -- Check if job exists
        IF NOT EXISTS (SELECT 1 FROM Job WHERE Id = @JobId)
        BEGIN
            SET @Success = 0;
            SET @Message = 'Job not found';
            R<PERSON><PERSON><PERSON><PERSON>K TRANSACTION;
            RETURN;
        END
        
        -- Check for existing active session
        IF EXISTS (SELECT 1 FROM JobStepActiveSession 
                  WHERE JobId = @JobId AND UserId = @UserId AND Status = @Status AND IsActive = 1)
        BEGIN
            SET @Success = 0;
            SET @Message = 'Active session already exists for this job/user/status';
            ROLLBACK TRANSACTION;
            RETURN;
        END
        
        -- Check for finished session (prevent restart)
        IF EXISTS (SELECT 1 FROM JobStepActiveSession 
                  WHERE JobId = @JobId AND UserId = @UserId AND Status = @Status 
                  AND CurrentState = 'FINISHED')
        BEGIN
            SET @Success = 0;
            SET @Message = 'Timing already completed for this job/status';
            ROLLBACK TRANSACTION;
            RETURN;
        END
        
        -- Generate new session ID
        SET @SessionId = NEWID();
        
        -- Create active session
        INSERT INTO JobStepActiveSession (JobId, UserId, Status, SessionId, CurrentState, StartTime, LastEventTime)
        VALUES (@JobId, @UserId, @Status, @SessionId, 'PLAYING', GETDATE(), GETDATE());
        
        -- Create timing event
        INSERT INTO JobStepTimingEvent (JobId, UserId, Status, EventType, EventTime, SessionId, Notes)
        VALUES (@JobId, @UserId, @Status, 'START', GETDATE(), @SessionId, @Notes);
        
        SET @Success = 1;
        SET @Message = 'Timing started successfully';
        
        COMMIT TRANSACTION;
    END TRY
    BEGIN CATCH
        ROLLBACK TRANSACTION;
        SET @Success = 0;
        SET @Message = ERROR_MESSAGE();
    END CATCH
END
GO

-- =============================================
-- Procedure: sp_TimingPause
-- Description: Pause timing for a job step
-- =============================================
CREATE OR ALTER PROCEDURE [dbo].[sp_TimingPause]
    @JobId INT,
    @UserId INT,
    @Status VARCHAR(24),
    @Notes VARCHAR(500) = NULL,
    @Success BIT OUTPUT,
    @Message VARCHAR(500) OUTPUT
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        BEGIN TRANSACTION;
        
        -- Get active session
        DECLARE @SessionId UNIQUEIDENTIFIER, @CurrentState VARCHAR(20);
        
        SELECT @SessionId = SessionId, @CurrentState = CurrentState
        FROM JobStepActiveSession 
        WHERE JobId = @JobId AND UserId = @UserId AND Status = @Status AND IsActive = 1;
        
        IF @SessionId IS NULL
        BEGIN
            SET @Success = 0;
            SET @Message = 'No active session found';
            ROLLBACK TRANSACTION;
            RETURN;
        END
        
        IF @CurrentState != 'PLAYING'
        BEGIN
            SET @Success = 0;
            SET @Message = 'Cannot pause - session is not currently playing';
            ROLLBACK TRANSACTION;
            RETURN;
        END
        
        -- Update session state
        UPDATE JobStepActiveSession 
        SET CurrentState = 'PAUSED', 
            LastEventTime = GETDATE(),
            DateModified = GETDATE()
        WHERE SessionId = @SessionId;
        
        -- Create timing event
        INSERT INTO JobStepTimingEvent (JobId, UserId, Status, EventType, EventTime, SessionId, Notes)
        VALUES (@JobId, @UserId, @Status, 'PAUSE', GETDATE(), @SessionId, @Notes);
        
        SET @Success = 1;
        SET @Message = 'Timing paused successfully';
        
        COMMIT TRANSACTION;
    END TRY
    BEGIN CATCH
        ROLLBACK TRANSACTION;
        SET @Success = 0;
        SET @Message = ERROR_MESSAGE();
    END CATCH
END
GO

-- =============================================
-- Procedure: sp_TimingResume
-- Description: Resume timing for a job step
-- =============================================
CREATE OR ALTER PROCEDURE [dbo].[sp_TimingResume]
    @JobId INT,
    @UserId INT,
    @Status VARCHAR(24),
    @Notes VARCHAR(500) = NULL,
    @Success BIT OUTPUT,
    @Message VARCHAR(500) OUTPUT
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        BEGIN TRANSACTION;
        
        -- Get active session
        DECLARE @SessionId UNIQUEIDENTIFIER, @CurrentState VARCHAR(20), @LastEventTime DATETIME;
        
        SELECT @SessionId = SessionId, @CurrentState = CurrentState, @LastEventTime = LastEventTime
        FROM JobStepActiveSession 
        WHERE JobId = @JobId AND UserId = @UserId AND Status = @Status AND IsActive = 1;
        
        IF @SessionId IS NULL
        BEGIN
            SET @Success = 0;
            SET @Message = 'No active session found';
            ROLLBACK TRANSACTION;
            RETURN;
        END
        
        IF @CurrentState != 'PAUSED'
        BEGIN
            SET @Success = 0;
            SET @Message = 'Cannot resume - session is not currently paused';
            ROLLBACK TRANSACTION;
            RETURN;
        END
        
        -- Calculate paused duration and add to total
        DECLARE @PausedDuration INT = DATEDIFF(SECOND, @LastEventTime, GETDATE());
        
        UPDATE JobStepActiveSession 
        SET CurrentState = 'PLAYING',
            TotalPausedDuration = TotalPausedDuration + @PausedDuration,
            LastEventTime = GETDATE(),
            DateModified = GETDATE()
        WHERE SessionId = @SessionId;
        
        -- Create timing event
        INSERT INTO JobStepTimingEvent (JobId, UserId, Status, EventType, EventTime, SessionId, Notes)
        VALUES (@JobId, @UserId, @Status, 'RESUME', GETDATE(), @SessionId, @Notes);
        
        SET @Success = 1;
        SET @Message = 'Timing resumed successfully';
        
        COMMIT TRANSACTION;
    END TRY
    BEGIN CATCH
        ROLLBACK TRANSACTION;
        SET @Success = 0;
        SET @Message = ERROR_MESSAGE();
    END CATCH
END
GO

-- =============================================
-- Procedure: sp_TimingFinish
-- Description: Finish timing for a job step and create summary
-- =============================================
CREATE OR ALTER PROCEDURE [dbo].[sp_TimingFinish]
    @JobId INT,
    @UserId INT,
    @Status VARCHAR(24),
    @Notes VARCHAR(500) = NULL,
    @Success BIT OUTPUT,
    @Message VARCHAR(500) OUTPUT
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        BEGIN TRANSACTION;
        
        -- Get active session
        DECLARE @SessionId UNIQUEIDENTIFIER, @CurrentState VARCHAR(20), @LastEventTime DATETIME, 
                @StartTime DATETIME, @TotalPausedDuration INT;
        
        SELECT @SessionId = SessionId, @CurrentState = CurrentState, @LastEventTime = LastEventTime,
               @StartTime = StartTime, @TotalPausedDuration = TotalPausedDuration
        FROM JobStepActiveSession 
        WHERE JobId = @JobId AND UserId = @UserId AND Status = @Status AND IsActive = 1;
        
        IF @SessionId IS NULL
        BEGIN
            SET @Success = 0;
            SET @Message = 'No active session found';
            ROLLBACK TRANSACTION;
            RETURN;
        END
        
        IF @CurrentState = 'FINISHED'
        BEGIN
            SET @Success = 0;
            SET @Message = 'Session is already finished';
            ROLLBACK TRANSACTION;
            RETURN;
        END
        
        -- If currently paused, add the paused time
        IF @CurrentState = 'PAUSED'
        BEGIN
            DECLARE @FinalPausedDuration INT = DATEDIFF(SECOND, @LastEventTime, GETDATE());
            SET @TotalPausedDuration = @TotalPausedDuration + @FinalPausedDuration;
        END
        
        -- Calculate total working duration
        DECLARE @TotalElapsed INT = DATEDIFF(SECOND, @StartTime, GETDATE());
        DECLARE @WorkingDuration INT = @TotalElapsed - @TotalPausedDuration;
        
        -- Update session to finished
        UPDATE JobStepActiveSession 
        SET CurrentState = 'FINISHED',
            IsActive = 0,
            TotalPausedDuration = @TotalPausedDuration,
            LastEventTime = GETDATE(),
            DateModified = GETDATE()
        WHERE SessionId = @SessionId;
        
        -- Create timing event
        INSERT INTO JobStepTimingEvent (JobId, UserId, Status, EventType, EventTime, SessionId, Notes)
        VALUES (@JobId, @UserId, @Status, 'FINISH', GETDATE(), @SessionId, @Notes);
        
        -- Count events for this session
        DECLARE @EventCount INT;
        SELECT @EventCount = COUNT(*) FROM JobStepTimingEvent WHERE SessionId = @SessionId;
        
        -- Create summary record
        INSERT INTO JobStepTimingSummary (JobId, UserId, Status, SessionId, TotalDuration, TotalPausedTime, 
                                        SessionStart, SessionEnd, EventCount, IsCompleted)
        VALUES (@JobId, @UserId, @Status, @SessionId, @WorkingDuration, @TotalPausedDuration,
                @StartTime, GETDATE(), @EventCount, 1);
        
        SET @Success = 1;
        SET @Message = 'Timing completed successfully';
        
        COMMIT TRANSACTION;
    END TRY
    BEGIN CATCH
        ROLLBACK TRANSACTION;
        SET @Success = 0;
        SET @Message = ERROR_MESSAGE();
    END CATCH
END
GO

-- =============================================
-- Procedure: sp_GetTimingButtonState
-- Description: Get current button state for a job/user/status
-- =============================================
CREATE OR ALTER PROCEDURE [dbo].[sp_GetTimingButtonState]
    @JobId INT,
    @UserId INT,
    @Status VARCHAR(24)
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT 
        SessionId,
        CurrentState,
        CASE 
            WHEN CurrentState = 'FINISHED' THEN 0
            ELSE DATEDIFF(SECOND, StartTime, GETDATE()) - TotalPausedDuration
        END AS CurrentWorkingSeconds,
        StartTime,
        LastEventTime,
        TotalPausedDuration
    FROM JobStepActiveSession 
    WHERE JobId = @JobId AND UserId = @UserId AND Status = @Status AND IsActive = 1;
END
GO

-- =============================================
-- Procedure: sp_TimingCleanupAbandonedSessions
-- Description: Auto-finish sessions that have been inactive too long
-- =============================================
CREATE OR ALTER PROCEDURE [dbo].[sp_TimingCleanupAbandonedSessions]
    @MaxHours INT = 24
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @CutoffTime DATETIME = DATEADD(HOUR, -@MaxHours, GETDATE());
    DECLARE @CleanedCount INT = 0;

    BEGIN TRY
        BEGIN TRANSACTION;

        -- Find abandoned sessions
        UPDATE JobStepActiveSession
        SET IsActive = 0,
            CurrentState = 'FINISHED',
            DateModified = GETDATE()
        WHERE IsActive = 1
        AND LastEventTime < @CutoffTime;

        SET @CleanedCount = @@ROWCOUNT;

        -- Create finish events for abandoned sessions
        INSERT INTO JobStepTimingEvent (JobId, UserId, Status, EventType, EventTime, SessionId, Notes)
        SELECT JobId, UserId, Status, 'FINISH', GETDATE(), SessionId, 'Auto-finished due to abandonment'
        FROM JobStepActiveSession
        WHERE IsActive = 0
        AND CurrentState = 'FINISHED'
        AND DateModified >= DATEADD(MINUTE, -1, GETDATE()); -- Just updated

        COMMIT TRANSACTION;

        SELECT @CleanedCount AS CleanedSessionCount;
    END TRY
    BEGIN CATCH
        ROLLBACK TRANSACTION;
        THROW;
    END CATCH
END
GO

-- =============================================
-- Procedure: sp_TimingAutoPauseInactive
-- Description: Auto-pause sessions that have been playing too long without activity
-- =============================================
CREATE OR ALTER PROCEDURE [dbo].[sp_TimingAutoPauseInactive]
    @InactiveMinutes INT = 30
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @CutoffTime DATETIME = DATEADD(MINUTE, -@InactiveMinutes, GETDATE());
    DECLARE @PausedCount INT = 0;

    BEGIN TRY
        BEGIN TRANSACTION;

        -- Find inactive playing sessions
        UPDATE JobStepActiveSession
        SET CurrentState = 'PAUSED',
            DateModified = GETDATE()
        WHERE IsActive = 1
        AND CurrentState = 'PLAYING'
        AND LastEventTime < @CutoffTime;

        SET @PausedCount = @@ROWCOUNT;

        -- Create pause events for auto-paused sessions
        INSERT INTO JobStepTimingEvent (JobId, UserId, Status, EventType, EventTime, SessionId, Notes)
        SELECT JobId, UserId, Status, 'PAUSE', GETDATE(), SessionId, 'Auto-paused due to inactivity'
        FROM JobStepActiveSession
        WHERE IsActive = 1
        AND CurrentState = 'PAUSED'
        AND DateModified >= DATEADD(MINUTE, -1, GETDATE()); -- Just updated

        COMMIT TRANSACTION;

        SELECT @PausedCount AS PausedSessionCount;
    END TRY
    BEGIN CATCH
        ROLLBACK TRANSACTION;
        THROW;
    END CATCH
END
GO

-- =============================================
-- Procedure: sp_GetTimingReport
-- Description: Get timing statistics for reporting
-- =============================================
CREATE OR ALTER PROCEDURE [dbo].[sp_GetTimingReport]
    @FromDate DATETIME,
    @ToDate DATETIME,
    @UserId INT = NULL,
    @Status VARCHAR(24) = NULL
AS
BEGIN
    SET NOCOUNT ON;

    SELECT
        s.Status,
        COUNT(*) AS SessionCount,
        AVG(s.TotalDuration) AS AvgDurationSeconds,
        MIN(s.TotalDuration) AS MinDurationSeconds,
        MAX(s.TotalDuration) AS MaxDurationSeconds,
        SUM(s.TotalDuration) AS TotalDurationSeconds,
        AVG(s.TotalPausedTime) AS AvgPausedSeconds,
        AVG(s.EventCount) AS AvgEventCount,
        COUNT(CASE WHEN s.IsCompleted = 1 THEN 1 END) AS CompletedCount,
        COUNT(CASE WHEN s.IsCompleted = 0 THEN 1 END) AS IncompleteCount
    FROM JobStepTimingSummary s
    WHERE s.SessionStart >= @FromDate
    AND s.SessionStart <= @ToDate
    AND (@UserId IS NULL OR s.UserId = @UserId)
    AND (@Status IS NULL OR s.Status = @Status)
    GROUP BY s.Status
    ORDER BY s.Status;
END
GO

PRINT 'Timing stored procedures created successfully!'
PRINT 'Available procedures:'
PRINT '  - sp_TimingStart: Start timing session'
PRINT '  - sp_TimingPause: Pause timing session'
PRINT '  - sp_TimingResume: Resume timing session'
PRINT '  - sp_TimingFinish: Finish timing session and create summary'
PRINT '  - sp_GetTimingButtonState: Get current button state'
PRINT '  - sp_TimingCleanupAbandonedSessions: Auto-finish old sessions'
PRINT '  - sp_TimingAutoPauseInactive: Auto-pause inactive sessions'
PRINT '  - sp_GetTimingReport: Get timing statistics for reporting'
