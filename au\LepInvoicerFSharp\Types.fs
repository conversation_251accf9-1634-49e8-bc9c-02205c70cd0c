namespace LepInvoicerFSharp.Types

open System
open FsToolkit.ErrorHandling

// ============================================================================
// CORE DOMAIN TYPES - Functional F# with popular libraries
// ============================================================================

// Using FsToolkit.ErrorHandling for Result and Option types
// No need to redefine Result - use built-in Result<'T, 'Error>

/// Order status enumeration
type OrderStatus = 
    | Pending
    | InProgress  
    | Completed
    | Cancelled

/// Payment status enumeration
type PaymentStatus =
    | Unpaid
    | PartiallyPaid
    | FullyPaid
    | Refunded

/// Invoice status enumeration  
type InvoiceStatus =
    | NotInvoiced
    | Invoiced
    | Failed
    | CustomerNotFound

/// Credit type enumeration
type CreditType =
    | Credit        // "C"
    | Miscellaneous // "M" 
    | CreditInvoice // "CI"
    | Refund        // "S"

/// Customer information
type Customer = {
    Id: int
    Name: string
    Username: string
    Email: string option
}

/// Job information
type Job = {
    Id: int
    OrderId: int
    Price: decimal option
    Description: string
    IsEnabled: bool
}

/// Order entity (immutable record)
type Order = {
    Id: int
    OrderNr: string
    Customer: Customer
    Jobs: Job list
    Status: OrderStatus
    PaymentStatus: PaymentStatus
    InvoiceStatus: InvoiceStatus
    FinishDate: DateTime option
    SubmissionDate: DateTime option
    PurchaseOrder: string option
    PromotionBenefit: decimal
    PickUpCharge: decimal
    GST: decimal
}

/// Order credit entity
type OrderCredit = {
    Id: int
    Type: CreditType
    Amount: decimal
    Description: string option
    OrderId: int option
    CustomerId: int
    IsInvoiced: bool
    DateCreated: DateTime
}

/// Processing result statistics
type ProcessingStats = {
    OrdersProcessed: int
    OrdersSuccessful: int
    OrdersFailed: int
    CreditsProcessed: int
    RefundsProcessed: int
    ElapsedTime: TimeSpan
    Errors: string list
}

/// Configuration settings
type InvoicerConfig = {
    ConnectionString: string
    InvoiceBatchSize: int
    RefundBatchSize: int
    CreateOrderInvoice: bool
    CreateRefundInvoice: bool
    CreatePdfInvoice: bool
    TestMode: bool
    IgnoreCustomers: string list
    MinimumFinishDate: DateTime
    MYOBConfig: MYOBConfig
}

and MYOBConfig = {
    DeveloperKey: string
    DeveloperSecret: string
    CompanyFileName: string
    ConfirmationUrl: string
}

/// Database operation result
type DbResult<'T> = Result<'T, string>

/// MYOB operation result  
type MYOBResult<'T> = Result<'T, string>

/// Log level enumeration
type LogLevel =
    | Debug
    | Info  
    | Warning
    | Error

/// Log entry
type LogEntry = {
    Level: LogLevel
    Message: string
    Timestamp: DateTime
    Exception: exn option
}

// ============================================================================
// FUNCTIONAL COMPOSITION OPERATORS - Using FsToolkit.ErrorHandling
// ============================================================================

// FsToolkit.ErrorHandling provides:
// - result { } computation expression
// - option { } computation expression
// - asyncResult { } computation expression
// - Result.map, Result.bind, etc.
// - List.traverseResultM, List.sequenceResultM
// - And many more functional utilities

// Custom operators for enhanced readability
module Operators =
    /// Function composition (F# built-in)
    let (<<) = (<<)

    /// Reverse function composition (F# built-in)
    let (>>) = (>>)

    /// Pipe operator (F# built-in)
    let (|>) = (|>)

// ============================================================================
// DOMAIN LOGIC FUNCTIONS - Pure functions
// ============================================================================

module OrderLogic =
    /// Calculate price of jobs only (pure function)
    let calculateJobsPrice (jobs: Job list) : decimal option =
        let validPrices = 
            jobs 
            |> List.choose (fun job -> job.Price)
        
        if List.isEmpty validPrices then
            None
        else
            Some (List.sum validPrices)
    
    /// Calculate total order price including GST (pure function)
    let calculateTotalPrice (order: Order) : decimal option =
        calculateJobsPrice order.Jobs
        |> Option.map (fun jobsPrice ->
            let adjustedPrice = jobsPrice - order.PromotionBenefit + order.PickUpCharge
            adjustedPrice + (adjustedPrice * order.GST / 100m))
    
    /// Check if order has valid price (pure function)
    let hasValidPrice (order: Order) : bool =
        calculateJobsPrice order.Jobs
        |> Option.map (fun price -> price > 0m)
        |> Option.defaultValue false
    
    /// Check if order is ready for invoicing (pure function)
    let isReadyForInvoicing (config: InvoicerConfig) (order: Order) : bool =
        let hasFinishDate = order.FinishDate.IsSome
        let isAfterMinDate =
            order.FinishDate
            |> Option.map (fun date -> date >= config.MinimumFinishDate)
            |> Option.defaultValue false
        let isNotIgnored = not (List.contains order.Customer.Name config.IgnoreCustomers)
        let isNotInvoiced = order.InvoiceStatus = NotInvoiced
        let hasPrice = hasValidPrice order
        
        hasFinishDate && isAfterMinDate && isNotIgnored && isNotInvoiced && hasPrice

module CreditLogic =
    /// Check if credit has valid amount (pure function)
    let hasValidAmount (credit: OrderCredit) : bool =
        credit.Amount > 0m
    
    /// Check if credit is ready for invoicing (pure function)
    let isReadyForInvoicing (config: InvoicerConfig) (credit: OrderCredit) : bool =
        let isNotInvoiced = not credit.IsInvoiced
        let hasValidAmount = hasValidAmount credit
        let isValidType = 
            match credit.Type with
            | Credit | Miscellaneous | CreditInvoice | Refund -> true
        
        isNotInvoiced && hasValidAmount && isValidType
