<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2"
                   namespace="lep.timing.entities"
                   assembly="lep"
                   auto-import="true"
                   default-cascade="none">

    <class name="JobStepTimingEvent" table="JobStepTimingEvent">
        <cache usage="read-write" />

        <id name="Id" type="Int32" unsaved-value="0">
            <generator class="identity" />
        </id>

        <property name="JobId" column="JobId" type="Int32" not-null="true" />
        <property name="UserId" column="UserId" type="Int32" not-null="true" />
        <property name="Status" column="Status" type="String" length="24" not-null="true" />
        <property name="EventType" column="EventType" type="String" length="20" not-null="true" />
        <property name="EventTime" column="EventTime" type="DateTime" not-null="true" />
        <property name="SessionId" column="SessionId" type="Guid" not-null="true" />
        <property name="Notes" column="Notes" type="String" length="500" />
        <property name="IPAddress" column="IPAddress" type="String" length="45" />
        <property name="UserAgent" column="UserAgent" type="String" length="500" />
        <property name="InstructionsViewed" column="InstructionsViewed" type="Boolean" not-null="true" />
        <property name="InstructionsViewedAt" column="InstructionsViewedAt" type="DateTime" />
        <property name="WorkstationId" column="WorkstationId" type="String" length="50" />
        <property name="QualityChecksPassed" column="QualityChecksPassed" type="Boolean" />
        <property name="CompletionNotes" column="CompletionNotes" type="String" length="1000" />
        <property name="DateCreated" column="DateCreated" type="DateTime" not-null="true" />

        <!-- Navigation properties -->
        <many-to-one name="Job" column="JobId" class="lep.job.impl.Job, lep" insert="false" update="false" />
        <many-to-one name="User" column="UserId" class="lep.user.IUser, lep" insert="false" update="false" />
    </class>
</hibernate-mapping>
