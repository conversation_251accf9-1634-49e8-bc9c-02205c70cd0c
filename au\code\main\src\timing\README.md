# Production Step Time Tracking System

A comprehensive time tracking system for production steps in the LEPCore job management system. This system provides Play, Pause, Resume, and Finish buttons for each production step, with complete audit trails and production instructions.

## Features

- **Four-Button Interface**: Play, Pause, Resume, Finish for each production step
- **Complete Audit Trail**: Every button click is recorded with timestamp, user, and context
- **Production Instructions**: Popup instructions with safety notes, tools, and quality checks
- **Real-time Timing**: Live timer displays with automatic updates
- **Session Management**: Handles multiple concurrent timing sessions per user
- **Quality Control**: Optional quality check confirmations before finishing
- **Reporting**: Comprehensive timing reports and analytics
- **Auto-cleanup**: Automatic cleanup of abandoned sessions

## Architecture

### Backend Components

#### Entities
- `JobStepTimingEvent` - Records every button click
- `JobStepActiveSession` - Tracks current timing sessions
- `JobStepTimingSummary` - Aggregated timing data for reporting

#### Application Services
- `ITimingApplication` - Core timing operations
- `IProductionInstructionsService` - File-based instructions management

#### Controllers
- `JobTimingController` - API endpoints for timing operations
- `ProductionInstructionsController` - API endpoints for instructions

### Frontend Components

#### JavaScript Classes
- `TimingManager` - Main timing logic and API communication
- `InstructionsModal` - Production instructions popup
- `JobBoardTimingIntegration` - Integration with existing job boards

#### UI Components
- Timing control buttons (Play, Pause, Resume, Finish)
- Live timer displays
- Instructions modal with safety notes and quality checks
- Emergency stop all sessions button

## Installation

### 1. Database Setup

Execute the SQL script to create timing tables:

```sql
-- Create the timing tables
EXEC au\sql\timing_tables.sql
```

**Note**: Production instructions are now stored as YAML + Markdown files, not in the database.

### 2. Backend Integration

The timing system is located in `au\code\main\src\timing\` and includes:

- Entity classes with NHibernate mappings
- Application service implementations
- DTOs and exception classes
- File-based instructions service

Controllers are in `au\LepCore\BackEnd\Controllers\`:
- `JobTimingController.cs`
- `ProductionInstructionsController.cs`

Production instructions are stored in `au\LepCore\Config\ProductionInstructions\` as YAML + Markdown files.

### 3. Frontend Integration

Include the JavaScript files in your job board pages:

```html
<!-- Core timing functionality -->
<script src="/app/staff/jobboards/timing/timing-manager.js"></script>
<script src="/app/staff/jobboards/timing/instructions-modal.js"></script>
<script src="/app/staff/jobboards/timing/timing-controls.js"></script>

<!-- Integration with existing job boards -->
<script src="/app/staff/jobboards/timing/timing-integration.js"></script>
```

### 4. Job Board Integration

To add timing controls to existing job boards:

```javascript
// Add timing column to DataTable
const columns = [
    // ... existing columns
    {
        data: null,
        name: 'timing',
        title: 'Timing',
        orderable: false,
        render: function(data, type, row) {
            return createTimingControls(row.id, row.currentStatus);
        }
    }
];
```

## Usage

### Basic Timing Operations

1. **Start Timing**: Click Play button → View instructions → Confirm → Timer starts
2. **Pause Timing**: Click Pause button → Timer pauses, duration preserved
3. **Resume Timing**: Click Resume button → Timer continues from where it left off
4. **Finish Timing**: Click Finish button → Complete quality checks → Session ends

### Production Instructions

Instructions are stored as YAML + Markdown files and shown when starting timing for a production step:

- **Safety Notes**: Important safety information with visual warnings
- **Required Tools**: List of tools and equipment needed
- **Step-by-Step Instructions**: Detailed process instructions with rich formatting
- **Quality Checks**: Quality control requirements and standards
- **Estimated Duration**: Expected time for completion

#### YAML + Markdown Format

Each instruction file follows this structure:

```yaml
---
status: StatusName
title: Human Readable Title
estimatedDuration: 30  # minutes
isActive: true
requiredTools:
  - Tool 1
  - Tool 2
qualityChecks:
  - Check 1
  - Check 2
---

# Markdown Content Here

Your instruction content in **Markdown** format with:
- Rich formatting (bold, italic, headers)
- Tables for structured data
- Task lists with checkboxes
- Code blocks and syntax highlighting
- Emojis and visual elements 🎯
- Blockquotes for important notes

> ⚠️ **Safety Warning**: Critical safety information stands out
```

#### Available Instructions

Current instruction files include:
- `Filling.md` - Material filling processes
- `Cut.md` - Cutting and trimming operations
- `Folded.md` - Folding and creasing procedures
- `Packed.md` - Packing and shipping preparation
- `QC.md` - Quality control inspection
- `Setup.md` - Job setup and preparation
- `Printing.md` - Digital and offset printing
- `Finished.md` - Final finishing operations

#### Editing Instructions

To modify production instructions:

1. **Edit the `.md` files** directly in `au\LepCore\Config\ProductionInstructions\`
2. **Use any text editor** or Markdown editor (VS Code, Typora, etc.)
3. **Changes take effect immediately** (files are cached for performance)
4. **Clear cache** if needed using the service's `ClearCache()` method
5. **Version control** the files like any other code

**Benefits of file-based approach:**
- ✅ Easy to edit and maintain
- ✅ Version controlled with the codebase
- ✅ No database dependency
- ✅ Rich formatting with Markdown
- ✅ Fast loading (cached in memory)
- ✅ Can be edited by non-developers

### Emergency Controls

- **Stop All Sessions**: Emergency button to stop all active timing sessions
- **Refresh States**: Reload timing states for all controls

## Configuration

Timing behavior is controlled by constants in the `TimingConfiguration` class:

```csharp
public static class TimingConfiguration
{
    // Maximum session duration before auto-timeout
    public const int MAX_SESSION_HOURS = 8;

    // Auto-pause after inactivity
    public const int AUTO_PAUSE_MINUTES = 30;

    // Require viewing instructions before starting
    public const bool REQUIRE_INSTRUCTIONS = true;

    // Require quality check confirmation on finish
    public const bool REQUIRE_QUALITY_CHECKS = false;

    // Enable real-time updates
    public const bool ENABLE_REALTIME_UPDATES = true;

    // Other configuration constants...
}
```

To modify timing behavior, update the constants in `au\code\main\src\timing\TimingConfiguration.cs` and recompile.

## API Endpoints

### Timing Operations
- `POST /api/jobtiming/play` - Start timing
- `POST /api/jobtiming/pause` - Pause timing
- `POST /api/jobtiming/resume` - Resume timing
- `POST /api/jobtiming/finish` - Finish timing
- `GET /api/jobtiming/state/{jobId}/{status}` - Get button state
- `GET /api/jobtiming/user/active` - Get user's active sessions

### Instructions Management
- `GET /api/productioninstructions/{status}` - Get instructions for status
- `POST /api/productioninstructions` - Create instructions
- `PUT /api/productioninstructions/{id}` - Update instructions
- `DELETE /api/productioninstructions/{id}` - Delete instructions

## Database Schema

### JobStepTimingEvent
Records every button click with full audit information.

### JobStepActiveSession
Tracks currently active timing sessions with state management.

### JobStepTimingSummary
Aggregated timing data for reporting and analytics.

**Note**: Production instructions are stored as YAML + Markdown files, not in database tables.

## Security

- All API endpoints require authentication
- User context is captured for all timing events
- IP address and user agent are logged for audit
- Workstation ID tracking for multi-device scenarios

## Reporting

The system provides comprehensive reporting capabilities:

- Individual job timing history
- User productivity reports
- Average time by production step
- Efficiency analysis
- Quality control tracking

## Maintenance

### Cleanup Tasks

The system includes automatic cleanup:

- **Abandoned Sessions**: Sessions inactive for more than max hours are auto-finished
- **Auto-pause**: Sessions with no activity are automatically paused
- **Data Archival**: Old timing data can be archived for performance

### Monitoring

Monitor the system through:

- Active session counts
- Average session durations
- Error rates in timing operations
- User adoption metrics

## Troubleshooting

### Common Issues

1. **Timing buttons not appearing**: Check JavaScript console for errors, verify timing-integration.js is loaded
2. **Instructions not loading**: Verify ProductionInstructionsController is accessible and instructions exist
3. **Authentication errors**: Check auth token configuration in timing manager
4. **Timer not updating**: Verify timing intervals are starting correctly

### Debug Mode

Enable debug logging by setting:
```javascript
window.timingManager.config.debug = true;
```

## Future Enhancements

Potential improvements:
- Real-time notifications using SignalR
- Mobile app integration
- Advanced analytics dashboard
- Integration with payroll systems
- Barcode scanning for job identification
- Voice commands for hands-free operation

## Support

For issues or questions:
1. Check the browser console for JavaScript errors
2. Verify database connectivity and table structure
3. Check API endpoint accessibility
4. Review authentication configuration
5. Consult the timing event logs for audit trail
