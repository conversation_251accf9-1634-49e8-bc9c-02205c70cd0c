using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using YamlDotNet.Serialization;
using Markdig;
using Serilog;
using lep.timing.dto;

namespace lep.timing.impl
{
    public class MarkdownInstructionsService : IProductionInstructionsService
    {
        private readonly string _instructionsPath;
        private readonly IDeserializer _yamlDeserializer;
        private readonly MarkdownPipeline _markdownPipeline;
        private readonly Dictionary<string, ProductionInstructionsDto> _cache;
        private readonly object _cacheLock = new object();

        public MarkdownInstructionsService( )
        {
            _instructionsPath = LepGlobal.Instance.ProductionInstructionsPath;

            _yamlDeserializer = new DeserializerBuilder()
                .IgnoreUnmatchedProperties()
                .Build();

            _markdownPipeline = new MarkdownPipelineBuilder()
                .UseAdvancedExtensions() // Tables, task lists, emojis, etc.
                .UseEmojiAndSmiley()
                .UseTaskLists()
                .UsePipeTables()
                .UseGenericAttributes()
                .Build();

            _cache = new Dictionary<string, ProductionInstructionsDto>();

            // Ensure directory exists
            if (!Directory.Exists(_instructionsPath))
            {
                Directory.CreateDirectory(_instructionsPath);
                Log.Information("Created production instructions directory: {Path}", _instructionsPath);
            }
        }

        public async Task<ProductionInstructionsDto> GetInstructionsForJobStep(int jobId, string status)
        {
            // For now, we don't have job-specific instructions, just return general ones
            return await GetInstructionsByStatus(status).ContinueWith(t => t.Result?.FirstOrDefault());
        }

        public async Task<List<ProductionInstructionsDto>> GetInstructionsByStatus(string status)
        {
            var instruction = await GetInstructions(status);
            return instruction != null ? new List<ProductionInstructionsDto> { instruction } : new List<ProductionInstructionsDto>();
        }

        public async Task<ProductionInstructionsDto> GetInstructions(string status)
        {
            if (string.IsNullOrWhiteSpace(status))
                return null;

            // Check cache first
            var cacheKey = $"{status}_checkpoints";
            lock (_cacheLock)
            {
                if (_cache.TryGetValue(cacheKey, out var cachedInstruction))
                    return cachedInstruction;
            }

            try
            {
                // Try checkpoints first (boss's version)
                var checkpointsPath = Path.Combine(_instructionsPath, $"{status}-Checkpoints.md");
                if (File.Exists(checkpointsPath))
                {
                    var checkpointsContent = await File.ReadAllTextAsync(checkpointsPath);
                    var checkpoints = ParseMarkdownWithYaml(checkpointsContent, status);
                    if (checkpoints != null)
                    {
                        checkpoints.Title = checkpoints.Title + " (Quick Checkpoints)";

                        // Cache the result
                        lock (_cacheLock)
                        {
                            _cache[cacheKey] = checkpoints;
                        }

                        return checkpoints;
                    }
                }

                // Fall back to detailed instructions
                var detailedPath = Path.Combine(_instructionsPath, $"{status}.md");
                if (File.Exists(detailedPath))
                {
                    var detailedContent = await File.ReadAllTextAsync(detailedPath);
                    var detailed = ParseMarkdownWithYaml(detailedContent, status);
                    if (detailed != null)
                    {
                        detailed.Title = detailed.Title + " (Detailed Guide)";

                        // Cache the result
                        lock (_cacheLock)
                        {
                            _cache[cacheKey] = detailed;
                        }

                        return detailed;
                    }
                }

                Log.Debug("No instruction file found for status: {Status}", status);
                return null;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error loading instructions for status: {Status}", status);
                return null;
            }
        }

        public async Task<List<ProductionInstructionsDto>> GetAllInstructions()
        {
            var instructions = new List<ProductionInstructionsDto>();

            try
            {
                if (!Directory.Exists(_instructionsPath))
                    return instructions;

                var files = Directory.GetFiles(_instructionsPath, "*.md");

                foreach (var file in files)
                {
                    var status = Path.GetFileNameWithoutExtension(file);
                    var instruction = await GetInstructions(status);

                    if (instruction != null)
                        instructions.Add(instruction);
                }

                return instructions.OrderBy(i => i.Status).ToList();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error loading all instructions");
                return instructions;
            }
        }

        public Task<ProductionInstructionsDto> CreateInstructions(ProductionInstructionsDto dto, int userId)
        {
            // For file-based system, this would create a new .md file
            throw new NotSupportedException("Creating instructions through API is not supported with file-based storage. Edit the .md files directly.");
        }

        public Task<ProductionInstructionsDto> UpdateInstructions(ProductionInstructionsDto dto, int userId)
        {
            // For file-based system, this would update the .md file
            throw new NotSupportedException("Updating instructions through API is not supported with file-based storage. Edit the .md files directly.");
        }

        public Task DeleteInstructions(int id, int userId)
        {
            // For file-based system, this would delete the .md file
            throw new NotSupportedException("Deleting instructions through API is not supported with file-based storage. Remove the .md files directly.");
        }

        public Task<List<string>> GetAllProductionStatuses()
        {
            try
            {
                if (!Directory.Exists(_instructionsPath))
                    return Task.FromResult(new List<string>());

                var files = Directory.GetFiles(_instructionsPath, "*.md");
                return Task.FromResult(files.Select(f => Path.GetFileNameWithoutExtension(f)).OrderBy(s => s).ToList());
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error getting production statuses");
                return Task.FromResult(new List<string>());
            }
        }

        public async Task<List<ProductionInstructionsDto>> GetInstructionsByJobType(int jobTypeId)
        {
            // For file-based system, we don't support job type specific instructions yet
            return await GetAllInstructions();
        }

        /// <summary>
        /// Gets both checkpoint and detailed instructions for a status
        /// </summary>
        public async Task<(ProductionInstructionsDto checkpoints, ProductionInstructionsDto detailed)> GetBothInstructionTypes(string status)
        {
            if (string.IsNullOrWhiteSpace(status))
                return (null, null);

            ProductionInstructionsDto checkpoints = null;
            ProductionInstructionsDto detailed = null;

            try
            {
                // Get checkpoints (boss's version)
                var checkpointsPath = Path.Combine(_instructionsPath, $"{status}-Checkpoints.md");
                if (File.Exists(checkpointsPath))
                {
                    var checkpointsContent = await File.ReadAllTextAsync(checkpointsPath);
                    checkpoints = ParseMarkdownWithYaml(checkpointsContent, status);
                    if (checkpoints != null)
                        checkpoints.Title = checkpoints.Title + " (Quick Checkpoints)";
                }

                // Get detailed instructions
                var detailedPath = Path.Combine(_instructionsPath, $"{status}.md");
                if (File.Exists(detailedPath))
                {
                    var detailedContent = await File.ReadAllTextAsync(detailedPath);
                    detailed = ParseMarkdownWithYaml(detailedContent, status);
                    if (detailed != null)
                        detailed.Title = detailed.Title + " (Detailed Guide)";
                }

                return (checkpoints, detailed);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error loading both instruction types for status: {Status}", status);
                return (null, null);
            }
        }

        private ProductionInstructionsDto ParseMarkdownWithYaml(string content, string status)
        {
            try
            {
                // Check if content has YAML front matter
                if (!content.StartsWith("---"))
                {
                    // No YAML front matter, treat as pure markdown
                    var htmlFromMarkdown = Markdown.ToHtml(content, _markdownPipeline);
                    return new ProductionInstructionsDto
                    {
                        Status = status,
                        Title = $"{status} Instructions",
                        Instructions = htmlFromMarkdown,
                        IsActive = true
                    };
                }

                // Split YAML front matter from Markdown content
                var parts = content.Substring(3).Split(new[] { "\n---\n", "\r\n---\r\n" }, 2, StringSplitOptions.None);

                if (parts.Length < 2)
                {
                    Log.Warning("Invalid YAML front matter format in instructions for status: {Status}", status);
                    return null;
                }

                var yamlContent = parts[0].Trim();
                var markdownContent = parts[1].Trim();

                // Parse YAML front matter
                var yamlData = _yamlDeserializer.Deserialize<Dictionary<string, object>>(yamlContent) ?? new Dictionary<string, object>();

                // Convert Markdown to HTML
                var htmlContent = Markdown.ToHtml(markdownContent, _markdownPipeline);

                // Create DTO
                return new ProductionInstructionsDto
                {
                    Status = yamlData.GetValueOrDefault("status")?.ToString() ?? status,
                    Title = yamlData.GetValueOrDefault("title")?.ToString() ?? $"{status} Instructions",
                    Instructions = htmlContent,
                    EstimatedDuration = ParseInt(yamlData.GetValueOrDefault("estimatedDuration")),
                    RequiredTools = string.Join(", ", ParseStringList(yamlData.GetValueOrDefault("requiredTools"))),
                    QualityChecks = string.Join(", ", ParseStringList(yamlData.GetValueOrDefault("qualityChecks"))),
                    IsActive = ParseBool(yamlData.GetValueOrDefault("isActive")) ?? true,
                    SafetyNotes = ExtractSafetyNotes(htmlContent)
                };
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error parsing YAML/Markdown for status: {Status}", status);
                return null;
            }
        }

        private int? ParseInt(object value) =>
            value != null && int.TryParse(value.ToString(), out var result) ? result : null;

        private bool? ParseBool(object value) =>
            value != null && bool.TryParse(value.ToString(), out var result) ? result : null;

        private List<string> ParseStringList(object value)
        {
            if (value is IEnumerable<object> list)
                return list.Select(x => x?.ToString()).Where(x => !string.IsNullOrEmpty(x)).ToList();
            return new List<string>();
        }

        private string ExtractSafetyNotes(string htmlContent)
        {
            // Simple extraction of safety-related content
            // Look for sections with safety keywords
            if (htmlContent.Contains("Safety") || htmlContent.Contains("⚠️") || htmlContent.Contains("WARNING"))
            {
                // This is a simple implementation - could be enhanced with proper HTML parsing
                var lines = htmlContent.Split('\n');
                var safetyLines = lines.Where(line =>
                    line.Contains("Safety") ||
                    line.Contains("⚠️") ||
                    line.Contains("WARNING") ||
                    line.Contains("CRITICAL")).ToList();

                if (safetyLines.Any())
                    return string.Join("\n", safetyLines);
            }

            return null;
        }

        public void ClearCache()
        {
            lock (_cacheLock)
            {
                _cache.Clear();
            }
        }
    }
}
