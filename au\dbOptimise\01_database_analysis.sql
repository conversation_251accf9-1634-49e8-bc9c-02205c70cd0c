-- =============================================
-- Database Performance Analysis Script
-- Target: [SRV03].[PRD_AU]
-- Purpose: Comprehensive analysis of database performance
-- Author: Database Optimization Assistant
-- Date: $(date)
-- =============================================

USE [PRD_AU];
GO

-- Set NOCOUNT ON to reduce network traffic
SET NOCOUNT ON;
GO

PRINT '=== DATABASE PERFORMANCE ANALYSIS STARTING ===';
PRINT 'Database: ' + DB_NAME();
PRINT 'Server: ' + @@SERVERNAME;
PRINT 'Analysis Date: ' + CONVERT(VARCHAR(20), GETDATE(), 120);
PRINT '';

-- =============================================
-- 1. MISSING INDEX ANALYSIS
-- =============================================
PRINT '1. ANALYZING MISSING INDEXES...';

SELECT 
    'MISSING_INDEX' AS analysis_type,
    ROUND(avg_total_user_cost * avg_user_impact * (user_seeks + user_scans), 0) AS improvement_measure,
    'CREATE INDEX [IX_' + 
    OBJECT_NAME(mid.object_id, mid.database_id) + '_' + 
    REPLACE(REPLACE(REPLACE(ISNULL(mid.equality_columns, ''), ', ', '_'), '[', ''), ']', '') +
    CASE WHEN mid.inequality_columns IS NOT NULL THEN '_' + REPLACE(REPLACE(REPLACE(mid.inequality_columns, ', ', '_'), '[', ''), ']', '') ELSE '' END +
    '] ON [' + OBJECT_SCHEMA_NAME(mid.object_id, mid.database_id) + '].[' + OBJECT_NAME(mid.object_id, mid.database_id) + ']' AS index_name,
    'CREATE INDEX [IX_' + 
    OBJECT_NAME(mid.object_id, mid.database_id) + '_' + 
    REPLACE(REPLACE(REPLACE(ISNULL(mid.equality_columns, ''), ', ', '_'), '[', ''), ']', '') +
    CASE WHEN mid.inequality_columns IS NOT NULL THEN '_' + REPLACE(REPLACE(REPLACE(mid.inequality_columns, ', ', '_'), '[', ''), ']', '') ELSE '' END +
    '] ON [' + OBJECT_SCHEMA_NAME(mid.object_id, mid.database_id) + '].[' + OBJECT_NAME(mid.object_id, mid.database_id) + '] (' +
    ISNULL(mid.equality_columns, '') +
    CASE WHEN mid.equality_columns IS NOT NULL AND mid.inequality_columns IS NOT NULL THEN ', ' ELSE '' END +
    ISNULL(mid.inequality_columns, '') + ')' +
    CASE WHEN mid.included_columns IS NOT NULL THEN ' INCLUDE (' + mid.included_columns + ')' ELSE '' END +
    ' WITH (FILLFACTOR = 90, ONLINE = ON);' AS create_script,
    OBJECT_SCHEMA_NAME(mid.object_id, mid.database_id) AS schema_name,
    OBJECT_NAME(mid.object_id, mid.database_id) AS table_name,
    mid.equality_columns,
    mid.inequality_columns,
    mid.included_columns,
    migs.user_seeks,
    migs.user_scans,
    migs.avg_total_user_cost,
    migs.avg_user_impact,
    CASE 
        WHEN ROUND(avg_total_user_cost * avg_user_impact * (user_seeks + user_scans), 0) > 100000 THEN 'HIGH'
        WHEN ROUND(avg_total_user_cost * avg_user_impact * (user_seeks + user_scans), 0) > 50000 THEN 'MEDIUM'
        ELSE 'LOW'
    END AS impact_level
FROM sys.dm_db_missing_index_details AS mid
INNER JOIN sys.dm_db_missing_index_groups AS mig ON mid.index_handle = mig.index_handle
INNER JOIN sys.dm_db_missing_index_group_stats AS migs ON mig.index_group_handle = migs.group_handle
WHERE mid.database_id = DB_ID()
    AND ROUND(avg_total_user_cost * avg_user_impact * (user_seeks + user_scans), 0) > 10000
ORDER BY improvement_measure DESC;

PRINT '';

-- =============================================
-- 2. INDEX FRAGMENTATION ANALYSIS
-- =============================================
PRINT '2. ANALYZING INDEX FRAGMENTATION...';

SELECT 
    'INDEX_FRAGMENTATION' AS analysis_type,
    OBJECT_SCHEMA_NAME(ips.object_id) AS schema_name,
    OBJECT_NAME(ips.object_id) AS table_name,
    i.name AS index_name,
    ips.index_type_desc,
    ips.avg_fragmentation_in_percent,
    ips.page_count,
    CASE 
        WHEN ips.avg_fragmentation_in_percent > 30 AND ips.page_count > 1000 THEN 'ALTER INDEX [' + i.name + '] ON [' + OBJECT_SCHEMA_NAME(ips.object_id) + '].[' + OBJECT_NAME(ips.object_id) + '] REBUILD WITH (ONLINE = ON, FILLFACTOR = 90);'
        WHEN ips.avg_fragmentation_in_percent > 10 AND ips.page_count > 1000 THEN 'ALTER INDEX [' + i.name + '] ON [' + OBJECT_SCHEMA_NAME(ips.object_id) + '].[' + OBJECT_NAME(ips.object_id) + '] REORGANIZE;'
        ELSE 'No action required'
    END AS maintenance_script,
    CASE 
        WHEN ips.avg_fragmentation_in_percent > 30 THEN 'HIGH'
        WHEN ips.avg_fragmentation_in_percent > 10 THEN 'MEDIUM'
        ELSE 'LOW'
    END AS impact_level
FROM sys.dm_db_index_physical_stats(DB_ID(), NULL, NULL, NULL, 'LIMITED') AS ips
INNER JOIN sys.indexes AS i ON ips.object_id = i.object_id AND ips.index_id = i.index_id
WHERE ips.avg_fragmentation_in_percent > 10
    AND ips.page_count > 100
    AND i.name IS NOT NULL
ORDER BY ips.avg_fragmentation_in_percent DESC;

PRINT '';

-- =============================================
-- 3. UNUSED INDEX ANALYSIS
-- =============================================
PRINT '3. ANALYZING UNUSED INDEXES...';

SELECT 
    'UNUSED_INDEX' AS analysis_type,
    OBJECT_SCHEMA_NAME(i.object_id) AS schema_name,
    OBJECT_NAME(i.object_id) AS table_name,
    i.name AS index_name,
    i.type_desc AS index_type,
    ISNULL(ius.user_seeks, 0) AS user_seeks,
    ISNULL(ius.user_scans, 0) AS user_scans,
    ISNULL(ius.user_lookups, 0) AS user_lookups,
    ISNULL(ius.user_updates, 0) AS user_updates,
    'DROP INDEX [' + i.name + '] ON [' + OBJECT_SCHEMA_NAME(i.object_id) + '].[' + OBJECT_NAME(i.object_id) + '];' AS drop_script,
    CASE 
        WHEN ISNULL(ius.user_updates, 0) > 1000 AND (ISNULL(ius.user_seeks, 0) + ISNULL(ius.user_scans, 0) + ISNULL(ius.user_lookups, 0)) = 0 THEN 'HIGH'
        WHEN ISNULL(ius.user_updates, 0) > 100 AND (ISNULL(ius.user_seeks, 0) + ISNULL(ius.user_scans, 0) + ISNULL(ius.user_lookups, 0)) = 0 THEN 'MEDIUM'
        ELSE 'LOW'
    END AS impact_level
FROM sys.indexes AS i
LEFT JOIN sys.dm_db_index_usage_stats AS ius ON i.object_id = ius.object_id AND i.index_id = ius.index_id AND ius.database_id = DB_ID()
WHERE i.type_desc IN ('NONCLUSTERED', 'CLUSTERED')
    AND i.is_primary_key = 0
    AND i.is_unique_constraint = 0
    AND OBJECTPROPERTY(i.object_id, 'IsUserTable') = 1
    AND (ISNULL(ius.user_seeks, 0) + ISNULL(ius.user_scans, 0) + ISNULL(ius.user_lookups, 0)) = 0
    AND ISNULL(ius.user_updates, 0) > 0
ORDER BY ius.user_updates DESC;

PRINT '';

-- =============================================
-- 4. TOP EXPENSIVE QUERIES ANALYSIS
-- =============================================
PRINT '4. ANALYZING TOP EXPENSIVE QUERIES...';

-- Check SQL Server version and use appropriate query
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('sys.dm_exec_query_stats') AND name = 'total_worker_time')
BEGIN
    SELECT TOP 20
        'EXPENSIVE_QUERY' AS analysis_type,
        qs.execution_count,
        qs.total_worker_time / 1000 AS total_cpu_time_ms,
        CASE WHEN qs.execution_count > 0 THEN (qs.total_worker_time / qs.execution_count) / 1000 ELSE 0 END AS avg_cpu_time_ms,
        qs.total_logical_reads,
        CASE WHEN qs.execution_count > 0 THEN qs.total_logical_reads / qs.execution_count ELSE 0 END AS avg_logical_reads,
        qs.total_elapsed_time / 1000 AS total_duration_ms,
        CASE WHEN qs.execution_count > 0 THEN (qs.total_elapsed_time / qs.execution_count) / 1000 ELSE 0 END AS avg_duration_ms,
        SUBSTRING(qt.text, (qs.statement_start_offset/2)+1,
            ((CASE qs.statement_end_offset
                WHEN -1 THEN DATALENGTH(qt.text)
                ELSE qs.statement_end_offset
            END - qs.statement_start_offset)/2)+1) AS query_text,
        CASE
            WHEN qs.execution_count > 0 AND ((qs.total_worker_time / qs.execution_count) / 1000 > 1000 OR (qs.total_logical_reads / qs.execution_count) > 10000) THEN 'HIGH'
            WHEN qs.execution_count > 0 AND ((qs.total_worker_time / qs.execution_count) / 1000 > 100 OR (qs.total_logical_reads / qs.execution_count) > 1000) THEN 'MEDIUM'
            ELSE 'LOW'
        END AS impact_level
    FROM sys.dm_exec_query_stats AS qs
    CROSS APPLY sys.dm_exec_sql_text(qs.sql_handle) AS qt
    WHERE qt.dbid = DB_ID()
        AND qs.execution_count > 0
        AND qs.total_worker_time > 0
    ORDER BY qs.total_worker_time DESC;
END
ELSE
BEGIN
    PRINT 'Query stats analysis not available on this SQL Server version';
    SELECT 'EXPENSIVE_QUERY' AS analysis_type, 'Not available' AS message;
END

PRINT '';

-- =============================================
-- 5. TABLE STRUCTURE ANALYSIS
-- =============================================
PRINT '5. ANALYZING TABLE STRUCTURES...';

SELECT 
    'TABLE_STRUCTURE' AS analysis_type,
    t.name AS table_name,
    COUNT(c.column_id) AS column_count,
    SUM(CASE WHEN c.max_length = -1 THEN 1 ELSE 0 END) AS max_length_columns,
    SUM(CASE WHEN c.system_type_id IN (35, 99, 167, 175, 231, 239) AND c.max_length > 1000 THEN 1 ELSE 0 END) AS large_varchar_columns,
    CASE 
        WHEN COUNT(c.column_id) > 50 THEN 'Review table design - excessive column count'
        WHEN SUM(CASE WHEN c.max_length = -1 THEN 1 ELSE 0 END) > 3 THEN 'Review MAX columns usage'
        WHEN SUM(CASE WHEN c.system_type_id IN (35, 99, 167, 175, 231, 239) AND c.max_length > 1000 THEN 1 ELSE 0 END) > 5 THEN 'Review large VARCHAR columns'
        ELSE 'Structure looks good'
    END AS recommendation,
    CASE 
        WHEN COUNT(c.column_id) > 100 OR SUM(CASE WHEN c.max_length = -1 THEN 1 ELSE 0 END) > 5 THEN 'HIGH'
        WHEN COUNT(c.column_id) > 50 OR SUM(CASE WHEN c.max_length = -1 THEN 1 ELSE 0 END) > 2 THEN 'MEDIUM'
        ELSE 'LOW'
    END AS impact_level
FROM sys.tables AS t
INNER JOIN sys.columns AS c ON t.object_id = c.object_id
WHERE t.name IN ('Order', 'Job', 'Runs') OR t.is_ms_shipped = 0
GROUP BY t.name
ORDER BY column_count DESC;

PRINT '';
PRINT '=== DATABASE PERFORMANCE ANALYSIS COMPLETED ===';
PRINT 'Review the results above and execute the generated optimization scripts.';
PRINT 'Remember to test in a non-production environment first!';
