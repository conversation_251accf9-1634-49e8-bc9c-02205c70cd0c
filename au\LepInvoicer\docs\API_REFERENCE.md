# LEP Invoicer API Reference

## 🎯 Overview

This document provides a comprehensive reference for the LEP Invoicer service interfaces and their implementations.

## 🏗️ Service Architecture

### Interface Hierarchy
```
IInvoicerService (Main Orchestrator)
├── IDatabaseService (Data Access)
├── IMYOBService (MYOB Integration)
├── IEmailService (Email Delivery)
├── IPdfService (PDF Generation)
└── IOAuthKeyService (OAuth Management)
```

## 📋 Service Interfaces

### IInvoicerService
**Purpose**: Main orchestration service that coordinates all invoice processing operations.

```csharp
public interface IInvoicerService
{
    /// <summary>
    /// Initialize the invoicer service and all dependencies
    /// </summary>
    Task Initialize();

    /// <summary>
    /// Run the complete invoicing process for orders, credits, and refunds
    /// </summary>
    Task<ProcessingResult> RunInvoicer();
}
```

**Key Methods**:
- `Initialize()`: Sets up database connections, MYOB authentication, and service dependencies
- `RunInvoicer()`: Executes the complete invoicing workflow

---

### IDatabaseService
**Purpose**: Handles all database operations using NHibernate ORM.

```csharp
public interface IDatabaseService
{
    /// <summary>
    /// Initialize database connection and NHibernate session factory
    /// </summary>
    Task Initialize();

    /// <summary>
    /// Get orders that need to be invoiced
    /// </summary>
    Task<List<Order>> GetOrdersToInvoice(int batchSize);

    /// <summary>
    /// Get credits that need to be invoiced
    /// </summary>
    Task<List<OrderCredit>> GetCreditsToInvoice(int batchSize);

    /// <summary>
    /// Get refunds that need to be invoiced
    /// </summary>
    Task<List<OrderCredit>> GetRefundsToInvoice(int batchSize);

    /// <summary>
    /// Mark order as successfully invoiced
    /// </summary>
    Task MarkOrderAsInvoiced(int orderId, string details);

    /// <summary>
    /// Mark order as failed to invoice
    /// </summary>
    Task MarkOrderAsFailed(int orderId, string errorDetails);

    /// <summary>
    /// Mark credit as invoiced
    /// </summary>
    Task MarkCreditAsInvoiced(int creditId);

    /// <summary>
    /// Log processing result to Invoicer2Log table
    /// </summary>
    Task LogProcessingResult(int? orderId, int jobCount, decimal total, 
                           DateTime finishDate, bool success, string details);

    /// <summary>
    /// Clean up old failed invoice attempts
    /// </summary>
    Task CleanupInvoicerLogs();

    /// <summary>
    /// Dispose database resources
    /// </summary>
    void Dispose();
}
```

**Key Features**:
- NHibernate-based data access
- Batch processing support
- Enhanced logging with useful Details column
- Automatic cleanup of old records

---

### IMYOBService
**Purpose**: Manages MYOB AccountRight API integration and OAuth authentication.

```csharp
public interface IMYOBService
{
    /// <summary>
    /// Initialize MYOB service with OAuth authentication
    /// </summary>
    Task Initialize();

    /// <summary>
    /// Create an invoice in MYOB for an order
    /// </summary>
    Task<string> CreateOrderInvoice(Order order);

    /// <summary>
    /// Create a credit invoice in MYOB
    /// </summary>
    Task<string> CreateCreditInvoice(OrderCredit credit);

    /// <summary>
    /// Create a refund invoice in MYOB
    /// </summary>
    Task<string> CreateRefundInvoice(OrderCredit refund);

    /// <summary>
    /// Delete an existing invoice from MYOB
    /// </summary>
    Task DeleteInvoice(string invoiceNumber);
}
```

**Key Features**:
- OAuth 2.0 authentication with token persistence
- Automatic account mapping based on job types
- Rate limiting with API call delays
- Comprehensive error handling

---

### IEmailService
**Purpose**: Handles email delivery of PDF invoices to customers.

```csharp
public interface IEmailService
{
    /// <summary>
    /// Send invoice email with PDF attachment
    /// </summary>
    Task SendInvoiceEmail(string toEmail, string customerName, 
                         string invoiceNumber, string pdfFilePath);
}
```

**Key Features**:
- SMTP-based email delivery
- PDF attachment support
- Customer-specific email addressing
- Professional email templates

---

### IPdfService
**Purpose**: Generates PDF invoices using FastReport templates.

```csharp
public interface IPdfService
{
    /// <summary>
    /// Generate PDF invoice for an order
    /// </summary>
    Task<string> GenerateOrderInvoicePdf(Order order, string invoiceNumber);
}
```

**Key Features**:
- FastReport template integration
- Automatic file organization by date
- Order folder integration
- Error handling for template issues

---

### IOAuthKeyService
**Purpose**: Manages OAuth token persistence and renewal for MYOB authentication.

```csharp
public interface IOAuthKeyService
{
    // This interface is mainly for dependency injection
    // The actual OAuth functionality is provided by the MYOB SDK IOAuthKeyService interface
}
```

**Key Features**:
- Automatic token persistence to `Tokens.json`
- Portable token storage relative to application directory
- Automatic token renewal
- Secure token management

## 🔧 Implementation Details

### Service Registration
Services are registered in `Program.cs` using dependency injection:

```csharp
services.AddScoped<IInvoicerService, InvoicerService>();
services.AddScoped<IMYOBService, MYOBService>();
services.AddScoped<IEmailService, EmailService>();
services.AddScoped<IPdfService, PdfService>();
services.AddScoped<IDatabaseService, DatabaseService>();
// Note: OAuthKeyService is created internally by MYOBService
```

### Configuration Injection
All services receive configuration through dependency injection:

```csharp
public DatabaseService(IOptions<InvoicerConfiguration> config, ILogger<DatabaseService> logger)
{
    _config = config.Value;
    _logger = logger;
}
```

### Error Handling Pattern
All services follow consistent error handling:

```csharp
try
{
    // Service operation
    _logger.LogInformation("Operation completed successfully");
}
catch (Exception ex)
{
    _logger.LogError(ex, "Operation failed: {ErrorMessage}", ex.Message);
    throw;
}
```

## 📊 Data Models

### Order
```csharp
public class Order
{
    public int OrderId { get; set; }
    public string OrderNr { get; set; }
    public Customer Customer { get; set; }
    public DateTime? FinishDate { get; set; }
    public decimal PriceOfJobs { get; set; }
    public string Invoiced2 { get; set; }
    public List<Job> Jobs { get; set; }
    // ... additional properties
}
```

### OrderCredit
```csharp
public class OrderCredit
{
    public int CreditId { get; set; }
    public string Type { get; set; } // C, M, CI, S
    public decimal Amount { get; set; }
    public string Description { get; set; }
    public Order Order { get; set; }
    public Customer Customer { get; set; }
    public string Invoiced { get; set; }
    // ... additional properties
}
```

### ProcessingResult
```csharp
public class ProcessingResult
{
    public int OrdersProcessed { get; set; }
    public int OrdersSuccessful { get; set; }
    public int OrdersFailed { get; set; }
    public int CreditsProcessed { get; set; }
    public int RefundsProcessed { get; set; }
    public TimeSpan ProcessingTime { get; set; }
    public List<string> Errors { get; set; }
}
```

## 🔐 Security Considerations

### OAuth Token Security
- Tokens stored in application directory with restricted permissions
- Automatic token renewal prevents expired token issues
- Secure token transmission using HTTPS

### Database Security
- Connection strings with TrustServerCertificate for internal servers
- Parameterized queries prevent SQL injection
- Minimal required database permissions

### Error Handling Security
- Sensitive information excluded from logs
- Error messages sanitized for external consumption
- Detailed errors logged internally only

## 📈 Performance Characteristics

### Batch Processing
- Configurable batch sizes for optimal performance
- Memory-efficient processing of large datasets
- Progress tracking and logging

### API Rate Limiting
- Built-in delays between MYOB API calls (150ms)
- Respect for MYOB API rate limits
- Automatic retry logic for transient failures

### Database Optimization
- Efficient LINQ queries with proper indexing
- Connection pooling through NHibernate
- Minimal database round trips

## 🔍 Monitoring and Diagnostics

### Logging Levels
- **Information**: Normal operation progress
- **Warning**: Non-critical issues that don't stop processing
- **Error**: Critical errors that stop processing
- **Debug**: Detailed diagnostic information

### Performance Metrics
- Processing times for each batch
- Success/failure rates
- API call response times
- Database query performance

### Health Checks
- Database connectivity
- MYOB API accessibility
- File system permissions
- Configuration validation

---

**LEP Invoicer API Reference - Complete Service Documentation** 📚
