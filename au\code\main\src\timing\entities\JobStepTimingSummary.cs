using System;
using lep;
using lep.job;
using lep.user;

namespace lep.timing.entities
{
    public class JobStepTimingSummary : IEntity
    {
        public virtual int Id { get; set; }
        public virtual int JobId { get; set; }
        public virtual int UserId { get; set; }
        public virtual string Status { get; set; }
        public virtual Guid SessionId { get; set; }
        public virtual int TotalDuration { get; set; }         // Total working time in seconds
        public virtual int TotalPausedTime { get; set; }       // Total paused time in seconds
        public virtual DateTime SessionStart { get; set; }     // First PLAY event
        public virtual DateTime? SessionEnd { get; set; }      // FINISH event (NULL if not finished)
        public virtual int EventCount { get; set; }            // Number of button clicks
        public virtual bool IsCompleted { get; set; }          // True when FINISHED
        public virtual DateTime DateCreated { get; set; }
        public virtual DateTime DateModified { get; set; }

        // Navigation properties
        public virtual IJob Job { get; set; }
        public virtual IUser User { get; set; }

        // Calculated properties
        public virtual TimeSpan TotalWorkingTime
        {
            get { return TimeSpan.FromSeconds(TotalDuration); }
        }

        public virtual TimeSpan TotalPausedTimeSpan
        {
            get { return TimeSpan.FromSeconds(TotalPausedTime); }
        }

        public virtual TimeSpan? TotalSessionTime
        {
            get
            {
                if (SessionEnd.HasValue)
                    return SessionEnd.Value - SessionStart;
                return null;
            }
        }

        public virtual double EfficiencyPercentage
        {
            get
            {
                if (!SessionEnd.HasValue) return 0;
                var totalTime = TotalSessionTime?.TotalSeconds ?? 0;
                if (totalTime == 0) return 0;
                return (TotalDuration / totalTime) * 100;
            }
        }

        public JobStepTimingSummary()
        {
            DateCreated = DateTime.Now;
            DateModified = DateTime.Now;
            IsCompleted = false;
            EventCount = 0;
            TotalDuration = 0;
            TotalPausedTime = 0;
        }
    }
}
