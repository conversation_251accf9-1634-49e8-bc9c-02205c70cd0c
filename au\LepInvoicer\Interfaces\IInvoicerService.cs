namespace LepInvoicer.Interfaces;

/// <summary>
/// Main invoicer service interface for orchestrating the invoicing process
/// </summary>
public interface IInvoicerService
{
	/// <summary>
	/// Run the complete invoicing process
	/// </summary>
	Task<int> RunInvoicer();

	/// <summary>
	/// Resend emails for all successfully invoiced orders after a specific date
	/// </summary>
	Task ResendEmailsForInvoicedOrders(DateTime fromDate);
}
