namespace LepInvoicer.Constants;

/// <summary>
/// Constants for the LEP Invoicer application
/// </summary>
public static class InvoicerConstants
{
    /// <summary>
    /// Database operation constants
    /// </summary>
    public static class Database
    {
        public const string InvoicedStatusSuccess = "Y";
        public const string InvoicedStatusFailed = "F";
        public const string InvoicedStatusCustomerNotFound = "C";
        public const string LogSuccessFlag = "Y";
        public const string LogFailureFlag = "N";
    }

    /// <summary>
    /// Order credit type constants
    /// </summary>
    public static class CreditTypes
    {
        public const string Credit = "C";
        public const string Miscellaneous = "M";
        public const string Refund = "S";
    }

    /// <summary>
    /// MYOB account display IDs for different job types
    /// </summary>
    public static class AccountDisplayIds
    {
        // Offset printing accounts
        public const string OffsetBusinessCards = "4-1004";
        public const string OffsetBrochures = "4-1005";
        public const string OffsetStationery = "4-1006";
        public const string OffsetMagazines = "4-1007";
        public const string OffsetPresentationFolders = "4-1008";
        public const string OffsetOther = "4-1009";

        // Digital printing accounts
        public const string DigitalBusinessCards = "4-2004";
        public const string DigitalBrochures = "4-2005";
        public const string DigitalStationery = "4-2006";
        public const string DigitalMagazines = "4-2007";
        public const string DigitalPresentationFolders = "4-2008";
        public const string DigitalOther = "4-2009";

        // Wide format accounts
        public const string WideFormatAdhesives = "4-3020";
        public const string WideFormatBanners = "4-3021";

        // Other accounts
        public const string DiscountsAllowed = "4-1010";
        public const string FreightRecovered = "8-1050";
    }

    /// <summary>
    /// Template and job category constants
    /// </summary>
    public static class JobCategories
    {
        public const string Stationery = "Stationery";
        public const string PresentationFolders = "Presentation Folders";
        public const string AdhesiveSigns = "Adhesive Signs, Rigid Signs & Stickers";
        public const string Banners = "Banners/Pull Ups";
    }

    /// <summary>
    /// Template name replacements for cleaner invoice descriptions
    /// </summary>
    public static class TemplateNameReplacements
    {
        public const string SameDayDispatch = "same day dispatch";
        public const string SameDayDispatchShort = "SDD";
        public const string NextDayDispatch = "next day dispatch";
        public const string NextDayDispatchShort = "NDD";
    }

    /// <summary>
    /// File and folder constants
    /// </summary>
    public static class Files
    {
        public const string OrderInvoiceTemplate = @"C:\LepData\Labels2\lep-invoice-order.frx";
        public const string RefundInvoiceTemplate = @"c:\LepData\Labels2\lep-invoice-refund.frx";
        public const string ExtraFilesFolder = "Extrafiles";
        public const string OrdersFolder = "orders";
        public const string CustomersFolder = "Customers";
    }

    /// <summary>
    /// Email constants
    /// </summary>
    public static class Email
    {
        public const string InvoiceSubjectTemplate = "Invoice {0}";
        public const string InvoiceBodyTemplate = @"Please find attached your invoice {0} from LEP Colour Printers.<br/>
                                                   Contact <EMAIL> for any queries.";
    }

    /// <summary>
    /// MYOB filter constants
    /// </summary>
    public static class MYOBFilters
    {
        public const string GstTaxCodeFilter = "$filter=Code eq 'GST'";
        public const string FreightAccountFilter = "$filter=Name eq 'Freight recovered'";
        public const string DiscountsAccountFilter = "$filter=Name eq 'Discounts Allowed'";
        public const string CustomerByDisplayIdFilter = "$filter=DisplayID eq '{0}'";
        public const string InvoiceByNumberFilter = "$filter=Number eq '{0}'";
    }

    /// <summary>
    /// Timing constants for API calls
    /// </summary>
    public static class Timing
    {
        public const int ApiCallDelayMs = 150;
    }

    /// <summary>
    /// String length limits
    /// </summary>
    public static class StringLimits
    {
        public const int PurchaseOrderMaxLength = 20;
        public const int InvoiceNumberMaxLength = 50;
        public const int DescriptionMaxLength = 255;
    }

    /// <summary>
    /// SQL query constants
    /// </summary>
    public static class SqlQueries
    {
        public const string CleanupInitialSql = @"
            UPDATE [order] SET invoiced2 = null WHERE invoiced2 = 'F';
            DELETE FROM Invoicer2Log WHERE [Success] = 'N';";

        public const string CleanupInvoicerLogsSql = @"
            WITH cte AS (
                SELECT *,
                       RN = ROW_NUMBER() OVER(PARTITION BY [OrderId] ORDER BY [DateCreated] DESC, [OrderId] DESC)  
                FROM [Invoicer2Log]
            )
            DELETE FROM cte WHERE RN != 1;";

        public const string MarkOrderInvoicedSql = "UPDATE [Order] SET Invoiced2 = 'Y' WHERE Id = {0};";
        
        public const string MarkOrderFailedSql = @"
            UPDATE [Order] SET Invoiced2 = 'F', Invoiced2Details = '{1}' WHERE Id = {0};";

        public const string MarkCreditInvoicedSql = "UPDATE [OrderCredit] SET Invoiced = 'Y' WHERE Id = {0};";

        public const string LogInvoicingResultSql = @"
            INSERT INTO Invoicer2Log ([OrderId], [JobCount], [Total], [FinishDate], [Success], [Details], [DateCreated]) 
            VALUES ({0}, {1}, {2}, '{3}', '{4}', {5}, '{6}');";
    }
}
