### eslint-disable ###
### jshint ignore: start ###

appCore = angular.module('app.core')

# Job Board Popup Controller
appCore.controller 'JobBoardPopupController', [
    '$scope', '$http', '$log', '$modalInstance', 'jobId', 'status', '$sce',
    ($scope, $http, $log, $modalInstance, jobId, status, $sce) ->
        $scope._ = 'JobBoardPopupController'
        
        # Initialize scope variables
        $scope.jobId = jobId
        $scope.status = status
        $scope.loading = true
        $scope.error = null
        $scope.jobData = null
        $scope.checkpointsInstructions = null
        $scope.detailedInstructions = null
        $scope.hasDetailedInstructions = false
        
        # Timing state
        $scope.isActive = false
        $scope.isPaused = false
        $scope.elapsedTime = 0
        $scope.startTime = null
        $scope.pausedTime = 0
        
        # Timer interval
        timerInterval = null
        
        # Load job data and instructions
        $scope.loadData = () ->
            $scope.loading = true
            $scope.error = null
            
            # Load job details
            jobPromise = $http.get("/api/orders/job/#{jobId}")
                .then (response) ->
                    $scope.jobData = response.data
                    $log.debug 'Job data loaded:', $scope.jobData
                .catch (error) ->
                    $log.error 'Error loading job data:', error
                    $scope.error = 'Failed to load job details'
            
            # Load both instruction types
            instructionsPromise = $http.get("/api/production-instructions/#{status}/both")
                .then (response) ->
                    data = response.data
                    if data.success
                        if data.checkpoints
                            $scope.checkpointsInstructions = {
                                title: data.checkpoints.title
                                instructions: $sce.trustAsHtml(data.checkpoints.instructions)
                            }
                        
                        if data.detailed
                            $scope.detailedInstructions = {
                                title: data.detailed.title
                                instructions: $sce.trustAsHtml(data.detailed.instructions)
                            }
                            $scope.hasDetailedInstructions = true
                        
                        $log.debug 'Instructions loaded:', data
                    else
                        $log.warn 'No instructions found for status:', status
                .catch (error) ->
                    $log.error 'Error loading instructions:', error
                    # Don't set error here as instructions are optional
            
            # Wait for both to complete
            Promise.all([jobPromise, instructionsPromise])
                .finally () ->
                    $scope.loading = false
                    $scope.$apply() if !$scope.$$phase
        
        # Timing functions
        $scope.startTiming = () ->
            $scope.isActive = true
            $scope.isPaused = false
            $scope.startTime = new Date()
            $scope.pausedTime = 0
            $scope.elapsedTime = 0
            
            # Start timer
            $scope.startTimer()
            
            # Send start event to backend
            $scope.sendTimingEvent('Start')
            
            $log.debug 'Timing started for job:', jobId, 'status:', status
        
        $scope.pauseTiming = () ->
            $scope.isPaused = true
            $scope.stopTimer()
            
            # Send pause event to backend
            $scope.sendTimingEvent('Pause')
            
            $log.debug 'Timing paused'
        
        $scope.resumeTiming = () ->
            $scope.isPaused = false
            $scope.pausedTime += $scope.elapsedTime
            $scope.startTime = new Date()
            
            # Restart timer
            $scope.startTimer()
            
            # Send resume event to backend
            $scope.sendTimingEvent('Resume')
            
            $log.debug 'Timing resumed'
        
        $scope.finishTiming = () ->
            $scope.isActive = false
            $scope.isPaused = false
            $scope.stopTimer()
            
            # Send finish event to backend
            $scope.sendTimingEvent('Finish')
            
            $log.debug 'Timing finished. Total time:', $scope.formatElapsedTime($scope.elapsedTime + $scope.pausedTime)
        
        # Timer management
        $scope.startTimer = () ->
            $scope.stopTimer()  # Clear any existing timer
            
            timerInterval = setInterval(() ->
                if !$scope.isPaused && $scope.isActive
                    now = new Date()
                    $scope.elapsedTime = Math.floor((now - $scope.startTime) / 1000)
                    $scope.$apply() if !$scope.$$phase
            , 1000)
        
        $scope.stopTimer = () ->
            if timerInterval
                clearInterval(timerInterval)
                timerInterval = null
        
        # Send timing event to backend
        $scope.sendTimingEvent = (eventType) ->
            eventData = {
                jobId: $scope.jobId
                status: $scope.status
                eventType: eventType
                timestamp: new Date().toISOString()
                elapsedSeconds: $scope.elapsedTime + $scope.pausedTime
            }
            
            $http.post('/api/job-timing/event', eventData)
                .then (response) ->
                    $log.debug 'Timing event sent:', eventType, response.data
                .catch (error) ->
                    $log.error 'Error sending timing event:', error
        
        # Format elapsed time for display
        $scope.formatElapsedTime = (seconds) ->
            return '00:00' unless seconds
            
            hours = Math.floor(seconds / 3600)
            minutes = Math.floor((seconds % 3600) / 60)
            secs = seconds % 60
            
            if hours > 0
                return "#{hours.toString().padStart(2, '0')}:#{minutes.toString().padStart(2, '0')}:#{secs.toString().padStart(2, '0')}"
            else
                return "#{minutes.toString().padStart(2, '0')}:#{secs.toString().padStart(2, '0')}"
        
        # View full job in new window/tab
        $scope.viewFullJob = () ->
            if $scope.jobData
                # Open job details in new tab
                window.open("/orders/job/#{$scope.jobData.Id}", '_blank')
        
        # Modal dismiss
        $scope.$dismiss = () ->
            $modalInstance.dismiss('cancel')
        
        # Cleanup on destroy
        $scope.$on '$destroy', () ->
            $scope.stopTimer()
        
        # Initialize
        $scope.loadData()
        
        $log.debug 'JobBoardPopupController initialized for job:', jobId, 'status:', status
]

### jshint ignore: end ###
