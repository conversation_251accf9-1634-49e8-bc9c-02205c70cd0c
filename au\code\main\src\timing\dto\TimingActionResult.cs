using System;

namespace lep.timing.dto
{
    public class TimingActionResult
    {
        public bool Success { get; set; }
        public string Message { get; set; }
        public Guid? SessionId { get; set; }
        public TimingButtonState ButtonState { get; set; }
        public TimeSpan? CurrentDuration { get; set; }
        public ProductionInstructionsDto Instructions { get; set; }
        public bool RequiresInstructions { get; set; }
        public bool RequiresQualityChecks { get; set; }
        public string ErrorCode { get; set; }

        public static TimingActionResult CreateSuccess(string message = "Operation completed successfully")
        {
            return new TimingActionResult
            {
                Success = true,
                Message = message
            };
        }

        public static TimingActionResult Failure(string message, string errorCode = null)
        {
            return new TimingActionResult
            {
                Success = false,
                Message = message,
                ErrorCode = errorCode
            };
        }
    }
}
