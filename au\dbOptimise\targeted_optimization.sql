-- =============================================
-- Targeted Database Optimization Script
-- Based on Analysis Results from PRD_AU
-- Target: Critical performance issues identified
-- =============================================

USE [PRD_AU];
GO

SET NOCOUNT ON;
GO

PRINT '=== TARGETED DATABASE OPTIMIZATION STARTING ===';
PRINT 'Database: ' + DB_NAME();
PRINT 'Server: ' + @@SERVERNAME;
PRINT 'Execution Date: ' + CONVERT(VARCHAR(20), GETDATE(), 120);
PRINT '';

-- =============================================
-- PHASE 1: CREATE HIGH-IMPACT MISSING INDEXES
-- =============================================
PRINT '1. CREATING HIGH-IMPACT MISSING INDEXES...';

-- Index 1: Order table - Status and DateModified (Improvement Score: 146M+)
PRINT 'Creating index on Order table for Status and DateModified...';
BEGIN TRY
    IF NOT EXISTS (SELECT 1 FROM sys.indexes WHERE object_id = OBJECT_ID('dbo.Order') AND name = 'IX_Order_Status_DateModified')
    BEGIN
        CREATE INDEX [IX_Order_Status_DateModified] ON [dbo].[Order] ([Status], [DateModified]) 
        INCLUDE ([Id], [userId]) 
        WITH (FILLFACTOR = 90, ONLINE = ON);
        PRINT 'SUCCESS: IX_Order_Status_DateModified created';
    END
    ELSE
        PRINT 'Index IX_Order_Status_DateModified already exists';
END TRY
BEGIN CATCH
    PRINT 'ERROR creating IX_Order_Status_DateModified: ' + ERROR_MESSAGE();
END CATCH

-- Index 2: Comment table - JobId (Improvement Score: 5.9M+)
PRINT 'Creating index on Comment table for JobId...';
BEGIN TRY
    IF NOT EXISTS (SELECT 1 FROM sys.indexes WHERE object_id = OBJECT_ID('dbo.Comment') AND name = 'IX_Comment_JobId')
    BEGIN
        CREATE INDEX [IX_Comment_JobId] ON [dbo].[Comment] ([JobId]) 
        WITH (FILLFACTOR = 90, ONLINE = ON);
        PRINT 'SUCCESS: IX_Comment_JobId created';
    END
    ELSE
        PRINT 'Index IX_Comment_JobId already exists';
END TRY
BEGIN CATCH
    PRINT 'ERROR creating IX_Comment_JobId: ' + ERROR_MESSAGE();
END CATCH

-- Index 3: Job table - OrderId optimization (already exists but may need optimization)
PRINT 'Checking Job table OrderId index...';
BEGIN TRY
    IF NOT EXISTS (SELECT 1 FROM sys.indexes WHERE object_id = OBJECT_ID('dbo.Job') AND name = 'IX_Job_OrderId_Status')
    BEGIN
        CREATE INDEX [IX_Job_OrderId_Status] ON [dbo].[Job] ([OrderId], [Status]) 
        INCLUDE ([Id], [Facility], [DateCreated]) 
        WITH (FILLFACTOR = 90, ONLINE = ON);
        PRINT 'SUCCESS: IX_Job_OrderId_Status created';
    END
    ELSE
        PRINT 'Index IX_Job_OrderId_Status already exists';
END TRY
BEGIN CATCH
    PRINT 'ERROR creating IX_Job_OrderId_Status: ' + ERROR_MESSAGE();
END CATCH

-- Index 4: LepUser table - Customer lookups
PRINT 'Creating index on LepUser table for customer lookups...';
BEGIN TRY
    IF NOT EXISTS (SELECT 1 FROM sys.indexes WHERE object_id = OBJECT_ID('dbo.LepUser') AND name = 'IX_LepUser_Customer_Active')
    BEGIN
        CREATE INDEX [IX_LepUser_Customer_Active] ON [dbo].[LepUser] ([IsCustomer], [IsActive]) 
        INCLUDE ([Id], [Username]) 
        WITH (FILLFACTOR = 90, ONLINE = ON);
        PRINT 'SUCCESS: IX_LepUser_Customer_Active created';
    END
    ELSE
        PRINT 'Index IX_LepUser_Customer_Active already exists';
END TRY
BEGIN CATCH
    PRINT 'ERROR creating IX_LepUser_Customer_Active: ' + ERROR_MESSAGE();
END CATCH

PRINT '';

-- =============================================
-- PHASE 2: REBUILD HIGHLY FRAGMENTED INDEXES
-- =============================================
PRINT '2. REBUILDING HIGHLY FRAGMENTED INDEXES...';

-- Rebuild LepUser indexes (98% fragmentation)
PRINT 'Rebuilding LepUser.UserName index...';
BEGIN TRY
    ALTER INDEX [UserName] ON [dbo].[LepUser] REBUILD WITH (FILLFACTOR = 90, ONLINE = ON);
    PRINT 'SUCCESS: LepUser.UserName index rebuilt';
END TRY
BEGIN CATCH
    PRINT 'ERROR rebuilding LepUser.UserName: ' + ERROR_MESSAGE();
END CATCH

PRINT 'Rebuilding LepUser.FastUserLookupIdx1 index...';
BEGIN TRY
    ALTER INDEX [FastUserLookupIdx1] ON [dbo].[LepUser] REBUILD WITH (FILLFACTOR = 90, ONLINE = ON);
    PRINT 'SUCCESS: LepUser.FastUserLookupIdx1 index rebuilt';
END TRY
BEGIN CATCH
    PRINT 'ERROR rebuilding LepUser.FastUserLookupIdx1: ' + ERROR_MESSAGE();
END CATCH

-- Rebuild Job indexes (83% fragmentation)
PRINT 'Rebuilding Job._dta_index_Job_9_101575400__K65_K4_K1 index...';
BEGIN TRY
    ALTER INDEX [_dta_index_Job_9_101575400__K65_K4_K1] ON [dbo].[Job] REBUILD WITH (FILLFACTOR = 90, ONLINE = ON);
    PRINT 'SUCCESS: Job._dta_index_Job_9_101575400__K65_K4_K1 index rebuilt';
END TRY
BEGIN CATCH
    PRINT 'ERROR rebuilding Job._dta_index_Job_9_101575400__K65_K4_K1: ' + ERROR_MESSAGE();
END CATCH

-- Rebuild Order indexes (75% fragmentation)
PRINT 'Rebuilding Order._dta_index_Order_12_411512895__K2_K1_K28 index...';
BEGIN TRY
    ALTER INDEX [_dta_index_Order_12_411512895__K2_K1_K28] ON [dbo].[Order] REBUILD WITH (FILLFACTOR = 90, ONLINE = ON);
    PRINT 'SUCCESS: Order._dta_index_Order_12_411512895__K2_K1_K28 index rebuilt';
END TRY
BEGIN CATCH
    PRINT 'ERROR rebuilding Order._dta_index_Order_12_411512895__K2_K1_K28: ' + ERROR_MESSAGE();
END CATCH

-- Rebuild Job.job_orderid_index (61% fragmentation)
PRINT 'Rebuilding Job.job_orderid_index...';
BEGIN TRY
    ALTER INDEX [job_orderid_index] ON [dbo].[Job] REBUILD WITH (FILLFACTOR = 90, ONLINE = ON);
    PRINT 'SUCCESS: Job.job_orderid_index rebuilt';
END TRY
BEGIN CATCH
    PRINT 'ERROR rebuilding Job.job_orderid_index: ' + ERROR_MESSAGE();
END CATCH

-- Rebuild RunSlot.runslot_runId (56% fragmentation)
PRINT 'Rebuilding RunSlot.runslot_runId index...';
BEGIN TRY
    ALTER INDEX [runslot_runId] ON [dbo].[RunSlot] REBUILD WITH (FILLFACTOR = 90, ONLINE = ON);
    PRINT 'SUCCESS: RunSlot.runslot_runId index rebuilt';
END TRY
BEGIN CATCH
    PRINT 'ERROR rebuilding RunSlot.runslot_runId: ' + ERROR_MESSAGE();
END CATCH

PRINT '';

-- =============================================
-- PHASE 3: UPDATE CRITICAL STATISTICS
-- =============================================
PRINT '3. UPDATING STATISTICS ON CRITICAL TABLES...';

-- Update statistics on largest tables
PRINT 'Updating statistics on Job table...';
UPDATE STATISTICS [dbo].[Job] WITH FULLSCAN;
PRINT 'SUCCESS: Job statistics updated';

PRINT 'Updating statistics on Comment table...';
UPDATE STATISTICS [dbo].[Comment] WITH FULLSCAN;
PRINT 'SUCCESS: Comment statistics updated';

PRINT 'Updating statistics on Order table...';
UPDATE STATISTICS [dbo].[Order] WITH FULLSCAN;
PRINT 'SUCCESS: Order statistics updated';

PRINT 'Updating statistics on LepUser table...';
UPDATE STATISTICS [dbo].[LepUser] WITH FULLSCAN;
PRINT 'SUCCESS: LepUser statistics updated';

PRINT 'Updating statistics on Customer table...';
UPDATE STATISTICS [dbo].[Customer] WITH FULLSCAN;
PRINT 'SUCCESS: Customer statistics updated';

PRINT '';

-- =============================================
-- PHASE 4: REMOVE UNUSED INDEXES (OPTIONAL)
-- =============================================
PRINT '4. ANALYZING UNUSED INDEXES (Review before dropping)...';

-- Note: The unused index on Job table has 98,998 updates but 0 reads
-- This suggests it may be safe to drop, but requires careful review
PRINT 'Found unused index: Job.NonClusteredIndex-20221204-101540';
PRINT 'This index has 98,998 updates but 0 reads - consider dropping after review';
PRINT 'Command to drop: DROP INDEX [NonClusteredIndex-20221204-101540] ON [dbo].[Job];';

PRINT '';

-- =============================================
-- PHASE 5: VERIFICATION
-- =============================================
PRINT '5. POST-OPTIMIZATION VERIFICATION...';

-- Check new indexes
SELECT 
    'NEW_INDEXES' AS check_type,
    COUNT(*) AS new_index_count
FROM sys.indexes 
WHERE create_date >= DATEADD(minute, -10, GETDATE())
    AND name LIKE 'IX_%';

-- Check fragmentation levels after rebuild
SELECT 
    'FRAGMENTATION_CHECK' AS check_type,
    COUNT(*) AS high_fragmentation_count
FROM sys.dm_db_index_physical_stats(DB_ID(), NULL, NULL, NULL, 'LIMITED') ips
INNER JOIN sys.indexes i ON ips.object_id = i.object_id AND ips.index_id = i.index_id
WHERE ips.avg_fragmentation_in_percent > 30 
    AND ips.page_count > 100
    AND i.name IS NOT NULL;

-- Check statistics update dates
SELECT 
    'STATISTICS_CHECK' AS check_type,
    COUNT(*) AS recently_updated_stats
FROM sys.stats s
INNER JOIN sys.tables t ON s.object_id = t.object_id
WHERE STATS_DATE(s.object_id, s.stats_id) >= DATEADD(minute, -10, GETDATE())
    AND t.name IN ('Job', 'Order', 'Comment', 'LepUser', 'Customer');

PRINT '';
PRINT '=== TARGETED OPTIMIZATION COMPLETED ===';
PRINT 'Key improvements made:';
PRINT '1. Created high-impact missing indexes on Order and Comment tables';
PRINT '2. Rebuilt highly fragmented indexes (>50% fragmentation)';
PRINT '3. Updated statistics on critical tables';
PRINT '4. Identified unused indexes for review';
PRINT '';
PRINT 'Expected performance improvements:';
PRINT '- Order queries: 30-50% faster due to Status/DateModified index';
PRINT '- Comment lookups: 40-60% faster due to JobId index';
PRINT '- Job queries: 20-30% faster due to rebuilt indexes';
PRINT '- Overall I/O reduction: 25-40%';
PRINT '';
PRINT 'Monitor performance over next 24-48 hours and adjust as needed.';
