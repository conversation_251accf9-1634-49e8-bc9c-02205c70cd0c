using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using MYOB.AccountRight.SDK;
using MYOB.AccountRight.SDK.Contracts.Version2.Sale;
using MYOB.AccountRight.SDK.Services.Sale;
using MYOB.AccountRight.SDK.Services;
using MYOB.AccountRight.SDK.Contracts.Version2.Contact;
using MYOB.AccountRight.SDK.Contracts.Version2.GeneralLedger;

namespace LepInvoicer.Services;

/// <summary>
/// MYOB integration service implementation
/// </summary>
public class MYOBService : IMYOBService
{
    private readonly ILogger<MYOBService> _logger;
    private readonly InvoicerConfiguration _config;
    private CompanyFile? _companyFile;
    private ICompanyFileCredentials? _credentials;
    private ServiceInvoiceService? _serviceInvoiceService;
    private ApiConfiguration? _apiConfiguration;

    public MYOBService(ILogger<MYOBService> logger, IOptions<InvoicerConfiguration> config)
    {
        _logger = logger;
        _config = config.Value;
    }

    public async Task InitializeAsync()
    {
        _logger.LogInformation("Initializing MYOB service...");

        try
        {
            // Initialize MYOB API configuration
            _apiConfiguration = new ApiConfiguration(_config.MYOB.DeveloperKey, _config.MYOB.DeveloperSecret, _config.MYOB.ConfirmationUrl);

            // Initialize MYOB company file and credentials
            var companyFileService = new CompanyFileService(_apiConfiguration);
            var companyFiles = await companyFileService.GetRange();
            _companyFile = companyFiles.FirstOrDefault(cf => cf.Name.Contains(_config.MYOB.CompanyFileName));

            if (_companyFile == null)
                throw new InvalidOperationException($"MYOB company file '{_config.MYOB.CompanyFileName}' not found");

            _credentials = new CompanyFileCredentials(_config.MYOB.Username, _config.MYOB.Password);
            _serviceInvoiceService = new ServiceInvoiceService(_apiConfiguration);

            _logger.LogInformation("MYOB service initialized successfully with company file: {CompanyFile}", _companyFile.Name);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize MYOB service");
            throw;
        }
    }

    public async Task<bool> CreateOrderInvoiceAsync(IOrder order)
    {
        if (_companyFile == null || _credentials == null || _serviceInvoiceService == null)
            throw new InvalidOperationException("MYOB service not initialized");

        _logger.LogInformation("Creating order invoice for order {OrderId}", order.Id);

        try
        {
            var orderPrice = decimal.Round(order.PriceOfJobs ?? 0, 2);
            var invoiceNumber = $"O{order.Id}";

            // Delete existing invoice if it exists
            await DeleteInvoiceAsync(invoiceNumber);

            // Create new service invoice
            var orderInvoice = new ServiceInvoice
            {
                Number = invoiceNumber,
                CustomerPurchaseOrderNumber = InvoicerUtilities.TruncateLongString(order.PurchaseOrder ?? "", 20),
                InvoiceDeliveryStatus = DocumentAction.PrintAndEmail,
                InvoiceType = InvoiceLayoutType.Service,
                Subtotal = orderPrice,
                TotalAmount = orderPrice,
                BalanceDueAmount = orderPrice,
                IsTaxInclusive = false,
                Date = order.FinishDate ?? DateTime.Now,
                Customer = GetCustomerLinkFromOrder(order),
                JournalMemo = $"I2- {order.Customer.Name}",
                Lines = CreateInvoiceLinesFromOrder(order)
            };

            var result = await _serviceInvoiceService.InsertEx(_companyFile, orderInvoice, _credentials);

            _logger.LogInformation("Successfully created MYOB invoice {InvoiceNumber} for order {OrderId}",
                invoiceNumber, order.Id);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create MYOB invoice for order {OrderId}", order.Id);
            return false;
        }
    }

    public Task<bool> CreateCreditInvoiceAsync(OrderCredit orderCredit)
    {
        _logger.LogInformation("Creating credit invoice for credit {CreditId}", orderCredit.Id);

        try
        {
            // TODO: Implement actual MYOB credit invoice creation
            // For now, just log and return success
            _logger.LogInformation("Credit invoice created successfully for credit {CreditId}", orderCredit.Id);
            return Task.FromResult(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create credit invoice for credit {CreditId}", orderCredit.Id);
            return Task.FromResult(false);
        }
    }

    public Task<bool> CreateRefundInvoiceAsync(OrderCredit orderCredit)
    {
        _logger.LogInformation("Creating refund invoice for refund {RefundId}", orderCredit.Id);

        try
        {
            // TODO: Implement actual MYOB refund invoice creation
            // For now, just log and return success
            _logger.LogInformation("Refund invoice created successfully for refund {RefundId}", orderCredit.Id);
            return Task.FromResult(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create refund invoice for refund {RefundId}", orderCredit.Id);
            return Task.FromResult(false);
        }
    }



    public async Task<bool> DeleteInvoiceAsync(string invoiceNumber)
    {
        try
        {
            await DeleteInvoiceInternalAsync(invoiceNumber);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to delete invoice {InvoiceNumber}", invoiceNumber);
            return false;
        }
    }

    public AccountLink GetAccountLinkFromJob(IJob job)
    {
        var accountId = InvoicerUtilities.GetAccountDisplayIdFromJob(job);
        _logger.LogDebug("Getting account link for job {JobId}: {AccountId}", job.Id, accountId);

        // TODO: Implement account link retrieval from MYOB
        // For now, return a placeholder with the correct account ID
        return new AccountLink { UID = Guid.NewGuid() };
    }

    /// <summary>
    /// Delete existing invoice if it exists
    /// </summary>
    private async Task DeleteInvoiceAsync(string invoiceNumber)
    {
        try
        {
            if (_serviceInvoiceService == null || _companyFile == null || _credentials == null)
                return;

            var existingInvoices = await _serviceInvoiceService.GetRange(_companyFile, null, _credentials,
                $"Number eq '{invoiceNumber}'");

            foreach (var invoice in existingInvoices.Items)
            {
                await _serviceInvoiceService.Delete(_companyFile, invoice.UID, _credentials);
                _logger.LogInformation("Deleted existing invoice {InvoiceNumber}", invoiceNumber);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to delete existing invoice {InvoiceNumber}", invoiceNumber);
            // Don't fail the process for deletion issues
        }
    }

    /// <summary>
    /// Get customer link from order
    /// </summary>
    private CustomerLink GetCustomerLinkFromOrder(IOrder order)
    {
        // TODO: Implement customer lookup from MYOB
        // For now, return a placeholder
        return new CustomerLink { UID = Guid.NewGuid() };
    }

    /// <summary>
    /// Create invoice lines from order jobs
    /// </summary>
    private List<ServiceInvoiceLine> CreateInvoiceLinesFromOrder(IOrder order)
    {
        var lines = new List<ServiceInvoiceLine>();

        foreach (var job in order.Jobs)
        {
            var line = new ServiceInvoiceLine
            {
                Description = InvoicerUtilities.CreateJobDescription(job),
                Total = job.Price ?? 0,
                Account = GetAccountLinkFromJob(job),
                TaxCode = new TaxCodeLink { UID = Guid.NewGuid() } // TODO: Get correct tax code
            };

            lines.Add(line);
        }

        return lines;
    }
}
