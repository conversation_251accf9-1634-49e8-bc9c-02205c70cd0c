using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using MYOB.AccountRight.SDK;
using MYOB.AccountRight.SDK.Contracts.Version2.Sale;
using MYOB.AccountRight.SDK.Services.Sale;

namespace LepInvoicer.Services;

/// <summary>
/// MYOB integration service implementation
/// </summary>
public class MYOBService : IMYOBService
{
    private readonly ILogger<MYOBService> _logger;
    private readonly InvoicerConfiguration _config;
    private CompanyFile? _companyFile;
    private ICompanyFileCredentials? _credentials;
    private ServiceInvoiceService? _serviceInvoiceService;

    public MYOBService(ILogger<MYOBService> logger, IOptions<InvoicerConfiguration> config)
    {
        _logger = logger;
        _config = config.Value;
    }

    public async Task InitializeAsync()
    {
        _logger.LogInformation("Initializing MYOB service...");

        try
        {
            // Initialize MYOB company file and credentials
            _companyFile = await new CompanyFileService().GetRange().ConfigureAwait(false)
                .ContinueWith(t => t.Result.FirstOrDefault(cf => cf.Name == _config.MyobCompanyFileName));

            if (_companyFile == null)
                throw new InvalidOperationException($"MYOB company file '{_config.MyobCompanyFileName}' not found");

            _credentials = new CompanyFileCredentials(_config.MyobUsername, _config.MyobPassword);
            _serviceInvoiceService = new ServiceInvoiceService();

            _logger.LogInformation("MYOB service initialized successfully with company file: {CompanyFile}", _companyFile.Name);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize MYOB service");
            throw;
        }
    }

    public async Task<bool> CreateOrderInvoiceAsync(IOrder order)
    {
        if (_companyFile == null || _credentials == null || _serviceInvoiceService == null)
            throw new InvalidOperationException("MYOB service not initialized");

        _logger.LogInformation("Creating order invoice for order {OrderId}", order.Id);

        try
        {
            var orderPrice = decimal.Round(order.PriceOfJobs ?? 0, 2);
            var invoiceNumber = $"O{order.Id}";

            // Delete existing invoice if it exists
            await DeleteInvoiceAsync(invoiceNumber);

            // Create new service invoice
            var orderInvoice = new ServiceInvoice
            {
                Number = invoiceNumber,
                CustomerPurchaseOrderNumber = InvoicerUtilities.TruncateLongString(order.PurchaseOrder ?? "", 20),
                InvoiceDeliveryStatus = DocumentAction.PrintAndEmail,
                InvoiceType = InvoiceLayoutType.Service,
                Subtotal = orderPrice,
                TotalAmount = orderPrice,
                BalanceDueAmount = orderPrice,
                IsTaxInclusive = false,
                Date = order.FinishDate ?? DateTime.Now,
                Customer = GetCustomerLinkFromOrder(order),
                JournalMemo = $"I2- {order.Customer.Name}",
                Lines = CreateInvoiceLinesFromOrder(order)
            };

            var result = await _serviceInvoiceService.InsertEx(_companyFile, orderInvoice, _credentials);

            _logger.LogInformation("Successfully created MYOB invoice {InvoiceNumber} for order {OrderId}",
                invoiceNumber, order.Id);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create MYOB invoice for order {OrderId}", order.Id);
            return false;
        }
    }

    public Task<bool> CreateCreditInvoiceAsync(OrderCredit orderCredit)
    {
        _logger.LogInformation("Creating credit invoice for credit {CreditId}", "TBD");
        // TODO: Implement credit invoice creation with actual credit type
        return Task.FromResult(true);
    }

    public Task<bool> CreateRefundInvoiceAsync(OrderCredit orderCredit)
    {
        _logger.LogInformation("Creating refund invoice for refund {RefundId}", "TBD");
        // TODO: Implement refund invoice creation with actual refund type
        return Task.FromResult(true);
    }

    public Task<bool> DeleteInvoiceAsync(string invoiceNumber)
    {
        _logger.LogInformation("Deleting invoice {InvoiceNumber}", invoiceNumber);
        // TODO: Implement invoice deletion
        return Task.FromResult(true);
    }

    public AccountLink GetAccountLinkFromJob(IJob job)
    {
        _logger.LogDebug("Getting account link for job {JobId}: {AccountId}", "TBD", "TBD");

        // TODO: Implement account link retrieval with actual job type
        return new object(); // AccountLink placeholder
    }
}
