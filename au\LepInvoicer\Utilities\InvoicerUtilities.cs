using LepInvoicer.Constants;

namespace LepInvoicer.Utilities;

/// <summary>
/// Utility methods for the LEP Invoicer application
/// </summary>
public static class InvoicerUtilities
{
    /// <summary>
    /// Truncate a string to a maximum length
    /// </summary>
    /// <param name="input">Input string</param>
    /// <param name="maxLength">Maximum length</param>
    /// <returns>Truncated string</returns>
    public static string TruncateString(string? input, int maxLength)
    {
        if (string.IsNullOrEmpty(input))
            return string.Empty;
            
        return input.Length <= maxLength ? input : input[..maxLength];
    }

    /// <summary>
    /// Get MYOB account display ID for a job based on its type and print method
    /// </summary>
    /// <param name="job">The job to get account for</param>
    /// <returns>Account display ID</returns>
    public static string GetAccountDisplayIdFromJob(IJob job)
    {
        var isOffset = job.PrintType == PrintType.O;

        // Business cards
        if (job.IsBusinessCard())
            return isOffset ? InvoicerConstants.AccountDisplayIds.OffsetBusinessCards 
                           : InvoicerConstants.AccountDisplayIds.DigitalBusinessCards;

        // Brochures
        if (job.IsBrochure())
            return isOffset ? InvoicerConstants.AccountDisplayIds.OffsetBrochures 
                           : InvoicerConstants.AccountDisplayIds.DigitalBrochures;

        // Stationery
        if (job.Template.Category.Contains(InvoicerConstants.JobCategories.Stationery))
            return isOffset ? InvoicerConstants.AccountDisplayIds.OffsetStationery 
                           : InvoicerConstants.AccountDisplayIds.DigitalStationery;

        // Magazines
        if (job.IsMagazine())
            return isOffset ? InvoicerConstants.AccountDisplayIds.OffsetMagazines 
                           : InvoicerConstants.AccountDisplayIds.DigitalMagazines;

        // Presentation folders
        if (job.Template.Category.Contains(InvoicerConstants.JobCategories.PresentationFolders))
            return isOffset ? InvoicerConstants.AccountDisplayIds.OffsetPresentationFolders 
                           : InvoicerConstants.AccountDisplayIds.DigitalPresentationFolders;

        // Wide format - Adhesives
        if (job.Template.Category.Contains(InvoicerConstants.JobCategories.AdhesiveSigns))
            return InvoicerConstants.AccountDisplayIds.WideFormatAdhesives;

        // Wide format - Banners
        if (job.Template.Category.Contains(InvoicerConstants.JobCategories.Banners))
            return InvoicerConstants.AccountDisplayIds.WideFormatBanners;

        // NCR Books and Envelopes
        if (job.IsNCRBook() || job.IsEnvelope())
            return InvoicerConstants.AccountDisplayIds.OffsetOther;

        // Default to "Other" category
        return isOffset ? InvoicerConstants.AccountDisplayIds.OffsetOther 
                       : InvoicerConstants.AccountDisplayIds.DigitalOther;
    }

    /// <summary>
    /// Clean template name for invoice description
    /// </summary>
    /// <param name="templateName">Original template name</param>
    /// <returns>Cleaned template name</returns>
    public static string CleanTemplateName(string templateName)
    {
        if (string.IsNullOrEmpty(templateName))
            return string.Empty;

        return templateName
            .Replace(InvoicerConstants.TemplateNameReplacements.SameDayDispatch, 
                    InvoicerConstants.TemplateNameReplacements.SameDayDispatchShort)
            .Replace(InvoicerConstants.TemplateNameReplacements.NextDayDispatch, 
                    InvoicerConstants.TemplateNameReplacements.NextDayDispatchShort);
    }

    /// <summary>
    /// Generate invoice number for different types
    /// </summary>
    /// <param name="type">Invoice type (O for order, C for credit, etc.)</param>
    /// <param name="orderId">Order ID</param>
    /// <param name="creditId">Credit ID (optional)</param>
    /// <param name="customerId">Customer ID (for credits without orders)</param>
    /// <returns>Generated invoice number</returns>
    public static string GenerateInvoiceNumber(string type, int? orderId = null, int? creditId = null, int? customerId = null)
    {
        return type switch
        {
            "O" when orderId.HasValue => $"O{orderId}",
            var t when (t == InvoicerConstants.CreditTypes.Credit || t == InvoicerConstants.CreditTypes.Miscellaneous) 
                      && orderId.HasValue && creditId.HasValue => $"{t}{orderId}{creditId}",
            var t when t == InvoicerConstants.CreditTypes.Refund && customerId.HasValue && creditId.HasValue => $"{t}{customerId}{creditId}",
            _ => throw new ArgumentException($"Invalid invoice number parameters: type={type}, orderId={orderId}, creditId={creditId}, customerId={customerId}")
        };
    }

    /// <summary>
    /// Create job description for invoice line
    /// </summary>
    /// <param name="job">The job</param>
    /// <returns>Formatted job description</returns>
    public static string CreateJobDescription(IJob job)
    {
        var templateName = CleanTemplateName(job.Template.Name);
        var printType = job.PrintType.ToDescription();
        var paperSize = job.FinishedSize?.PaperSize?.Name ?? "";
        var stockName = job.Stock.Name;
        var quantity = job.Quantity;

        return $"J{job.Id} / {templateName} / {printType} / {paperSize} / {stockName} / Qty: {quantity}";
    }

    /// <summary>
    /// Escape single quotes for SQL
    /// </summary>
    /// <param name="input">Input string</param>
    /// <returns>SQL-safe string</returns>
    public static string EscapeSqlString(string? input)
    {
        return input?.Replace("'", "''") ?? string.Empty;
    }

    /// <summary>
    /// Get customer email address with fallback logic
    /// </summary>
    /// <param name="customer">Customer object</param>
    /// <param name="overrideEmail">Override email for testing</param>
    /// <returns>Email address to use</returns>
    public static string? GetCustomerEmailAddress(ICustomerUser customer, string? overrideEmail = null)
    {
        if (!string.IsNullOrEmpty(overrideEmail))
            return overrideEmail;

        if (!string.IsNullOrEmpty(customer.AccountEmail))
            return customer.AccountEmail;

        return customer.Email;
    }

    /// <summary>
    /// Create directory if it doesn't exist
    /// </summary>
    /// <param name="path">Directory path</param>
    public static void EnsureDirectoryExists(string path)
    {
        if (!Directory.Exists(path))
        {
            Directory.CreateDirectory(path);
        }
    }

    /// <summary>
    /// Format date for file/folder names
    /// </summary>
    /// <param name="date">Date to format</param>
    /// <returns>Formatted date string</returns>
    public static string FormatDateForPath(DateTime date)
    {
        return date.ToString("yyyy/MMM/dd").Replace(".", "");
    }

    /// <summary>
    /// Validate order for invoicing
    /// </summary>
    /// <param name="order">Order to validate</param>
    /// <returns>Validation result</returns>
    public static (bool IsValid, string? ErrorMessage) ValidateOrderForInvoicing(IOrder order)
    {
        if (order.PriceOfJobs == null || order.PriceOfJobs == 0)
            return (false, "Order has zero price");

        if (order.FinishDate == null)
            return (false, "Order has no finish date");

        if (order.Jobs == null || !order.Jobs.Any())
            return (false, "Order has no jobs");

        return (true, null);
    }

    /// <summary>
    /// Calculate rounded decimal value
    /// </summary>
    /// <param name="value">Value to round</param>
    /// <param name="decimals">Number of decimal places</param>
    /// <returns>Rounded decimal</returns>
    public static decimal RoundDecimal(decimal value, int decimals = 2)
    {
        return Math.Round(value, decimals);
    }

    /// <summary>
    /// Parse decimal safely
    /// </summary>
    /// <param name="value">String value to parse</param>
    /// <param name="defaultValue">Default value if parsing fails</param>
    /// <returns>Parsed decimal or default</returns>
    public static decimal ParseDecimalSafe(string? value, decimal defaultValue = 0m)
    {
        if (string.IsNullOrEmpty(value))
            return defaultValue;

        return decimal.TryParse(value, out var result) ? result : defaultValue;
    }

    /// <summary>
    /// Get file extension from MIME type
    /// </summary>
    /// <param name="mimeType">MIME type</param>
    /// <returns>File extension</returns>
    public static string GetFileExtensionFromMimeType(string mimeType)
    {
        return mimeType.ToLowerInvariant() switch
        {
            "application/pdf" => ".pdf",
            "text/html" => ".html",
            "image/jpeg" => ".jpg",
            "image/png" => ".png",
            _ => ".bin"
        };
    }
}
