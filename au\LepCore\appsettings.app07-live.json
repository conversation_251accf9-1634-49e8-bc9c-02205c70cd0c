{
    "IMP": {
        "HotfolderPath": "\\\\ppp01\\HotFolderRoot\\MyLEP"
    },
    "TestBox": true,
    "HolidayMode": false,
    "ApplicationInsights": {
        "InstrumentationKey": ""
    },
    "Logging": {
        "IncludeScopes": false,
        "LogLevel": {
            "Default": "Debug",
            "System": "Error",
            "Microsoft": "Error",
            "Microsoft.AspNetCore.Mvc": "Error",
            "Microsoft.AspNetCore.Authentication": "Error",
            "Microsoft.AspNetCore.Routing.RouteBase": "Error",
            "Microsoft.AspNetCore.Server.Kestrel": "Error",
            "NHibernate": "Warning",
            "NHibernate.SQL": "Debug",
            "Microsoft.AspNetCore.SignalR": "None",
            "Microsoft.AspNetCore.Hosting": "None",
            "Microsoft.AspNetCore.StaticFiles": "None"
        }
    },
    "CustomerLogoDirectory": "\\\\dfs01\\customerlogos",
    "DataDirectory": "\\\\dfs01\\resource",
   "StaticAssets": "C:\\LepSF\\au\\StaticAssets",
    "OldDataDirectory": "\\\\dfs01\\resource",
    "DataDirectoryPC": "\\\\dfs01\\resource",
    "DataDirectoryMac": "/Volumes/resource",
    "InvoicerPDFFolder": "\\\\dfs01\\resource\\invoices",
    "job.option.csv.folder": "\\\\henry\\C$\\Lepdata\\StaticAssets\\jobOptionCSVs",
    "lepcrm.webservice.url": "http://winston.internal.lepcolourprinters.com.au:1000/MSCRMService.asmx",
    "compdata.webservice.url": "http://harry:8089/iFreightChargeEnquiryService.svc",
    "AbsolutePathURL": "http://localhost:5000",
    "reports": {
        "LepQuote": "\\\\henry\\C$\\LEPDATA\\t1.frx"
    },
    "email": {
        "sendMail": false,
        "server": "smtp.sendgrid.net",
        "port": "587",
        "username": "<EMAIL>",
        "password": "*********************************************************************"
    },
    "Nhibernate": {
        "Con": "Data Source=newman; user id=sa; password=*************; Initial Catalog=PRD_AU;MultipleActiveResultSets=true;Max Pool Size=200;App=LepCore"
    },
    "SupplyMaster": {
        "Con": "Data Source=*************; user id=sa; password=*************; Initial Catalog=SupplyMaster;MultipleActiveResultSets=true"
    },
    "GhostScript": "\\\\henry\\C$\\gs10\\bin\\gswin64c.exe",
    "PdfTk": "\\\\henry\\C$\\gs952\\bin\\pdftk.exe",
    "LibTiff": "\\\\henry\\C$\\LepData\\LibTiff\\tiff2pdf.exe",
    "PdfToPrinter": "\\\\henry\\c$\\LepData\\PdfToPrinter.exe",
    
    "Labels": {
        "LogoLabel": "\\\\henry\\c$\\LEPDATA\\Labels2\\LogoLabel_102x73mm.frx",
        "PayMeLabel": "\\\\henry\\c$\\LEPDATA\\Labels2\\PayMeLabel.frx",
        "FillingLabel": "\\\\henry\\c$\\LEPDATA\\Labels2\\FillingLabel.frx",
        "SampleLabel": "\\\\henry\\c$\\LEPDATA\\Labels2\\SampleLabel.frx",
        "AddressA4Label": "\\\\henry\\c$\\LEPDATA\\Labels2\\AddressA4Label.frx",
        "PickupLabel": "\\\\henry\\c$\\LEPDATA\\Labels2\\PickupLabel.frx",
        "OneDeliveryOnly": "\\\\henry\\c$\\LEPDATA\\Labels2\\OneDeliveryOnly.frx",
        "AddressLabel": "\\\\henry\\c$\\LEPDATA\\Labels2\\SampleLabel.frx",
        "AddressLabelOther": "\\\\henry\\c$\\LEPDATA\\Labels2\\SampleLabel.frx",
        "CartonLabel": "\\\\henry\\c$\\LEPDATA\\labels2\\CartonLabel.frx",
        "CartonLabelMH": "\\\\henry\\c$\\LepData\\labels2\\CartonLabelMH.frx"
    },
    "Seq": {
        "ServerUrl": "http://localhost:5341",
        "MinimumLevel": "Trace",
        "LevelOverride": {
            "Microsoft": "Warning"
        }
    },
    "FreightProvider": "SmartFreight", // or  CompData "current"
    "AutomatedArtworkCheck": {
        "Enabled": true,
        "Timeout": "00:05:59",
        "Method": "DoPitStopViaCommandLine",
        "profiles": {
            "default": "\\\\henry\\C$\\LEPDATA\\default.ppp",
            "spotcolor": "\\\\henry\\C$\\LEPDATA\\Spotcolour.ppp",
            "wideformat": "\\\\henry\\C$\\LEPDATA\\Wideformat.ppp",
        },
        "DoPitStopViaCommandLine": {
            "path": "\\\\henry\\C$\\Program Files\\Enfocus\\Enfocus PitStop Server 24\\PitStopServerCLI.exe",
            "mutator": "\\\\henry\\C$\\LEPDATA\\LEP Check 2013-1.ppp"
        },
        "DoPitStopViaHotFolder": {
            "input": "\\\\henry\\c$\\hotfolder\\Input Folder",
            "output": "\\\\henry\\c$\\hotfolder\\Output Folder"
        }
    },
    "Dispatchers": [
        {
            "Name": "FG-DISTRIB-02",
            "DispatchFacility": "FG"
        },
        {
            "Name": "FG-DISTRIB-03",
            "DispatchFacility": "FG"
        },
        {
            "Name": "PM-DISTRIB-01",
            "DispatchFacility": "PM"
        },
        {
            "Name": "PM-BINDERY-05",
            "DispatchFacility": "PM"
        }
    ],
    "SmartFreight": {
        "Url": {
            "SFOv1": "https://api-au-1.smartfreight.com/api/soap/classic",
            "DeliveryOptions": "https://api-au-1.smartfreight.com/api/soap/deliveryoptions"
        },
        "Senders": {
            "LEPQLD": {
                "Id": "NWM",
                "Key": "RqB0fJOapwP79AVDdzF5Pgg7fESwF4GxHQ"
            },
            "TESTQLD": {
                "Id": "RqB0fJOapwP79AVDdzF5Pgg7fESwF4GxHQ",
                "Key": ""
            },
            "LEPVIC": {
                "Id": "NGU",
                "Key": "JoIJ2bXndxCk0V3ZfkM6VxKuzv1qgEAiA0"
            },
            "TESTVIC": {
                "Id": "NGU",
                "Key": "JoIJ2bXndxCk0V3ZfkM6VxKuzv1qgEAiA0"
            }
        },
    },
}
