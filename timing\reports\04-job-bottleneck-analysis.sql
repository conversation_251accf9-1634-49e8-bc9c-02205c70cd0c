-- Job Bottleneck Analysis
-- Identify which job steps are taking longest and causing delays

WITH JobStepDurations AS (
    SELECT
        e.JobId,
        j.Name AS JobName,
        o.Id AS OrderId,
        c.UserName AS CustomerName,
        e.Status,
        e.WorkstationId,
        MIN(CASE WHEN e.EventType = 1 THEN e.EventTime END) AS StepStartTime,
        MAX(CASE WHEN e.EventType = 4 THEN e.EventTime END) AS StepEndTime,
        DATEDIFF(SECOND,
            MIN(CASE WHEN e.EventType = 1 THEN e.EventTime END),
            MAX(CASE WHEN e.EventType = 4 THEN e.EventTime END)
        ) AS StepDurationSeconds
    FROM JobStepTimingEvent e
        INNER JOIN Job j ON e.JobId = j.Id
        INNER JOIN [Order] o ON j.OrderId = o.Id
        INNER JOIN LepUser c ON o.UserId = c.Id
    GROUP BY e.JobId, j.<PERSON>, o.Id, c.<PERSON>, e.<PERSON>, e.WorkstationId
    HAVING MIN(CASE WHEN e.EventType = 1 THEN e.EventTime END) IS NOT NULL
       AND MAX(CASE WHEN e.EventType = 4 THEN e.EventTime END) IS NOT NULL
),
StatusAverages AS (
    SELECT
        Status,
        AVG(CAST(StepDurationSeconds AS FLOAT)) AS AvgDuration
    FROM JobStepDurations
    WHERE StepDurationSeconds > 0
    GROUP BY Status
)
SELECT
    jsd.JobId,
    jsd.JobName,
    jsd.OrderId,
    jsd.CustomerName,
    jsd.Status,
    jsd.WorkstationId,
    jsd.StepStartTime,
    jsd.StepEndTime,
    jsd.StepDurationSeconds,
    FORMAT(DATEADD(SECOND, jsd.StepDurationSeconds, 0), 'HH:mm:ss') AS FormattedDuration,
    sa.AvgDuration,
    FORMAT(DATEADD(SECOND, sa.AvgDuration, 0), 'HH:mm:ss') AS AvgFormattedDuration,
    CASE
        WHEN jsd.StepDurationSeconds > sa.AvgDuration * 2 THEN 'Significantly Slow'
        WHEN jsd.StepDurationSeconds > sa.AvgDuration * 1.5 THEN 'Slow'
        WHEN jsd.StepDurationSeconds < sa.AvgDuration * 0.5 THEN 'Fast'
        ELSE 'Normal'
    END AS PerformanceFlag,
    ROUND((jsd.StepDurationSeconds / sa.AvgDuration - 1) * 100, 1) AS PercentageVsAverage
FROM JobStepDurations jsd
    INNER JOIN StatusAverages sa ON jsd.Status = sa.Status
WHERE jsd.StepDurationSeconds > 0
ORDER BY jsd.StepDurationSeconds DESC;
