# =============================================
# Database Optimization Execution PowerShell Script
# Target: [SRV03].[PRD_AU]
# Purpose: Advanced execution of database optimization scripts with error handling
# Author: Database Optimization Assistant
# =============================================

param(
    [string]$Server = "srv03",
    [string]$Database = "PRD_AU",
    [string]$Username = "sa",
    [string]$Password = "11_Fore5tGl5n",
    [switch]$AnalysisOnly,
    [switch]$SkipConfirmation,
    [string]$LogPath = ""
)

# Set error action preference
$ErrorActionPreference = "Stop"

# Get script directory
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
if ([string]::IsNullOrEmpty($LogPath)) {
    $LogDir = Join-Path $ScriptDir "logs"
} else {
    $LogDir = $LogPath
}

# Create logs directory if it doesn't exist
if (!(Test-Path $LogDir)) {
    New-Item -ItemType Directory -Path $LogDir -Force | Out-Null
}

# Generate timestamp
$Timestamp = Get-Date -Format "yyyyMMdd_HHmmss"

# Function to write log with timestamp
function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $LogMessage = "$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss') [$Level] $Message"
    Write-Host $LogMessage
    $LogMessage | Out-File -FilePath (Join-Path $LogDir "execution_$Timestamp.log") -Append
}

# Function to execute SQL script
function Invoke-SqlScript {
    param(
        [string]$ScriptPath,
        [string]$OutputPath,
        [int]$TimeoutSeconds = 3600
    )
    
    try {
        Write-Log "Executing script: $ScriptPath"
        
        $sqlcmdArgs = @(
            "-S", $Server,
            "-U", $Username,
            "-P", $Password,
            "-d", $Database,
            "-i", $ScriptPath,
            "-o", $OutputPath,
            "-r", "1",
            "-t", $TimeoutSeconds.ToString()
        )
        
        $process = Start-Process -FilePath "sqlcmd" -ArgumentList $sqlcmdArgs -Wait -PassThru -NoNewWindow
        
        if ($process.ExitCode -eq 0) {
            Write-Log "Script executed successfully: $ScriptPath" "SUCCESS"
            return $true
        } else {
            Write-Log "Script execution failed with exit code $($process.ExitCode): $ScriptPath" "ERROR"
            return $false
        }
    }
    catch {
        Write-Log "Exception during script execution: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

# Function to test database connection
function Test-DatabaseConnection {
    try {
        Write-Log "Testing database connection to $Server.$Database"
        
        $testQuery = "SELECT 'Connection successful' AS Status, GETDATE() AS CurrentTime"
        $sqlcmdArgs = @(
            "-S", $Server,
            "-U", $Username,
            "-P", $Password,
            "-d", $Database,
            "-Q", $testQuery,
            "-h", "-1"
        )
        
        $process = Start-Process -FilePath "sqlcmd" -ArgumentList $sqlcmdArgs -Wait -PassThru -NoNewWindow
        
        if ($process.ExitCode -eq 0) {
            Write-Log "Database connection successful" "SUCCESS"
            return $true
        } else {
            Write-Log "Database connection failed" "ERROR"
            return $false
        }
    }
    catch {
        Write-Log "Exception during connection test: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

# Function to get user confirmation
function Get-UserConfirmation {
    param([string]$Message)
    
    if ($SkipConfirmation) {
        return $true
    }
    
    do {
        $response = Read-Host "$Message (Y/N)"
    } while ($response -notmatch '^[YyNn]$')
    
    return $response -match '^[Yy]$'
}

# Main execution
try {
    Write-Log "=== Database Optimization Execution Starting ===" "INFO"
    Write-Log "Server: $Server"
    Write-Log "Database: $Database"
    Write-Log "Script Directory: $ScriptDir"
    Write-Log "Log Directory: $LogDir"
    Write-Log "Analysis Only: $AnalysisOnly"
    
    # Check if sqlcmd is available
    try {
        $null = Get-Command "sqlcmd" -ErrorAction Stop
        Write-Log "sqlcmd found and available"
    }
    catch {
        Write-Log "sqlcmd is not available or not in PATH" "ERROR"
        Write-Log "Please install SQL Server Command Line Utilities" "ERROR"
        exit 1
    }
    
    # Test database connection
    if (!(Test-DatabaseConnection)) {
        Write-Log "Cannot connect to database. Please check connection parameters." "ERROR"
        exit 1
    }
    
    # Confirmation prompt
    if (!$SkipConfirmation) {
        Write-Host ""
        Write-Host "WARNING: This will execute database optimization scripts on $Server.$Database" -ForegroundColor Yellow
        Write-Host ""
        Write-Host "The following operations will be performed:"
        Write-Host "1. Database analysis (read-only)"
        if (!$AnalysisOnly) {
            Write-Host "2. Index optimization (creates indexes, rebuilds fragmented indexes)"
            Write-Host "3. Statistics updates"
        }
        Write-Host ""
        Write-Host "IMPORTANT: Ensure you have a recent backup of the database!" -ForegroundColor Red
        Write-Host ""
        
        if (!(Get-UserConfirmation "Do you want to continue?")) {
            Write-Log "Operation cancelled by user"
            exit 0
        }
    }
    
    # Phase 1: Database Analysis
    Write-Log "=== Phase 1: Database Analysis ===" "INFO"
    $AnalysisScript = Join-Path $ScriptDir "01_database_analysis.sql"
    $AnalysisLog = Join-Path $LogDir "01_analysis_$Timestamp.log"
    
    if (!(Test-Path $AnalysisScript)) {
        Write-Log "Analysis script not found: $AnalysisScript" "ERROR"
        exit 1
    }
    
    if (!(Invoke-SqlScript -ScriptPath $AnalysisScript -OutputPath $AnalysisLog)) {
        Write-Log "Analysis phase failed. Check log: $AnalysisLog" "ERROR"
        exit 1
    }
    
    Write-Log "Analysis completed successfully. Log: $AnalysisLog" "SUCCESS"
    
    # Exit if analysis only
    if ($AnalysisOnly) {
        Write-Log "Analysis-only mode completed. Review results in: $AnalysisLog" "INFO"
        exit 0
    }
    
    # Confirmation for optimization
    if (!(Get-UserConfirmation "Analysis complete. Do you want to proceed with optimization implementation?")) {
        Write-Log "Optimization cancelled. Analysis results available in: $AnalysisLog"
        exit 0
    }
    
    # Phase 2: Optimization Implementation
    Write-Log "=== Phase 2: Optimization Implementation ===" "INFO"
    $OptimizationScript = Join-Path $ScriptDir "02_index_optimization.sql"
    $OptimizationLog = Join-Path $LogDir "02_optimization_$Timestamp.log"
    
    if (!(Test-Path $OptimizationScript)) {
        Write-Log "Optimization script not found: $OptimizationScript" "ERROR"
        exit 1
    }
    
    Write-Log "Executing optimization script. This may take several minutes..."
    if (!(Invoke-SqlScript -ScriptPath $OptimizationScript -OutputPath $OptimizationLog -TimeoutSeconds 7200)) {
        Write-Log "Optimization phase failed. Check log: $OptimizationLog" "ERROR"
        Write-Log "You may need to run the rollback script if partial changes were made" "WARNING"
        exit 1
    }
    
    Write-Log "Optimization completed successfully. Log: $OptimizationLog" "SUCCESS"
    
    # Phase 3: Post-Optimization Verification
    Write-Log "=== Phase 3: Post-Optimization Verification ===" "INFO"
    $VerificationLog = Join-Path $LogDir "03_verification_$Timestamp.log"
    
    # Create verification queries
    $VerificationQueries = @(
        @{
            Name = "Newly Created Indexes"
            Query = "SELECT 'NEWLY_CREATED_INDEXES' AS check_type, COUNT(*) AS count FROM sys.indexes WHERE create_date >= DATEADD(hour, -1, GETDATE()) AND name LIKE 'IX_%'"
        },
        @{
            Name = "High Fragmentation Indexes"
            Query = "SELECT 'FRAGMENTATION_CHECK' AS check_type, COUNT(*) AS high_fragmentation_indexes FROM sys.dm_db_index_physical_stats(DB_ID(), NULL, NULL, NULL, 'LIMITED') WHERE avg_fragmentation_in_percent > 30 AND page_count > 100"
        },
        @{
            Name = "Recently Updated Statistics"
            Query = "SELECT 'STATISTICS_CHECK' AS check_type, COUNT(*) AS recently_updated FROM sys.stats s INNER JOIN sys.tables t ON s.object_id = t.object_id WHERE STATS_DATE(s.object_id, s.stats_id) >= DATEADD(hour, -1, GETDATE())"
        }
    )
    
    "-- Post-optimization verification" | Out-File -FilePath $VerificationLog
    "-- Generated on $(Get-Date)" | Out-File -FilePath $VerificationLog -Append
    "" | Out-File -FilePath $VerificationLog -Append
    
    foreach ($query in $VerificationQueries) {
        Write-Log "Running verification: $($query.Name)"
        
        $sqlcmdArgs = @(
            "-S", $Server,
            "-U", $Username,
            "-P", $Password,
            "-d", $Database,
            "-Q", $query.Query,
            "-h", "-1"
        )
        
        "-- $($query.Name)" | Out-File -FilePath $VerificationLog -Append
        $result = & sqlcmd @sqlcmdArgs
        $result | Out-File -FilePath $VerificationLog -Append
        "" | Out-File -FilePath $VerificationLog -Append
    }
    
    Write-Log "Verification completed. Log: $VerificationLog" "SUCCESS"
    
    # Summary
    Write-Log "=== Optimization Summary ===" "INFO"
    Write-Log "All phases completed successfully!" "SUCCESS"
    Write-Log ""
    Write-Log "Generated files:"
    Write-Log "- Analysis log: $AnalysisLog"
    Write-Log "- Optimization log: $OptimizationLog"
    Write-Log "- Verification log: $VerificationLog"
    Write-Log ""
    Write-Log "Next steps:"
    Write-Log "1. Review all log files for any warnings or errors"
    Write-Log "2. Monitor database performance over the next 24-48 hours"
    Write-Log "3. Run application tests to ensure functionality"
    Write-Log "4. Keep the rollback script available if needed"
    
    # Offer to open logs directory
    if (!(Get-UserConfirmation "Do you want to open the logs directory?")) {
        Invoke-Item $LogDir
    }
    
    Write-Log "Database optimization execution completed successfully!" "SUCCESS"
}
catch {
    Write-Log "Unexpected error during execution: $($_.Exception.Message)" "ERROR"
    Write-Log "Stack trace: $($_.ScriptStackTrace)" "ERROR"
    exit 1
}
