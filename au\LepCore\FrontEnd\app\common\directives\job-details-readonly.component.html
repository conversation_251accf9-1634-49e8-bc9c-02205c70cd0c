<div class="job-details-readonly">
    <div class="panel panel-default">
        <div class="panel-header">
            <h4 class="panel-title">
                <i class="glyphicon glyphicon-file"></i>
                Job Details - {{job.Name}}
                <span class="label label-{{job.StatusCss}}">{{job.StatusC}}</span>
            </h4>
        </div>
        <div class="panel-body">
            <!-- Basic Job Information -->
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="control-label">Job ID:</label>
                        <span class="form-control-static">{{job.Id}}</span>
                    </div>
                    <div class="form-group">
                        <label class="control-label">Template:</label>
                        <span class="form-control-static">{{job.Template.Name}}</span>
                    </div>
                    <div class="form-group">
                        <label class="control-label">Quantity:</label>
                        <span class="form-control-static">{{job.Quantity | number}}</span>
                    </div>
                    <div class="form-group" ng-if="job.PrintType">
                        <label class="control-label">Print Type:</label>
                        <span class="form-control-static">{{job.PrintType | printtype}}</span>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="control-label">Order ID:</label>
                        <span class="form-control-static">{{job.OrderId}}</span>
                    </div>
                    <div class="form-group">
                        <label class="control-label">Status:</label>
                        <span class="form-control-static">{{job.StatusC}}</span>
                    </div>
                    <div class="form-group" ng-if="job.Price">
                        <label class="control-label">Price:</label>
                        <span class="form-control-static">${{job.Price | number:2}}</span>
                    </div>
                    <div class="form-group" ng-if="job.Facility">
                        <label class="control-label">Facility:</label>
                        <span class="form-control-static">{{job.Facility}}</span>
                    </div>
                </div>
            </div>

            <!-- Size and Material Information -->
            <div class="row">
                <div class="col-md-6">
                    <h5><i class="glyphicon glyphicon-resize-full"></i> Size &amp; Material</h5>
                    <div class="form-group" ng-if="job.FinishedSize">
                        <label class="control-label">Finished Size:</label>
                        <span class="form-control-static">
                            {{job.FinishedSize.PaperSize.Name}}
                            ({{job.FinishedSize.Width}}mm × {{job.FinishedSize.Height}}mm)
                        </span>
                    </div>
                    <div class="form-group" ng-if="job.FoldedSize && job.FoldedSize.PaperSize.Id">
                        <label class="control-label">Folded Size:</label>
                        <span class="form-control-static">
                            {{job.FoldedSize.PaperSize.Name}}
                            ({{job.FoldedSize.Width}}mm × {{job.FoldedSize.Height}}mm)
                        </span>
                    </div>
                    <div class="form-group" ng-if="job.Stock">
                        <label class="control-label">Stock:</label>
                        <span class="form-control-static">{{job.Stock.Name}}</span>
                    </div>
                    <div class="form-group" ng-if="job.StockOverride && job.StockOverride.Name">
                        <label class="control-label">Stock Override:</label>
                        <span class="form-control-static text-warning">{{job.StockOverride.Name}}</span>
                    </div>
                    <div class="form-group" ng-if="job.Pages > 1">
                        <label class="control-label">Pages:</label>
                        <span class="form-control-static">{{job.Pages}}</span>
                    </div>
                </div>
                <div class="col-md-6">
                    <h5><i class="glyphicon glyphicon-tint"></i> Printing &amp; Finishing</h5>
                    <div class="form-group" ng-if="job.FrontPrinting">
                        <label class="control-label">Front Printing:</label>
                        <span class="form-control-static">{{$root.enums.ValueDesc.JobPrintOptions[job.FrontPrinting]}}</span>
                    </div>
                    <div class="form-group" ng-if="job.BackPrinting">
                        <label class="control-label">Back Printing:</label>
                        <span class="form-control-static">{{$root.enums.ValueDesc.JobPrintOptions[job.BackPrinting]}}</span>
                    </div>
                    <div class="form-group" ng-if="job.FrontCelloglaze || job.BackCelloglaze">
                        <label class="control-label">Cello:</label>
                        <span class="form-control-static">
                            {{$root.enums.ValueDesc.JobCelloglazeOptions[job.FrontCelloglaze]}} /
                            {{$root.enums.ValueDesc.JobCelloglazeOptions[job.BackCelloglaze]}}
                        </span>
                    </div>
                    <div class="form-group" ng-if="job.FoilColour">
                        <label class="control-label">Foil Colour:</label>
                        <span class="form-control-static">{{job.FoilColour}}</span>
                    </div>
                    <div class="form-group" ng-if="job.BindingOption && job.BindingOption.Name">
                        <label class="control-label">Binding:</label>
                        <span class="form-control-static">{{job.BindingOption.Name}}</span>
                    </div>
                </div>
            </div>

            <!-- Special Options -->
            <div class="row" ng-if="hasSpecialOptions()">
                <div class="col-md-12">
                    <h5><i class="glyphicon glyphicon-cog"></i> Special Options</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group" ng-if="job.RoundOption">
                                <label class="control-label">Round Corners:</label>
                                <span class="form-control-static">{{$root.enums.ValueDesc.RoundOption[job.RoundOption]}}</span>
                            </div>
                            <div class="form-group" ng-if="job.HoleDrilling">
                                <label class="control-label">Hole Drilling:</label>
                                <span class="form-control-static">{{job.NumberOfHoles}} holes</span>
                            </div>
                            <div class="form-group" ng-if="job.Perforating">
                                <label class="control-label">Perforating:</label>
                                <span class="form-control-static">Yes</span>
                            </div>
                            <div class="form-group" ng-if="job.Scoring">
                                <label class="control-label">Scoring:</label>
                                <span class="form-control-static">Yes</span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group" ng-if="job.CustomDieCut">
                                <label class="control-label">Custom Die Cut:</label>
                                <span class="form-control-static">{{job.CustomDieCut}}</span>
                            </div>
                            <div class="form-group" ng-if="job.NumberOfMagnets">
                                <label class="control-label">Magnets:</label>
                                <span class="form-control-static">{{job.NumberOfMagnets}}</span>
                            </div>
                            <div class="form-group" ng-if="job.Envelope">
                                <label class="control-label">Envelope:</label>
                                <span class="form-control-static">{{job.Envelope}}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Special Instructions -->
            <div class="row" ng-if="job.SpecialInstructions">
                <div class="col-md-12">
                    <h5><i class="glyphicon glyphicon-comment"></i> Special Instructions</h5>
                    <div class="well well-sm">
                        <pre>{{job.SpecialInstructions}}</pre>
                    </div>
                </div>
            </div>

            <!-- Dates -->
            <div class="row">
                <div class="col-md-6">
                    <h5><i class="glyphicon glyphicon-calendar"></i> Important Dates</h5>
                    <div class="form-group" ng-if="job.DateCreated">
                        <label class="control-label">Created:</label>
                        <span class="form-control-static">{{job.DateCreated | date:'medium'}}</span>
                    </div>
                    <div class="form-group" ng-if="job.PrintByDate">
                        <label class="control-label">Print By:</label>
                        <span class="form-control-static text-warning">{{job.PrintByDate | date:'medium'}}</span>
                    </div>
                    <div class="form-group" ng-if="job.DispatchDate">
                        <label class="control-label">Dispatch Date:</label>
                        <span class="form-control-static text-info">{{job.DispatchDate | date:'medium'}}</span>
                    </div>
                </div>
                <div class="col-md-6" ng-if="job.CreatedBy">
                    <h5><i class="glyphicon glyphicon-user"></i> Created By</h5>
                    <div class="form-group">
                        <label class="control-label">User:</label>
                        <span class="form-control-static">{{job.CreatedBy.Name}}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
