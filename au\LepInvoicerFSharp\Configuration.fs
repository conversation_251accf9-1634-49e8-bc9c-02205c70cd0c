namespace LepInvoicer<PERSON>harp

open System
open System.IO
open Microsoft.Extensions.Configuration
open FsToolkit.ErrorHandling
open LepInvoicerFSharp.Types

// ============================================================================
// CONFIGURATION MODULE - Functional configuration loading
// ============================================================================

module Configuration =
    
    /// Default configuration values (pure)
    let private defaultConfig : InvoicerConfig = {
        ConnectionString = "Data Source=SRV03;user id=sa; password=*************; Initial Catalog=PRD_AU"
        InvoiceBatchSize = 20
        RefundBatchSize = 10
        CreateOrderInvoice = true
        CreateRefundInvoice = true
        CreatePdfInvoice = true
        TestMode = false
        IgnoreCustomers = [
            "LEP Colour Printers Pty Ltd"
            "LEP Marketing"
            "LEP TEST J"
            "LEP TEST T"
            "lepdemo"
        ]
        MinimumFinishDate = DateTime.Parse("2024/02/01")
        MYOBConfig = {
            DeveloperKey = ""
            DeveloperSecret = ""
            CompanyFileName = "LEP Colour Printers Pty Ltd"
            ConfirmationUrl = ""
        }
    }
    
    /// Parse string to boolean with default (pure function)
    let private parseBool (defaultValue: bool) (value: string) : bool =
        match Boolean.TryParse(value) with
        | (true, result) -> result
        | (false, _) -> defaultValue
    
    /// Parse string to int with default (pure function)
    let private parseInt (defaultValue: int) (value: string) : int =
        match Int32.TryParse(value) with
        | (true, result) -> result
        | (false, _) -> defaultValue
    
    /// Parse string to DateTime with default (pure function)
    let private parseDateTime (defaultValue: DateTime) (value: string) : DateTime =
        match DateTime.TryParse(value) with
        | (true, result) -> result
        | (false, _) -> defaultValue
    
    /// Parse comma-separated string to list (pure function)
    let private parseStringList (value: string) : string list =
        if String.IsNullOrWhiteSpace(value) then
            []
        else
            value.Split(',')
            |> Array.map (fun s -> s.Trim())
            |> Array.filter (fun s -> not (String.IsNullOrWhiteSpace(s)))
            |> Array.toList
    
    /// Safe configuration value getter (pure function)
    let private getConfigValue (config: IConfiguration) (key: string) (defaultValue: string) : string =
        let value = config.[key]
        if String.IsNullOrWhiteSpace(value) then defaultValue else value
    
    /// Load MYOB configuration from IConfiguration (functional)
    let private loadMYOBConfig (config: IConfiguration) : MYOBConfig =
        {
            DeveloperKey = getConfigValue config "MYOB:DeveloperKey" defaultConfig.MYOBConfig.DeveloperKey
            DeveloperSecret = getConfigValue config "MYOB:DeveloperSecret" defaultConfig.MYOBConfig.DeveloperSecret
            CompanyFileName = getConfigValue config "MYOB:CompanyFileName" defaultConfig.MYOBConfig.CompanyFileName
            ConfirmationUrl = getConfigValue config "MYOB:ConfirmationUrl" defaultConfig.MYOBConfig.ConfirmationUrl
        }
    
    /// Load configuration from IConfiguration (functional)
    let private loadFromIConfiguration (config: IConfiguration) : InvoicerConfig =
        {
            ConnectionString = getConfigValue config "ConnectionString" defaultConfig.ConnectionString
            InvoiceBatchSize = getConfigValue config "InvoiceBatchSize" (string defaultConfig.InvoiceBatchSize) |> parseInt defaultConfig.InvoiceBatchSize
            RefundBatchSize = getConfigValue config "RefundBatchSize" (string defaultConfig.RefundBatchSize) |> parseInt defaultConfig.RefundBatchSize
            CreateOrderInvoice = getConfigValue config "CreateOrderInvoice" (string defaultConfig.CreateOrderInvoice) |> parseBool defaultConfig.CreateOrderInvoice
            CreateRefundInvoice = getConfigValue config "CreateRefundInvoice" (string defaultConfig.CreateRefundInvoice) |> parseBool defaultConfig.CreateRefundInvoice
            CreatePdfInvoice = getConfigValue config "CreatePdfInvoice" (string defaultConfig.CreatePdfInvoice) |> parseBool defaultConfig.CreatePdfInvoice
            TestMode = getConfigValue config "TestMode" (string defaultConfig.TestMode) |> parseBool defaultConfig.TestMode
            IgnoreCustomers = 
                let configValue = getConfigValue config "IgnoreCustomers" ""
                if String.IsNullOrWhiteSpace(configValue) then 
                    defaultConfig.IgnoreCustomers 
                else 
                    parseStringList configValue
            MinimumFinishDate = getConfigValue config "MinimumFinishDate" (defaultConfig.MinimumFinishDate.ToString("yyyy-MM-dd")) |> parseDateTime defaultConfig.MinimumFinishDate
            MYOBConfig = loadMYOBConfig config
        }
    
    /// Load configuration from appsettings.json (IO operation)
    let loadFromFile (filePath: string) : Result<InvoicerConfig, string> =
        result {
            try
                if not (File.Exists(filePath)) then
                    return defaultConfig
                else
                    let builder = ConfigurationBuilder()
                    let config =
                        builder
                            .AddJsonFile(filePath, optional = true)
                            .AddEnvironmentVariables()
                            .Build()

                    return loadFromIConfiguration config
            with
            | ex -> return! Error $"Failed to load configuration from {filePath}: {ex.Message}"
        }
    
    /// Load configuration from environment variables and defaults (IO operation)
    let loadFromEnvironment () : Result<InvoicerConfig, string> =
        result {
            try
                let builder = ConfigurationBuilder()
                let config =
                    builder
                        .AddEnvironmentVariables("LEPINVOICER_")
                        .Build()

                return loadFromIConfiguration config
            with
            | ex -> return! Error $"Failed to load configuration from environment: {ex.Message}"
        }
    
    /// Load configuration with fallback strategy (IO operation)
    let load () : Result<InvoicerConfig, string> =
        let configFile = "appsettings.json"
        
        // Try to load from file first, then environment, then defaults
        result {
            let! fileConfig = loadFromFile configFile
            let! envConfig = loadFromEnvironment()
            return
                // Merge configurations (environment overrides file)
                {
                    ConnectionString = if String.IsNullOrWhiteSpace(envConfig.ConnectionString) then fileConfig.ConnectionString else envConfig.ConnectionString
                    InvoiceBatchSize = if envConfig.InvoiceBatchSize = defaultConfig.InvoiceBatchSize then fileConfig.InvoiceBatchSize else envConfig.InvoiceBatchSize
                    RefundBatchSize = if envConfig.RefundBatchSize = defaultConfig.RefundBatchSize then fileConfig.RefundBatchSize else envConfig.RefundBatchSize
                    CreateOrderInvoice = envConfig.CreateOrderInvoice
                    CreateRefundInvoice = envConfig.CreateRefundInvoice
                    CreatePdfInvoice = envConfig.CreatePdfInvoice
                    TestMode = envConfig.TestMode
                    IgnoreCustomers = if List.isEmpty envConfig.IgnoreCustomers then fileConfig.IgnoreCustomers else envConfig.IgnoreCustomers
                    MinimumFinishDate = if envConfig.MinimumFinishDate = defaultConfig.MinimumFinishDate then fileConfig.MinimumFinishDate else envConfig.MinimumFinishDate
                    MYOBConfig = {
                        DeveloperKey = if String.IsNullOrWhiteSpace(envConfig.MYOBConfig.DeveloperKey) then fileConfig.MYOBConfig.DeveloperKey else envConfig.MYOBConfig.DeveloperKey
                        DeveloperSecret = if String.IsNullOrWhiteSpace(envConfig.MYOBConfig.DeveloperSecret) then fileConfig.MYOBConfig.DeveloperSecret else envConfig.MYOBConfig.DeveloperSecret
                        CompanyFileName = if String.IsNullOrWhiteSpace(envConfig.MYOBConfig.CompanyFileName) then fileConfig.MYOBConfig.CompanyFileName else envConfig.MYOBConfig.CompanyFileName
                        ConfirmationUrl = if String.IsNullOrWhiteSpace(envConfig.MYOBConfig.ConfirmationUrl) then fileConfig.MYOBConfig.ConfirmationUrl else envConfig.MYOBConfig.ConfirmationUrl
                    }
                }
        }
    
    /// Validate configuration (pure function)
    let validate (config: InvoicerConfig) : Result<InvoicerConfig, string> =
        let errors = [
            if String.IsNullOrWhiteSpace(config.ConnectionString) then "ConnectionString is required"
            if config.InvoiceBatchSize <= 0 then "InvoiceBatchSize must be positive"
            if config.RefundBatchSize <= 0 then "RefundBatchSize must be positive"
            if config.MinimumFinishDate > DateTime.Now then "MinimumFinishDate cannot be in the future"
        ]

        if List.isEmpty errors then
            Ok config
        else
            Error (String.concat "; " errors)
    
    /// Load and validate configuration (IO operation)
    let loadAndValidate () : Result<InvoicerConfig, string> =
        result {
            let! config = load ()
            return! validate config
        }

// ============================================================================
// CONFIGURATION HELPERS
// ============================================================================

module ConfigHelpers =
    
    /// Create test configuration (pure function)
    let createTestConfig () : InvoicerConfig =
        { Configuration.defaultConfig with
            TestMode = true
            InvoiceBatchSize = 5
            RefundBatchSize = 5
            CreatePdfInvoice = false
        }
    
    /// Create development configuration (pure function)  
    let createDevConfig () : InvoicerConfig =
        { Configuration.defaultConfig with
            TestMode = true
            InvoiceBatchSize = 10
            RefundBatchSize = 5
        }
    
    /// Print configuration summary (side effect)
    let printSummary (config: InvoicerConfig) : unit =
        printfn "=== LEP Invoicer F# Configuration ==="
        printfn "Test Mode: %b" config.TestMode
        printfn "Invoice Batch Size: %d" config.InvoiceBatchSize
        printfn "Refund Batch Size: %d" config.RefundBatchSize
        printfn "Create Order Invoice: %b" config.CreateOrderInvoice
        printfn "Create Refund Invoice: %b" config.CreateRefundInvoice
        printfn "Create PDF Invoice: %b" config.CreatePdfInvoice
        printfn "Minimum Finish Date: %s" (config.MinimumFinishDate.ToString("yyyy-MM-dd"))
        printfn "Ignore Customers: %s" (String.concat ", " config.IgnoreCustomers)
        printfn "MYOB Company: %s" config.MYOBConfig.CompanyFileName
        printfn "======================================="
