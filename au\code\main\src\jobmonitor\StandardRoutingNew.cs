using lep.job;
using lep.run;
using System;
using System.Collections.Generic;
using System.Linq;
using static lep.job.JobStatusOptions;
using static lep.job.JobTypeOptions;

namespace lep.jobmonitor
{
    using JSO = JobStatusOptions;
    using JT = JobTypeOptions;

    /// <summary>
    /// StandardRoutingNew Class
    /// Simplified version of StandardRouting that handles different job paths based on job type and print type.
    /// Non-ganged Digital jobs take a shorter path through the production process.
    /// </summary>
    public sealed class StandardRoutingNew
    {
        // Singleton implementation
        private static readonly Lazy<StandardRoutingNew> _instance = new Lazy<StandardRoutingNew>(() => new StandardRoutingNew());
        public static StandardRoutingNew Instance => _instance.Value;

        private StandardRoutingNew() { }

        #region Routing Tables

        // Common statuses shared by all routes
        private static readonly JSO[] _commonStart = { Open, Submitted, PreflightDone };
        private static readonly JSO[] _commonEnd = { Packed, Dispatched, Complete };

        // Standard offset printing route
        private readonly List<JSO> _standardOffsetRoute = new List<JSO>(_commonStart)
        {
            Filling, LayoutRequired, LayoutDone,
            ApprovedForPlating, PlatingDone, PressDone,
            Celloglazed, Cut, Folded
        }.Concat(_commonEnd).ToList();

	
		// Digital printing route (shorter path for non-ganged digital jobs)
		private readonly List<JSO> _digitalRoute = new List<JSO>(_commonStart)
        {
            DPCPreProduction, DPCPrinted,
            Celloglazed, Cut, Folded
        }.Concat(_commonEnd).ToList();

        // Digital ganged printing route
        private readonly List<JSO> _digitalGangedRoute = new List<JSO>(_commonStart)
        {
            Filling, LayoutRequired, LayoutDone,
			ApprovedForPlating, PlatingDone, PressDone,
			Celloglazed, Cut, Folded
        }.Concat(_commonEnd).ToList();

        // Wide format printing route
        private readonly List<JSO> _wideFormatRoute = new List<JSO>(_commonStart)
        {
            WideFormatProduction,
            WideFormatComplete
        }.Concat(_commonEnd).ToList();

        // Outwork route
        private readonly List<JSO> _outworkRoute = new List<JSO>(_commonStart)
        {
            Outwork
        }.Concat(_commonEnd).ToList();

        #endregion

        #region Skip Conditions

        // Dictionary of predicates to determine if a job can skip a particular status
        private readonly Dictionary<JSO, Predicate<IJob>> _statusCanBeSkipped = new Dictionary<JSO, Predicate<IJob>>
        {
            // Skip finishing steps based on job specifications
            { Celloglazed, j => j.Celloglaze == RunCelloglazeOptions.None },
            { Folded, j => j.FoldedSize == null && !j.IsMagazine() },
            { Stitched, j => !j.IsMagazine() },
            { Letterpressed, j => j.DieCutType == CutOptions.None },
            { Drilled, j => j.HoleDrilling == HoleDrilling.None },
            { Rounded, j => j.RoundOption == RoundOption.None }
        };

        #endregion

        #region Public Methods

        /// <summary>
        /// Gets the next status in the production route for a job
        /// </summary>
        public JSO GetNextRouteForJob(IJob j)
        {
            // Special case for digital ganged jobs at LayoutDone
            if (j.IsDigital() && j.IsDigitalAndRunGanged() && j.Status == LayoutDone)
            {
                return JSO.DPCPreProduction;
            }

            // Handle special cases
            if (j.Status == UnableToMeetPrice || j.Status == RejectedVariation)
            {
                return JSO.Open;
            }

            if (j.Status == Outwork)
            {
                return Packed;
            }

            // Get the appropriate route based on job type
            List<JSO> route = GetRouteForJob(j);

			var status = j.Status;
            // Find the next status in the route
            int currentIndex = route.IndexOf(status);
			if (currentIndex == -1 && status == DPCPrinted)
			{
				status = PressDone;
				// If status not found in route, return the first status
				currentIndex = route.IndexOf(status);
			}
            if (currentIndex < 0)
            {
                // If status not found in route, find the closest status
                var statusValue = (int)status;
                var closestStatus = route.OrderBy(s => Math.Abs((int)s - statusValue)).First();
                currentIndex = route.IndexOf(closestStatus);
            }

            // Get the next status in the route
            int nextIndex = currentIndex + 1;
            if (nextIndex >= route.Count)
            {
                return Complete;
            }

            JSO nextStatus = route[nextIndex];

            // Check if the next status can be skipped
            while (_statusCanBeSkipped.ContainsKey(nextStatus) && _statusCanBeSkipped[nextStatus](j))
            {
                nextIndex++;
                if (nextIndex >= route.Count)
                {
                    return Complete;
                }
                nextStatus = route[nextIndex];
            }

            return nextStatus;
        }

        /// <summary>
        /// Gets all statuses in the production route for a job from Open to Complete
        /// </summary>
        public List<JSO> GetAllRoutesForJob(IJob j)
        {
            // Get the appropriate route based on job type
            List<JSO> route = GetRouteForJob(j);

            // Create a new list to hold the filtered route (skipping statuses that can be skipped)
            var filteredRoute = new List<JSO>();

            // Add each status to the filtered route, skipping those that can be skipped
            foreach (var status in route)
            {
                if (_statusCanBeSkipped.ContainsKey(status) && _statusCanBeSkipped[status](j))
                {
                    continue;
                }

                filteredRoute.Add(status);
            }

            return filteredRoute;
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Gets the appropriate route for a job based on its type and characteristics
        /// </summary>
        private List<JSO> GetRouteForJob(IJob j)
        {
            // Select route based on job type
            if (j.IsWideFormat())
                return _wideFormatRoute;

            if (j.IsOutworkJob())
                return _outworkRoute;

            if (j.IsDigital())
                return j.IsDigitalAndRunGanged() ? _digitalGangedRoute : _digitalRoute;

            // Default to standard offset route
            return _standardOffsetRoute;
        }



        #endregion
    }
}
