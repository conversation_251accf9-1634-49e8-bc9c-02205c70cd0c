<div id="jobBoardPage">
    <div class="board-header">
        <h3 id="facilityx"></h3>
        <div class="alert-counters">
            <span class="alertbox redAlertB">Red <span id="redJobsCount">0</span></span>
            <span class="alertbox amberAlertB">Amber <span id="amberJobsCount">0</span></span>
        </div>
    </div>

    <div class="board-filters">
        <select id="boardFilter" class="form-control">
            <option value="All">All Jobs</option>
            <option value="PreFlight">PreFlight</option>
            <option value="PrePress">PrePress</option>
            <option value="DPCProduction">DPC Production</option>
            <!-- Add other board options -->
        </select>
    </div>

    <table id="jobBoard" class="display compact" style="width:100%">
        <thead>
            <tr>
                <th>Job ID</th>
                <th>Order</th>
                <th>Run</th>
                <th>Customer</th>
                <th>Job Name</th>
                <th>Print By</th>
                <th>Despatch By</th>
                <th>Hours Remaining</th>
                <th>Status</th>
                <th>Job Type</th>
                <th>Qty</th>
                <th>Age</th>
            </tr>
        </thead>
    </table>

    <div class="status-message" id="statusMessage"></div>
</div>

<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/2.0.3/css/dataTables.dataTables.css">
<link rel="stylesheet" href="https://cdn.datatables.net/rowgroup/2.0.0/css/rowGroup.dataTables.css">

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.datatables.net/2.0.3/js/dataTables.js"></script>
<script src="https://cdn.datatables.net/rowgroup/2.0.0/js/dataTables.rowGroup.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js"></script>

<script>
// Configuration
const config = {
    refreshInterval: 30000,
    facility: 'FG',
    apiBase: '/api/jobboard',
    currentVersion: 0,
    authToken: localStorage.getItem('ngStorage-lepToken')?.replace(/"/g, '')
};

// DataTable columns configuration
const columns = [
    { 
        data: 'Id',
        render: (data, type, row) => 
            `<a href="${window.location.origin}/#!/staff/order/${row.OrderId}/job/${data}" target="_blank">${data}</a>`
    },
    { 
        data: 'OrderId',
        render: data => 
            `<a href="${window.location.origin}/#!/staff/order/${data}/open" target="_blank">${data}</a>`
    },
    { 
        data: 'RunId',
        render: data => data !== -1 ? 
            `<a href="${window.location.origin}/#!/staff/run/${data}" target="_blank">${data}</a>` : ''
    },
    { data: 'CustomerName' },
    { data: 'JobName' },
    { 
        data: 'PrintByDate',
        render: data => data ? moment(data).format('DD/MM/YY HH:mm') : ''
    },
    { data: 'DespatchByDate' },
    {
        data: 'HD',
        render: (data, type) => type === 'display' ? formatHours(data) : data
    },
    { data: 'CurrentStatus' },
    { data: 'JobType' },
    { data: 'Quantity', className: 'dt-right' },
    {
        data: 'Age',
        render: (data, type) => type === 'display' ? formatAge(data) : data
    }
];

// Initialize DataTable
const table = $('#jobBoard').DataTable({
    processing: true,
    serverSide: false,
    ajax: {
        url: `${config.apiBase}/get`,
        dataSrc: processData,
        data: { facilityStr: config.facility },
        beforeSend: xhr => xhr.setRequestHeader('Authorization', `Bearer ${config.authToken}`)
    },
    columns,
    order: [[7, 'asc'], [0, 'asc']],
    rowGroup: {
        dataSrc: 'RunId',
        startRender: (rows, group) => 
            `<div class="run-group">Run ${group} (${rows.count()} jobs)</div>`
    },
    createdRow: (row, data) => {
        $(row).addClass(getHealthClass(data.Health));
        if (data.RunIsBC) $(row).addClass('run-bc');
    }
});

// Helper functions
const formatHours = seconds => {
    if (!seconds) return '';
    const duration = moment.duration(Math.abs(seconds), 'seconds');
    return `${seconds < 0 ? '-' : ''}${Math.floor(duration.asHours())}h ${duration.minutes()}m`;
};

const formatAge = days => `${Math.floor(days)} days`;

const getHealthClass = health => {
    switch(health) {
        case 1: return 'redAlert';
        case 2: return 'amberAlert';
        default: return 'noAlert';
    }
};

const processData = data => {
    if (data.version === 0) {
        showMessage('Job board initializing...');
        setTimeout(() => window.location.reload(), 10000);
        return [];
    }
    
    config.currentVersion = data.version;
    updateCounters(data.aaData);
    return data.aaData.map(job => ({
        ...job,
        PrintByDate: job.PrintByDate ? new Date(job.PrintByDate) : null,
        HD: job.HD || calculateHD(job)
    }));
};

const calculateHD = job => {
    if (!job.OrderDispatchEst) return null;
    const despatchTime = new Date(job.OrderDispatchEst).getTime();
    return Math.round((despatchTime - Date.now()) / 1000);
};

// Event handlers
$('#boardFilter').on('change', function() {
    const filter = this.value;
    table.column(8).search(filter === 'All' ? '' : filter).draw();
});

// Auto-refresh logic
setInterval(async () => {
    try {
        const { version } = await fetchVersion();
        if (version > config.currentVersion) {
            table.ajax.reload(null, false);
        }
    } catch (error) {
        showMessage('Error updating job board', 'error');
    }
}, config.refreshInterval);

// Initialization
$(document).ready(() => {
    const facilityMap = { FG: 'Forest Glen' };
    $('#facilityx').text(facilityMap[config.facility]);
    updateCounters([]);
});

// Counter update function
function updateCounters(jobs) {
    const counts = jobs.reduce((acc, job) => {
        acc[job.Health === 1 ? 'red' : job.Health === 2 ? 'amber' : 'other']++;
        return acc;
    }, { red: 0, amber: 0, other: 0 });

    $('#redJobsCount').text(counts.red);
    $('#amberJobsCount').text(counts.amber);
}

function showMessage(text, type = 'info') {
    const status = $('#statusMessage');
    status.removeClass().addClass(`status-message ${type}`).text(text);
}
</script>

<style>
.board-header { display: flex; justify-content: space-between; align-items: center; }
.alert-counters { display: flex; gap: 1rem; }
.run-group { font-weight: bold; background: #f0f0f0; padding: 0.5rem; }
.redAlert { background-color: #ffd4d4 !important; }
.amberAlert { background-color: #fff3cd !important; }
.status-message { padding: 0.5rem; margin-top: 1rem; }
.status-message.info { background: #d4edda; }
.status-message.error { background: #f8d7da; }
</style>