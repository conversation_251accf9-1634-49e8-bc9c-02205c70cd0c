# Test script to verify FastReport templates are deployed correctly
Write-Host "Testing FastReport template deployment..." -ForegroundColor Green

$outputDir = "bin\Debug\net8.0-windows7.0"
$reportsDir = "$outputDir\reports"

Write-Host "Checking output directory: $outputDir" -ForegroundColor Yellow
if (Test-Path $outputDir) {
    Write-Host "OK Output directory exists" -ForegroundColor Green
} else {
    Write-Host "ERROR Output directory missing" -ForegroundColor Red
    exit 1
}

Write-Host "Checking reports directory: $reportsDir" -ForegroundColor Yellow
if (Test-Path $reportsDir) {
    Write-Host "OK Reports directory exists" -ForegroundColor Green
} else {
    Write-Host "ERROR Reports directory missing" -ForegroundColor Red
    exit 1
}

$orderTemplate = "$reportsDir\lep-invoice-order.frx"
$refundTemplate = "$reportsDir\lep-invoice-refund.frx"

Write-Host "Checking order template: $orderTemplate" -ForegroundColor Yellow
if (Test-Path $orderTemplate) {
    $size = (Get-Item $orderTemplate).Length
    Write-Host "OK Order template exists ($size bytes)" -ForegroundColor Green
} else {
    Write-Host "ERROR Order template missing" -ForegroundColor Red
    exit 1
}

Write-Host "Checking refund template: $refundTemplate" -ForegroundColor Yellow
if (Test-Path $refundTemplate) {
    $size = (Get-Item $refundTemplate).Length
    Write-Host "OK Refund template exists ($size bytes)" -ForegroundColor Green
} else {
    Write-Host "ERROR Refund template missing" -ForegroundColor Red
    exit 1
}

Write-Host "All FastReport templates deployed successfully!" -ForegroundColor Green
Write-Host "Templates are now deployed relative to the application binary instead of hardcoded paths." -ForegroundColor Cyan
