namespace LepInvoicerFSharp

open System

// ============================================================================
// CORE DOMAIN TYPES - Pure functional types
// ============================================================================

type CreditType = 
    | Credit
    | Refund
    | Adjustment
    | Other

type Customer = {
    Id: int
    Name: string
    Username: string
}

type Job = {
    Id: int
    OrderId: int
    Description: string
    Price: decimal option
    IsEnabled: bool
}

type Order = {
    Id: int
    OrderNumber: string
    Customer: Customer
    Jobs: Job list
    FinishDate: DateTime option
    SubmissionDate: DateTime
    PurchaseOrder: string option
    PromotionBenefit: decimal
    PickUpCharge: decimal
    GST: decimal
    IsInvoiced: bool
}

type OrderCredit = {
    Id: int
    OrderId: int
    CustomerId: int
    CreditType: CreditType
    Amount: decimal
    Description: string
    IsInvoiced: bool
    DateCreated: DateTime
}

type ProcessingStats = {
    OrdersProcessed: int
    OrdersSuccessful: int
    OrdersFailed: int
    CreditsProcessed: int
    RefundsProcessed: int
    ElapsedTime: TimeSpan
    Errors: string list
}

type InvoicerConfig = {
    ConnectionString: string
    InvoiceBatchSize: int
    RefundBatchSize: int
    MinimumFinishDate: DateTime
    IgnoreCustomers: string list
    TestMode: bool
    MYOBConfig: {|
        CompanyFileName: string
        Username: string
        Password: string
        ConfirmationUrl: string
    |}
}

// ============================================================================
// PURE BUSINESS LOGIC - No side effects
// ============================================================================

module OrderLogic =
    
    /// Calculate total price for an order (pure function)
    let calculateTotalPrice (order: Order) : decimal option =
        let jobTotal = 
            order.Jobs
            |> List.choose (fun job -> if job.IsEnabled then job.Price else None)
            |> List.sum
        
        if jobTotal > 0m then
            Some (jobTotal + order.PromotionBenefit + order.PickUpCharge + order.GST)
        else
            None
    
    /// Check if order is ready for invoicing (pure function)
    let isReadyForInvoicing (config: InvoicerConfig) (order: Order) : bool =
        let hasValidPrice = calculateTotalPrice order |> Option.isSome
        let isNotIgnored = not (List.contains order.Customer.Username config.IgnoreCustomers)
        let isFinished = 
            match order.FinishDate with
            | Some finishDate -> finishDate >= config.MinimumFinishDate
            | None -> false
        let isNotAlreadyInvoiced = not order.IsInvoiced
        
        hasValidPrice && isNotIgnored && isFinished && isNotAlreadyInvoiced

module CreditLogic =
    
    /// Check if credit is ready for invoicing (pure function)
    let isReadyForInvoicing (config: InvoicerConfig) (credit: OrderCredit) : bool =
        let hasValidAmount = credit.Amount > 0m
        let isNotAlreadyInvoiced = not credit.IsInvoiced
        
        hasValidAmount && isNotAlreadyInvoiced

// ============================================================================
// FUNCTIONAL ERROR HANDLING - Simple Result type
// ============================================================================

type AsyncResult<'T, 'Error> = Async<Result<'T, 'Error>>

module AsyncResult =
    
    let retn (value: 'T) : AsyncResult<'T, 'Error> =
        async { return Ok value }
    
    let bind (f: 'T -> AsyncResult<'U, 'Error>) (asyncResult: AsyncResult<'T, 'Error>) : AsyncResult<'U, 'Error> =
        async {
            let! result = asyncResult
            match result with
            | Ok value -> return! f value
            | Error error -> return Error error
        }
    
    let map (f: 'T -> 'U) (asyncResult: AsyncResult<'T, 'Error>) : AsyncResult<'U, 'Error> =
        async {
            let! result = asyncResult
            return Result.map f result
        }
    
    let mapError (f: 'Error -> 'Error2) (asyncResult: AsyncResult<'T, 'Error>) : AsyncResult<'T, 'Error2> =
        async {
            let! result = asyncResult
            return Result.mapError f result
        }

// ============================================================================
// CONFIGURATION HELPERS
// ============================================================================

module ConfigHelpers =
    
    /// Create default configuration
    let defaultConfig = {
        ConnectionString = ""
        InvoiceBatchSize = 50
        RefundBatchSize = 20
        MinimumFinishDate = DateTime.Now.AddDays(-30.0)
        IgnoreCustomers = []
        TestMode = false
        MYOBConfig = {|
            CompanyFileName = ""
            Username = ""
            Password = ""
            ConfirmationUrl = ""
        |}
    }
    
    /// Print configuration summary
    let printSummary (config: InvoicerConfig) =
        printfn "Configuration:"
        printfn "  Test Mode: %b" config.TestMode
        printfn "  Invoice Batch Size: %d" config.InvoiceBatchSize
        printfn "  Refund Batch Size: %d" config.RefundBatchSize
        printfn "  Minimum Finish Date: %s" (config.MinimumFinishDate.ToString("yyyy-MM-dd"))
        printfn "  Ignored Customers: %d" (List.length config.IgnoreCustomers)
        printfn "  MYOB Company File: %s" config.MYOBConfig.CompanyFileName
