namespace LepInvoicer;

public class Program
{
	public static int Main(string[] args)
	{
		return new Invoicer().RunInvoicer();
	}
}
public class Invoicer
{
	const int INVOICE_BATCH_SIZE = 20;
	const int REFUND_BATCH_SIZE = 10;
	bool dev = true;
	string LEPConnectionString = "Data Source=SRV03;user id=sa; password=*************; Initial Catalog=PRD_AU";
	const string format = "yyyy-MM-dd HH:mm:ss";
	string DataDirectoryFullName = @"\\dfs01\resource";
	string pdfFolder = @"\\dfs01\resource\invoices";

	bool createOrderInvoice = true;
	bool createRefundInvoice = true;

	bool createPdfInvoice = true;
	bool emailPdfInvoice = true;
	string emailPdfAddress = null;
	SqlConnection lepConn;

	NHibernate.ISession LepSession = null;

	ApiConfiguration configuration = null;
	CompanyFile cf = null;
	CompanyFileCredentials creds = null;

	InvoiceService invoiceService = null;
	ServiceInvoiceService serviceInvoiceService = null;
	CustomerService customerService = null;
	ItemService itemsService = null;
	TaxCodeService taxCodeService = null;
	AccountService accountService = null;
	JobService jobService = null;
	EmployeeService employeeService = null;

	TaxCode gstTaxCode = null;
	TaxCodeLink gst = null;
	Account freightAccount = null;
	AccountLink freightAccountLink = null;

	SmtpClient emailer = null;
	Dictionary<string, Account> accounts = null;

	void wait()
	{
		System.Threading.Thread.Sleep(150);
	}

	public int RunInvoicer()
	{
		try
		{
			Console.WriteLine("\n\n\nStarting invoicer " + DateTime.Now.ToString());

			SetupSMTPEmail();

			FastReport.Utils.Config.FontListFolder = "C:\\LEPDATA\\FONTS";
			//FastReport.Utils.Config.ReportSettings.ShowProgress = false;
			LepSession = SetupNHIbernate();
			IMapper _mapper = SetupAutoMapper();

			LepExecuteSQL(@"Update [order] set invoiced2 = null where  invoiced2 = 'F';delete from Invoicer2Log where [Success]= 'N';"); //	

			var ignoreCusts = new List<string>(){
						"LEP Colour Printers Pty Ltd",
						"LEP Marketing",
						"LEP TEST J",
						"LEP TEST T",
						"lepdemo"};

			var lepSqls = new StringBuilder();
			var ordersInvoiced = new List<int>();
			var ordersListToInvoice = LepSession.Query<IOrder>().Fetch(x => x.Jobs)
						.Where(o => !ignoreCusts.Contains(o.Customer.Name))
						.Where(o => o.Invoiced2 == null || (o.Invoiced2 != "Y" && o.Invoiced2 != "F" && o.Invoiced2 != "C"))
						.Where(o => o.FinishDate != null && o.FinishDate.Value.Year != 1)
						.Where(o => o.FinishDate.Value.Date >= DateTime.Parse("2024/02/01"))
						//.Where(o => !new[] {x,y,z}.Contains(o.Id))
						.OrderBy(o => o.Id)
						.Select(o => new KeyValuePair<int, string>(o.Id, o.Customer.Username))
							.Take(INVOICE_BATCH_SIZE).ToList();


			Console.WriteLine("Found " + ordersListToInvoice.Count().ToString() + " orders");
			//ordersListToInvoice.Dump();

			if (!ordersListToInvoice.Any())
			{
				//goto Refunds;
			}


			wait();

			ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12 | SecurityProtocolType.Tls11;

			ApiConfiguration configuration = new ApiConfiguration(MYOBModule.DeveloperKey, MYOBModule.DeveloperSecret, MYOBModule.MYOBConfirmationUrl);

			OAuthService oauthService = new OAuthService(configuration);

			var keystore = new OAuthKeyService2();
			OAuthTokens tokens;
			if (keystore.OAuthResponse != null)
			{
				tokens = keystore.OAuthResponse;
			}
			else
			{
				tokens = oauthService.GetTokens(OAuthLogin.GetAuthorizationCode(configuration));
				keystore.OAuthResponse = tokens;
			}


			CompanyFileService cfService = new CompanyFileService(configuration, null, keystore);
			CompanyFile[] companyFiles = cfService.GetRange();


			wait();

			CompanyFile cf = companyFiles.First(c => c.Name.Contains("LEP Colour Printers Pty Ltd"));

			var itemInvoiceService = new myob.Sale.ItemInvoiceService(configuration, null, keystore);
			invoiceService = new myob.Sale.InvoiceService(configuration, null, keystore);
			serviceInvoiceService = new myob.Sale.ServiceInvoiceService(configuration, null, keystore);
			customerService = new myob.Contact.CustomerService(configuration, null, keystore);
			itemsService = new myob.Inventory.ItemService(configuration, null, keystore);
			taxCodeService = new myob.GeneralLedger.TaxCodeService(configuration, null, keystore);
			accountService = new myob.GeneralLedger.AccountService(configuration, null, keystore);
			jobService = new myob.GeneralLedger.JobService(configuration, null, keystore);
			employeeService = new myob.Contact.EmployeeService(configuration, null, keystore);


			creds = null;

			accounts = accountService.GetRange(cf, "", creds).Items.OrderBy(_ => _.DisplayID).ToDictionary(_ => _.DisplayID);
			wait();
			AccountLink GetAccountLinkFromJob(IJob job)
			{
				var id = GetAccountDisplayIdFromJob(job);
				var x = accounts[id];
				return new AccountLink() { UID = x.UID };
			}

			// get GST code
			gstTaxCode = taxCodeService.GetRange(cf, $"$filter=Code eq 'GST'", creds).Items.FirstOrDefault();
			wait();
			gst = new TaxCodeLink() { UID = gstTaxCode.UID };


			#region obtain Fright account
			freightAccount = accountService.GetRange(cf, $"$filter=Name eq 'Freight recovered'", creds).Items.FirstOrDefault();
			wait();
			freightAccountLink = new AccountLink() { UID = freightAccount.UID };
			#endregion


			#region obtain Discount/promot account
			var discountsAccount = accountService.GetRange(cf, $"$filter=Name eq 'Discounts Allowed'", creds).Items.FirstOrDefault();
			wait();
			var _discountsAccountLink = new AccountLink() { UID = discountsAccount.UID };
			#endregion


			//	
			//	if(!ordersListToInvoice.Any()){
			//		createOrderInvoice = false;
			//	}
			//==============

			var watch0 = System.Diagnostics.Stopwatch.StartNew();

			var orderstoSkip = new List<int>();

			var cu = 0;
			var errorMsg = "";
			foreach (var oId in ordersListToInvoice)
			{

				if (orderstoSkip.Contains(oId.Key))
				{
					Console.WriteLine($"\n skipping order {oId.Key} ");
					continue;
				}


				try
				{

					var filterOldInvs = $"Number eq 'O{oId.Key}' "; wait();
					var oldInvs = serviceInvoiceService.GetRange(cf, $"$filter={filterOldInvs}", creds);

					if (oldInvs.Items.Any())
					{
						foreach (var oldInv in oldInvs.Items)
						{

							try
							{
								Console.WriteLine($"Deleting old invoice {oldInv.Number}");
								wait();
								itemInvoiceService.Delete(cf, oldInv.UID, creds);
							}
							catch (Exception ex)
							{
								continue;
							}

						}
					}



				}
				catch (Exception ex)
				{
					continue;
				}



				errorMsg = "";
				var watch = System.Diagnostics.Stopwatch.StartNew();
				var o = LepSession.Query<IOrder>()
				.Where(_ => _.Id == oId.Key).First();

				decimal orderPrice = 0.0m;
				try
				{
					orderPrice = decimal.Round(o.PriceOfJobs.Value, 2);
				}
				catch (Exception ex)
				{
					Console.WriteLine(ex.Message);

					continue;
				}

				if (orderPrice == 0)
				{
					LepExecuteSQL($@"UPDATE [Order] SET Invoiced2 = 'Y' where Id = {o.Id} ;");
					continue;
				}

				string invoiceNumber = "O" + o.Id.ToString();

				var orderInvoice = new ServiceInvoice();
				string x = string.Empty;
				try
				{
					Console.Write($"\n{++cu}. Order {o.Id}, {o.FinishDate.Value.ToShortDateString()},   {o.Jobs.Count} Jobs, Total {orderPrice}");


					#region Create a ServiceInvoice header for this order


					orderInvoice.Number = invoiceNumber;
					orderInvoice.CustomerPurchaseOrderNumber = TruncateLongString(o.PurchaseOrder ?? "", 20);
					orderInvoice.InvoiceDeliveryStatus = DocumentAction.PrintAndEmail;
					orderInvoice.InvoiceType = InvoiceLayoutType.Service;
					orderInvoice.Subtotal = orderPrice;
					orderInvoice.TotalAmount = orderPrice;
					orderInvoice.BalanceDueAmount = orderPrice;
					orderInvoice.IsTaxInclusive = false;
					orderInvoice.Date = o.FinishDate.Value;

					#endregion

					#region Lookup Customer from MYOB by MYOB.DisplayId = LepUsername
					Customer myobCustomer = null;
					if (!string.IsNullOrEmpty(o.Customer.MyobUid))
					{
						orderInvoice.Customer = new CardLink() { UID = Guid.Parse(o.Customer.MyobUid) };
					}
					else
					{
						var userName = WebUtility.UrlEncode(o.Customer.Username);
						var custFilter = $"$filter=DisplayID  eq '{userName}' ";
						myobCustomer = customerService.GetRange(cf, custFilter, creds).Items.FirstOrDefault();
						wait();
						if (myobCustomer == null)
						{
							var msg = $" can not find customer '{o.Customer.Name}' -- '{o.Customer.Username}'";
							Console.WriteLine(msg);
							//myobCustomer = CreateMYOBCustomerFromLEPCustomer(o.Customer);
							LepExecuteSQL($@"UPDATE [Order] SET Invoiced2 = 'C' where Id = {o.Id} ;");
							continue;
						}
						orderInvoice.Customer = new CardLink() { UID = myobCustomer.UID };
					}





					#endregion

					var lines = new List<ServiceInvoiceLine>();

					// Add Jobs
					#region add jobs
					foreach (var j in o.Jobs)
					{
						var jTemplateName = j.Template.Name;
						jTemplateName = jTemplateName.Replace("same day dispatch", "SDD");
						jTemplateName = jTemplateName.Replace("next day dispatch", "NDD");

						var invLine = new ServiceInvoiceLine
						{
							Description = $"J{j.Id} / {jTemplateName} / {j.PrintType.ToDescription()} / {j.FinishedSize?.PaperSize?.Name ?? ""} / {j.Stock.Name} / Qty: {j.Quantity}",
							Total = decimal.Round(decimal.Parse(j.Price), 2),
							TaxCode = gst,
							Account = GetAccountLinkFromJob(j)
						};
						lines.Add(invLine);
					}
					#endregion

					#region Add Freight Line for invoice
					var freightPrice = decimal.Round(o.PackDetail?.Price ?? 0m, 2);
					if (freightPrice > 0m)
					{
						var invLine = new ServiceInvoiceLine
						{
							Account = freightAccountLink,
							Description = $"{o.Courier}",
							Total = freightPrice,
							TaxCode = gst
						};
						lines.Add(invLine);
					}
					#endregion


					#region Add Promotion Line for invoice
					if (o.Promotion != null && o.PromotionBenefit > 0)
					{
						var invLine = new ServiceInvoiceLine();
						invLine.Account = _discountsAccountLink;
						invLine.Description = $"{o.Promotion.PromotionCode} - {o.Promotion.ShortDescription}";
						invLine.Total = -1 * decimal.Round(o.PromotionBenefit, 2);
						invLine.TaxCode = gst;
						lines.Add(invLine);
					}
					#endregion

					orderInvoice.Lines = lines;
					orderInvoice.JournalMemo = "I2- " + o.Customer.Name;
					var justCreatedInvoice = serviceInvoiceService.InsertEx(cf, orderInvoice, creds);
					Console.Write($" Success. ");
					ordersInvoiced.Add(o.Id);
					// log success full myob invoice creation
					LepExecuteSQL($@"Insert into Invoicer2Log ([OrderId],[JobCount],[Total],[FinishDate],[Success],[Details] ,[DateCreated]) VALUES 
				({o.Id}, {o.Jobs.Count}, {orderPrice}, '{o.FinishDate.Value.ToString(format)}', 'Y', null, '{DateTime.Now.ToString(format)}');");



					if (createPdfInvoice)
					{
						var orderDto = _mapper.Map<OrderInvoiceDto>(o);
						var jsonStr = JsonConvert.SerializeObject(orderDto, Newtonsoft.Json.Formatting.Indented);

						jsonStr = jsonStr.Replace("'", "''");
						//jsonStr.Dump();
						FastReport.Report reportOrderInvoice = new FastReport.Report();
						var fileName = @"C:\LepData\Labels2\lep-invoice-order.frx";
						reportOrderInvoice.Load(fileName);

						reportOrderInvoice.Dictionary.Connections[0].ConnectionString = "Json='" + jsonStr + "'";
						reportOrderInvoice.Prepare();
						var d = o.FinishDate.Value.Date.ToString("yyyy/MMM/dd").Replace(".", "");

						var dir = $@"{pdfFolder}\{d}";
						if (!Directory.Exists(dir))
							Directory.CreateDirectory(dir);

						var fileDownloadName = $@"{dir}/O{o.Id}.pdf";
						var fileDownloadName2 = "";
						var pdfExport = new PDFSimpleExport();
						var htmlExport = new HTMLExport();




						//pdfExport.AllowModify = true;

						using (var fs1 = new System.IO.FileStream(fileDownloadName, FileMode.Create))
						{
							pdfExport.Export(reportOrderInvoice, fs1);
						};





						var orderExtraFolder = $@"{DataDirectoryFullName}\orders\{o.DateCreated:yyyyMMdd}\{o.OrderNr}\Extrafiles";
						if (!Directory.Exists(orderExtraFolder))
							Directory.CreateDirectory(orderExtraFolder);

						var y = Path.Combine(orderExtraFolder, $"O{o.Id}.pdf");
						//y.Dump();
						File.Copy(fileDownloadName, y, true);

						//y = Path.Combine(orderExtraFolder, $"O{o.Id}.thumb.jpg");
						//File.Copy(fileDownloadName2, y, true);

						if (emailPdfInvoice)
						{

							var address = o.Customer.AccountEmail;

							if (string.IsNullOrEmpty(address))
							{
								address = o.Customer.Email;
							}

							if (!string.IsNullOrEmpty(emailPdfAddress))
							{
								address = emailPdfAddress;
							}

							if (!string.IsNullOrEmpty(address))
							{
								var mail = createEmail();
								mail.Subject = $"Invoice {invoiceNumber}";


								mail.To.Add(new MailAddress(address));
								mail.Attachments.Add(new Attachment(fileDownloadName));

								mail.IsBodyHtml = true;
								var body = $@"Please find attached your invoice {invoiceNumber} from LEP Colour Printers.<br/>
										 Contact <EMAIL> for any queries.";
								mail.Body = body;
								emailer.Send(mail);
							}
						}
					}
				}
				catch (ApiCommunicationException ex)
				{
					//ex.dump();
					//orderInvoice.Dump();

					var errrs = ex.Message;

					if (ex.Errors != null)
					{
						errrs += "\n" +
						String.Join(", ", ex.Errors.Select(_ => _.Message).ToArray());
					}


					errorMsg = errrs;
					errorMsg = errorMsg.Replace("'", "''");
					Console.Write($" Fail. " + errorMsg);
					LepExecuteSQL($@"Insert into Invoicer2Log ([OrderId],[JobCount],[Total],[FinishDate],[Success],[Details] ,[DateCreated]) VALUES 
				({o.Id}, {o.Jobs.Count}, {orderPrice},  '{o.FinishDate.Value.ToString(format)}', 'N', '{errorMsg}', '{DateTime.Now.ToString(format)}');
				UPDATE [Order] SET Invoiced2 = 'F' , Invoiced2Details = '{errorMsg}' Where Id = {o.Id};
				
				");
					continue;
				}
				catch (Exception ex)
				{
					Console.Write($" Fail. {ex.Message} \n {ex.StackTrace}");

					errorMsg = ex.Message.Replace("'", "''");
					Console.Write($" Fail. " + errorMsg);
					LepExecuteSQL($@"Insert into Invoicer2Log 
											([OrderId],[JobCount],[Total],[FinishDate]
											,[Success],[Details] ,[DateCreated]) 
										VALUES 
											({o.Id}, {o.Jobs.Count}, {orderPrice}, '{o.FinishDate.Value.ToString(format)}',
											'N', '{errorMsg}', '{DateTime.Now.ToString(format)}');
										UPDATE [Order] SET Invoiced2 = 'F' , Invoiced2Details = '{errorMsg}' Where Id = {o.Id};");

					continue;
				}

				LepExecuteSQL($@"UPDATE [Order] SET Invoiced2 = 'Y' where Id = {o.Id} ;");

				//System.Threading.Thread.Sleep(3000);

			}

			CreateCreditInvoices(_mapper, cf, itemInvoiceService, accounts);

			CreateRefundInvoices(_mapper, cf, itemInvoiceService, accounts);
			CleanupInvoicerReports();



			LepSession.Flush();
			LepSession.Close();

			Console.WriteLine("Finishing invoicer " + DateTime.Now.ToString());

			return 0;
		}
		catch (Exception ex)
		{
			Console.WriteLine(ex.Message);
			Console.WriteLine(ex.StackTrace.ToString());
			return 1;
		}

		void CreateRefundInvoices(IMapper _mapper, CompanyFile cf, ItemInvoiceService itemInvoiceService, Dictionary<string, Account> accounts)
		{
			string errorMsg = "";
			if (!createRefundInvoice)
			{
				return;
			}

			// get the list of 10 last refund notes
			var ordersListToInvoice2 = LepSession.Query<OrderCredit>().Fetch(_ => _.Customer).Where(o => !o.Invoiced && o.Type == "S").Take(10).ToList();
			if (!ordersListToInvoice2.Any())
			{
				return;
			}

			foreach (OrderCredit oc in ordersListToInvoice2)
			{
				string invoiceNumber = "";
				invoiceNumber = oc.Type + oc.Customer.Id.ToString() + oc.Id.ToString();

				var watch2 = System.Diagnostics.Stopwatch.StartNew();
				var filterOldInvs = $"Number eq '{invoiceNumber}' ";
				var oldInvs = serviceInvoiceService.GetRange(cf, $"$filter={filterOldInvs}", creds);
				wait();
				if (oldInvs.Items.Any())
				{

					try
					{
						foreach (var oldInv in oldInvs.Items)
						{
							Console.WriteLine($"Deleting old invoice {oldInv.Number}");
							itemInvoiceService.Delete(cf, oldInv.UID, creds);
						}
					}
					catch (Exception ex)
					{
						//ex.Dump();
						Console.WriteLine($"Error Deleting old credit invoice  {oc.Type}{oc.Order.Id}");
						continue;
					}
				}
				Console.WriteLine($"{watch2.Elapsed.TotalSeconds} to delete old invoices");


				var errorMsg2 = "";


				errorMsg = "";
				var watch = System.Diagnostics.Stopwatch.StartNew();

				var invAmt = oc.Amount;
				var miscInvoice = new ServiceInvoice();
				string x = string.Empty;
				try
				{
					Console.Write($"\n{invoiceNumber}, {oc.DateCreated.ToShortDateString()},  total {invAmt}");

					#region Create a ServiceInvoice header for this order
					miscInvoice.Number = invoiceNumber;
					miscInvoice.CustomerPurchaseOrderNumber = TruncateLongString(invoiceNumber, 20);
					miscInvoice.InvoiceDeliveryStatus = DocumentAction.PrintAndEmail;
					miscInvoice.InvoiceType = InvoiceLayoutType.Service;
					miscInvoice.Subtotal = invAmt;
					miscInvoice.TotalAmount = invAmt;
					miscInvoice.BalanceDueAmount = invAmt;
					miscInvoice.IsTaxInclusive = false;
					miscInvoice.Date = oc.DateCreated;

					#endregion

					#region Lookup Customer from MYOB by MYOB.DisplayId = LepUsername
					Customer myobCustomer = null;
					if (!string.IsNullOrEmpty(oc.Customer.MyobUid))
					{
						miscInvoice.Customer = new CardLink() { UID = Guid.Parse(oc.Customer.MyobUid) };
					}
					else
					{
						var userName = WebUtility.UrlEncode(oc.Customer.Username);
						var custFilter = $"$filter=DisplayID  eq '{userName}' ";
						myobCustomer = customerService.GetRange(cf, custFilter, creds).Items.FirstOrDefault();
						wait();
						if (myobCustomer == null)
						{
							var msg = $" can not find customer '{oc.Customer.Name}' -- '{oc.Customer.Username}'";
							throw new Exception(msg);
						}
						miscInvoice.Customer = new CardLink() { UID = myobCustomer.UID };
					}


					#endregion

					var lines = new List<ServiceInvoiceLine>();



					var invLine = new ServiceInvoiceLine();
					invLine.Description = $"{oc.Description}";
					var jobPrice = invAmt;

					if (!String.IsNullOrEmpty(oc.Account))
					{
						var x2 = accounts[oc.Account];
						invLine.Account = new AccountLink() { UID = x2.UID }; ;
					}

					invLine.Total = jobPrice;
					invLine.TaxCode = gst;

					lines.Add(invLine);


					miscInvoice.Lines = lines;
					miscInvoice.JournalMemo = "I2 " + oc.Customer.Name;
					var justCreatedInvoice = serviceInvoiceService.InsertEx(cf, miscInvoice, creds);


					Console.Write($" Success. ");

					// log success full myob invoice creation
					//LepExecuteSQL($@"Insert into Invoicer2Log ([OrderId],[JobCount],[Total],[FinishDate],[Success],[Details] ,[DateCreated]) VALUES 
					//({oc.Id}, {1}, {jobPrice}, '{oc.DateCreated}', 'Y', null, '{DateTime.Now.ToString(format)}');");



					#region Email
					if (createPdfInvoice)
					{
						var orderDto = _mapper.Map<CustomerRefundDto>(oc);
						var jsonStr = JsonConvert.SerializeObject(orderDto, Newtonsoft.Json.Formatting.Indented);
						jsonStr = jsonStr.Replace("'", "''");
						//jsonStr.Dump();

						FastReport.Report reportOrderInvoice = new FastReport.Report();
						var fileName = @"c:\LepData\Labels2\lep-invoice-refund.frx";
						reportOrderInvoice.Load(fileName);

						reportOrderInvoice.Dictionary.Connections[0].ConnectionString = "Json='" + jsonStr + "'";
						reportOrderInvoice.Prepare();
						var d = $@"Customers\{oc.Customer.Id}";

						var dir = $@"{pdfFolder}\{d}";
						if (!Directory.Exists(dir))
							Directory.CreateDirectory(dir);

						var miscInvPdfName = $@"{dir}/{invoiceNumber}.pdf";
						var pdfExport = new PDFSimpleExport();
						var htmlExport = new HTMLExport();

						//pdfExport.AllowModify = true;

						using (var fs1 = new System.IO.FileStream(miscInvPdfName, FileMode.Create))
						{
							pdfExport.Export(reportOrderInvoice, fs1);
						};


						//						if (oc.Type == "C" || oc.Type == "M")
						//						{
						//							var destFolder = $@"{DataDirectoryFullName}\orders\{o.DateCreated:yyyyMMdd}\{o.OrderNr}\Extrafiles";
						//
						//							if (!Directory.Exists(destFolder))
						//								Directory.CreateDirectory(destFolder);
						//							File.Copy(miscInvPdfName, Path.Combine(destFolder, $"{invoiceNumber}.pdf"), true);
						//						}


						//using (var ms = new System.IO.FileStream(fileDownloadName+".html", FileMode.Create)) {
						//	htmlExport.Export(reportOrderInvoice, ms);
						//};
						if (false && emailPdfInvoice)
						{
							var address = oc.Customer.AccountEmail;

							if (string.IsNullOrEmpty(address))
							{
								address = oc.Customer.Email;
							}

							if (!string.IsNullOrEmpty(emailPdfAddress))
							{
								address = emailPdfAddress;
							}

							if (!string.IsNullOrEmpty(address))
							{
								var mail = createEmail();
								mail.Subject = $"Invoice {invoiceNumber}";
								mail.To.Add(new MailAddress(address));
								mail.Attachments.Add(new Attachment(miscInvPdfName));

								mail.IsBodyHtml = true;

								var body = $@"Please find attached your invoice {invoiceNumber} from LEP Colour Printers.<br/>
										 Contact <EMAIL> for any queries.";

								mail.Body = body;
								try
								{
									emailer.Send(mail);
								}
								catch (Exception ex)
								{
									Console.WriteLine("Error sending email to : " + address);
								}


							}
						}

					}
					#endregion
				}
				catch (ApiCommunicationException ex)
				{
					Console.WriteLine(ex.Message);
					//orderInvoice.Dump();

					var errrs = ex.Message;

					if (ex.Errors != null)
					{
						errrs += "\n" +
						String.Join(", ", ex.Errors.Select(_ => _.Message).ToArray());
					}


					errorMsg = errrs;
					errorMsg = errorMsg.Replace("'", "''");
					Console.Write($" Fail. " + errorMsg);
					//LepExecuteSQL($@"Insert into Invoicer2Log ([OrderId],[JobCount],[Total],[FinishDate],[Success],[Details] ,[DateCreated]) VALUES 
					//({o.Id}, {o.Jobs.Count}, {invAmt},  '{o.FinishDate.Value.ToString(format)}', 'N', '{errorMsg}', '{DateTime.Now.ToString(format)}');
					//UPDATE [OrderCredit] SET Invoiced2 = 'F' , Invoiced2Details = '{errorMsg}' Where Id = {oc.Id};
					//");

					continue;
				}
				catch (Exception ex)
				{
					Console.Write($" Fail. {ex.Message} \n {ex.StackTrace}");

					errorMsg = ex.Message.Replace("'", "''");
					Console.Write($" Fail. " + errorMsg);
					//LepExecuteSQL($@"Insert into Invoicer2Log ([OrderId],[JobCount],[Total],[FinishDate],[Success],[Details] ,[DateCreated]) VALUES 
					//({o.Id}, {o.Jobs.Count}, {invAmt}, '{o.FinishDate.Value.ToString(format)}', 'N', '{errorMsg}', '{DateTime.Now.ToString(format)}');
					//UPDATE [OrderCredit] SET Invoiced2 = 'F' , Invoiced2Details = '{errorMsg}' Where Id = {oc.Id};");

					continue;
				}

				LepExecuteSQL($@"UPDATE [OrderCredit] SET Invoiced = 'Y' where Id = {oc.Id} ;");


			}

		}
	}

	private static IMapper SetupAutoMapper()
	{
		#region automapper

		var lepProfile = new LepCore.Setup.LepAutoMapperProfile(() => null);
		var config = new MapperConfiguration(cfg => { cfg.AddProfile(lepProfile); });


		var _mapper = config.CreateMapper();

		//Mapper.Initialize(cfg =>
		//{
		//	cfg.AddProfile(lepProfile);
		//});

		#endregion
		return _mapper;
	}

	private NHibernate.ISession SetupNHIbernate()
	{

		#region Setup LEP DL
		var nhconfig = new NHibernate.Cfg.Configuration();
		nhconfig.SetProperty(NHibernate.Cfg.Environment.Dialect, typeof(NHibernate.Dialect.MsSql2008Dialect).AssemblyQualifiedName);
		nhconfig.SetProperty(NHibernate.Cfg.Environment.ConnectionDriver, typeof(NHibernate.Driver.SqlClientDriver).AssemblyQualifiedName);
		nhconfig.SetProperty(NHibernate.Cfg.Environment.Isolation, "ReadCommitted");
		nhconfig.SetProperty(NHibernate.Cfg.Environment.UseSecondLevelCache, false.ToString());
		nhconfig.SetProperty(NHibernate.Cfg.Environment.ShowSql, false.ToString());
		nhconfig.SetProperty(NHibernate.Cfg.Environment.ConnectionProvider, typeof(NHibernate.Connection.DriverConnectionProvider).AssemblyQualifiedName);
		nhconfig.SetProperty(NHibernate.Cfg.Environment.ConnectionString, LEPConnectionString);
		nhconfig.SetProperty(NHibernate.Cfg.Environment.CurrentSessionContextClass, "www");
		nhconfig.SetProperty(NHibernate.Cfg.Environment.BatchSize, "1000");
		//nhconfig.SetProperty(NHibernate.Cfg.Environment.ShowSql, "true");
		//nhconfig.SetProperty(NHibernate.Cfg.Environment.FormatSql, "true");
		nhconfig.AddAssembly(typeof(ICustomerUser).Assembly);
		var factory = nhconfig.BuildSessionFactory();
		return factory.OpenSession();
		#endregion
	}

	private void SetupSMTPEmail()
	{
		emailer = new SmtpClient("smtp.office365.com", 587)
		{
			EnableSsl = true,
			Credentials = new System.Net.NetworkCredential("<EMAIL>", "Kor72522")
		};
	}

	private void CreateCreditInvoices(IMapper _mapper, CompanyFile cf, ItemInvoiceService itemInvoiceService, Dictionary<string, Account> accounts)
    {
        var errorMsg = "";
        if (!createRefundInvoice)
        {
            return;
        }

        // get the list of 10 last refund notes
        var ordersListToInvoice2 = LepSession.Query<OrderCredit>().Fetch(x => x.Order).Where(o => !o.Invoiced && (o.Type == "C" || o.Type == "M")).Take(10).ToList();
        if (!ordersListToInvoice2.Any())
        {
            return;
        }

        foreach (OrderCredit oc in ordersListToInvoice2)
        {
            IOrder o = oc.Order;
            string invoiceNumber = "";


            if (oc.Order != null)
            {
                invoiceNumber = oc.Type + oc.Order.Id.ToString() + oc.Id.ToString();
            }
            else
            {
                invoiceNumber = oc.Type + oc.Customer.Id.ToString() + oc.Id.ToString();
            }



            var watch2 = System.Diagnostics.Stopwatch.StartNew();
            var filterOldInvs = $"Number eq '{invoiceNumber}' ";
            var oldInvs = serviceInvoiceService.GetRange(cf, $"$filter={filterOldInvs}", creds);
            wait();
            if (oldInvs.Items.Any())
            {

                try
                {
                    foreach (var oldInv in oldInvs.Items)
                    {
                        Console.WriteLine($"Deleting old invoice {oldInv.Number}");
                        itemInvoiceService.Delete(cf, oldInv.UID, creds);
                    }
                }
                catch (Exception ex)
                {
                    //ex.Dump();
                    Console.WriteLine($"Error Deleting old credit invoice  {oc.Type}{oc.Order.Id}");
                    continue;
                }
            }
            Console.WriteLine($"{watch2.Elapsed.TotalSeconds} to delete old invoices");


            var errorMsg2 = "";


            errorMsg = "";
            var watch = System.Diagnostics.Stopwatch.StartNew();





            var invAmt = oc.Amount;
            var miscInvoice = new ServiceInvoice();
            string x = string.Empty;
            try
            {
                Console.Write($"\n{invoiceNumber}, {oc.DateCreated.ToShortDateString()},  total {invAmt}");

                #region Create a ServiceInvoice header for this order
                miscInvoice.Number = invoiceNumber;
                miscInvoice.CustomerPurchaseOrderNumber = TruncateLongString(o.PurchaseOrder ?? "", 20);
                miscInvoice.InvoiceDeliveryStatus = DocumentAction.PrintAndEmail;
                miscInvoice.InvoiceType = InvoiceLayoutType.Service;
                miscInvoice.Subtotal = invAmt;
                miscInvoice.TotalAmount = invAmt;
                miscInvoice.BalanceDueAmount = invAmt;
                miscInvoice.IsTaxInclusive = false;
                miscInvoice.Date = oc.DateCreated;

                #endregion

                #region Lookup Customer from MYOB by MYOB.DisplayId = LepUsername
                Customer myobCustomer = null;
                if (!string.IsNullOrEmpty(o.Customer.MyobUid))
                {
                    miscInvoice.Customer = new CardLink() { UID = Guid.Parse(o.Customer.MyobUid) };
                }
                else
                {
                    var userName = WebUtility.UrlEncode(o.Customer.Username);
                    var custFilter = $"$filter=DisplayID  eq '{userName}' ";
                    myobCustomer = customerService.GetRange(cf, custFilter, creds).Items.FirstOrDefault();
                    wait();
                    if (myobCustomer == null)
                    {
                        var msg = $" can not find customer '{o.Customer.Name}' -- '{o.Customer.Username}'";
                        throw new Exception(msg);
                    }
                    miscInvoice.Customer = new CardLink() { UID = myobCustomer.UID };
                }


                #endregion

                var lines = new List<ServiceInvoiceLine>();



                var invLine = new ServiceInvoiceLine();
                invLine.Description = $"{oc.Description}";

                if (!String.IsNullOrEmpty(oc.Account))
                {
                    var x2 = accounts[oc.Account];
                    invLine.Account = new AccountLink() { UID = x2.UID }; ;
                }

                invLine.Total = invAmt;
                invLine.TaxCode = gst;

                lines.Add(invLine);


                miscInvoice.Lines = lines;
                miscInvoice.JournalMemo = "I2";
                var justCreatedInvoice = serviceInvoiceService.InsertEx(cf, miscInvoice, creds);
                Console.Write($" Success. ");

                // log success full myob invoice creation
                LepExecuteSQL($@"
									Insert into Invoicer2Log 
											([OrderId],[JobCount],[Total],[FinishDate]
											,[Success],[Details] ,[DateCreated])
									VALUES 
											({o.Id}, {1}, {invAmt}, '{oc.DateCreated.ToString(format)}',
											'Y', null, '{DateTime.Now.ToString(format)}');");
                #region Email
                if (createPdfInvoice)
                {
                    var orderDto = _mapper.Map<OrderRefundDto>(oc);

                    var jsonStr = JsonConvert.SerializeObject(orderDto, Newtonsoft.Json.Formatting.Indented);

                    jsonStr = jsonStr.Replace("'", "''");
                    //jsonStr.Dump();

                    FastReport.Report reportOrderInvoice = new FastReport.Report();
                    var fileName = @"c:\LepData\Labels2\lep-invoice-refund.frx";
                    reportOrderInvoice.Load(fileName);

                    reportOrderInvoice.Dictionary.Connections[0].ConnectionString = "Json='" + jsonStr + "'";
                    reportOrderInvoice.Prepare();
                    var d = (o.FinishDate ?? DateTime.Now).Date.ToString("yyyy/MMM/dd");

                    var dir = $@"{pdfFolder}\{d}";
                    if (!Directory.Exists(dir))
                        Directory.CreateDirectory(dir);

                    var miscInvPdfName = $@"{dir}/{invoiceNumber}.pdf";
                    var pdfExport = new PDFSimpleExport();
                    var htmlExport = new HTMLExport();

                    //pdfExport.AllowModify = true;

                    using (var fs1 = new System.IO.FileStream(miscInvPdfName, FileMode.Create))
                    {
                        pdfExport.Export(reportOrderInvoice, fs1);
                    };


                    if (oc.Type == "C" || oc.Type == "M")
                    {
                        var destFolder = $@"{DataDirectoryFullName}\orders\{o.DateCreated:yyyyMMdd}\{o.OrderNr}\Extrafiles";

                        if (!Directory.Exists(destFolder))
                            Directory.CreateDirectory(destFolder);
                        File.Copy(miscInvPdfName, Path.Combine(destFolder, $"{invoiceNumber}.pdf"), true);
                    }


                    //using (var ms = new System.IO.FileStream(fileDownloadName+".html", FileMode.Create)) {
                    //	htmlExport.Export(reportOrderInvoice, ms);
                    //};
                    if (emailPdfInvoice)
                    {
                        var address = o.Customer.AccountEmail;

                        if (string.IsNullOrEmpty(address))
                        {
                            address = o.Customer.Email;
                        }

                        if (!string.IsNullOrEmpty(emailPdfAddress))
                        {
                            address = emailPdfAddress;
                        }

                        if (!string.IsNullOrEmpty(address))
                        {
                            var mail = createEmail();
                            mail.Subject = $"Invoice {invoiceNumber}";
                            mail.To.Add(new MailAddress(address));
                            mail.Attachments.Add(new Attachment(miscInvPdfName));

                            mail.IsBodyHtml = true;

                            var body = $@"Please find attached your invoice {invoiceNumber} from LEP Colour Printers.<br/>
										 Contact <EMAIL> for any queries.";

                            mail.Body = body;
                            emailer.Send(mail);
                        }
                    }

                }
                #endregion
            }
            catch (ApiCommunicationException ex)
            {
                Console.WriteLine(ex.Message);
                //orderInvoice.Dump();

                var errrs = ex.Message;

                if (ex.Errors != null)
                {
                    errrs += "\n" +
                    String.Join(", ", ex.Errors.Select(_ => _.Message).ToArray());
                }


                errorMsg = errrs;
                errorMsg = errorMsg.Replace("'", "''");
                Console.Write($" Fail. " + errorMsg);
                LepExecuteSQL($@"
										Insert into Invoicer2Log 
											([OrderId],[JobCount],[Total],[FinishDate],
												[Success],[Details] ,[DateCreated]) 
										VALUES 
											({o.Id}, {o.Jobs.Count}, {invAmt},  '{o.FinishDate.Value.ToString(format)}',
												'N', '{errorMsg}', '{DateTime.Now.ToString(format)}');
										UPDATE [OrderCredit] SET Invoiced2 = 'F' , Invoiced2Details = '{errorMsg}' Where Id = {oc.Id};
										");
                continue;
            }
            catch (Exception ex)
            {
                Console.Write($" Fail. {ex.Message} \n {ex.StackTrace}");

                errorMsg = ex.Message.Replace("'", "''");
                Console.Write($" Fail. " + errorMsg);
                LepExecuteSQL($@"Insert into Invoicer2Log ([OrderId],[JobCount],[Total],[FinishDate],[Success],[Details] ,[DateCreated]) VALUES 
					({o.Id}, {o.Jobs.Count}, {invAmt}, '{o.FinishDate.Value.ToString(format)}', 'N', '{errorMsg}', '{DateTime.Now.ToString(format)}');
					UPDATE [OrderCredit] SET Invoiced2 = 'F' , Invoiced2Details = '{errorMsg}' Where Id = {oc.Id};");

                continue;
            }

            LepExecuteSQL($@"UPDATE [OrderCredit] SET Invoiced = 'Y' where Id = {oc.Id} ;");


        }

    }

    private void CleanupInvoicerReports()
	{
		LepExecuteSQL(@";
					with cte 
					as (
						 select *,
						 RN = row_number()
							 over(partition By[OrderId]
								  order By[DateCreated] Desc, [OrderId] desc)  
								  from[Invoicer2Log]
						)
					delete from cte where RN != 1; ");
	}

	//--------------------------------------
	MailMessage createEmail()
	{
		return new MailMessage()
		{
			IsBodyHtml = true,
			From = new MailAddress("<EMAIL>", "LEP Colour Printers")

		};
	}


	//Customer CreateMYOBCustomerFromLEPCustomer(ICustomerUser lepCustomer)
	//{
	//	var myobCustomer = new Customer();
	//	myobCustomer.CompanyName = lepCustomer.Name;
	//	myobCustomer.DisplayID = lepCustomer.Username;


	//	var addresses = new List<MYOB.AccountRight.SDK.Contracts.Version2.Contact.Address>();

	//	var billingAddress = new Address();
	//	billingAddress.Country = lepCustomer.BillingAddress.Country;
	//	billingAddress.PostCode = lepCustomer.BillingAddress.Postcode;
	//	billingAddress.State = lepCustomer.BillingAddress.State;
	//	billingAddress.City = lepCustomer.BillingAddress.City;

	//	var bs = lepCustomer.BillingAddress.Address1;
	//	billingAddress.Street = string.Join(", ", new[] { lepCustomer.BillingAddress.Address1, lepCustomer.BillingAddress.Address2, lepCustomer.BillingAddress.Address3 }).Replace(", , ", "");
	//	billingAddress.Location = 1;
	//	addresses.Add(billingAddress);


	//	var shippingAddress = new Address();
	//	shippingAddress.Country = lepCustomer.PostalAddress.Country;
	//	shippingAddress.PostCode = lepCustomer.PostalAddress.Postcode;
	//	shippingAddress.State = lepCustomer.PostalAddress.State;
	//	shippingAddress.City = lepCustomer.PostalAddress.City;
	//	shippingAddress.Street = string.Join(", ", new[] { lepCustomer.PostalAddress.Address1, lepCustomer.PostalAddress.Address2, lepCustomer.PostalAddress.Address3 }).Replace(", , ", "");
	//	shippingAddress.Location = 2;
	//	addresses.Add(shippingAddress);

	//	myobCustomer.Addresses = addresses.AsEnumerable();

	//	//myobCustomer.CustomField1.Value =

	//	//CustomField3 Franchise Code
	//	//NOT NEEDED in myob ANYWAYS - JOHN WILL CONFIRM
	//	//myobCustomer.CustomField3 = new Identifier() { Label = "Franchise Code", Value = lepCustomer.FranchiseCode};

	//	// CustomList2 Customer Status
	//	//NOT NEEDED in myob ANYWAYS - 
	//	myobCustomer.CustomList2 = new Identifier() { Label = "Customer Status", Value = lepCustomer.CustomerStatus };


	//	// CustomList3 Terms
	//	// Driven from myob -> LEP
	//	myobCustomer.CustomList3 = new Identifier() { Label = "Terms", Value = lepCustomer.PaymentTerms == PaymentTermsOptions.COD ? "COD/PREPAID" : "ACCOUNT" }; ;

	//	var customerSellingDetails = new CustomerSellingDetails() { TaxCode = gst, FreightTaxCode = gst };

	//	try
	//	{
	//		// if lep cusomer has a sales consultant then find the sales consultant UID from myob
	//		if ( false && !String.IsNullOrEmpty(lepCustomer.SalesConsultant))
	//		{
	//			var salesPersonId = LepSession.CreateSQLQuery($"Select Id From SalesConsultant where name = '{lepCustomer.SalesConsultant}'").List<int>().First();
	//			var empFilter = $"$filter=DisplayID eq '{salesPersonId}'";
	//			var employee = employeeService.GetRange(cf, empFilter, creds).Items.FirstOrDefault();
	//			wait();
	//			if (employee != null)
	//			{
	//				customerSellingDetails.SalesPerson = new EmployeeLink() { UID = employee.UID };

	//			}
	//		}
	//	}
	//	catch (Exception ex)
	//	{
	//		//ex.Dump();
	//		Console.WriteLine($" SalesConsultant '{lepCustomer.SalesConsultant}' not found in myob\n {ex.Message}");

	//	}
	//	myobCustomer.SellingDetails = customerSellingDetails;
	//	// not required myob -> LEP
	//	//sd.Credit = new CustomerCredit(){}
	//	var r = customerService.InsertEx(cf, myobCustomer, creds);
	//	return r;
	//	//try
	//	//{

	//	//} catch(Exception ex)
	//	//{
	//	//	Console.WriteLine($" Add Customer to myob failed");
	//	//	Console.WriteLine(ex.Message);
	//	//}
	//	//return null;
	//}

	void LepExecuteSQL(string sql)
	{
		try
		{
			LepSession.CreateSQLQuery(sql).ExecuteUpdate();
		}
		catch (Exception ex)
		{
			Console.WriteLine(ex.Message);
		}
	}


	string TruncateLongString(string str, int maxLength)
	{
		if (string.IsNullOrEmpty(str))
			return str;
		return str.Substring(0, Math.Min(str.Length, maxLength));
	}

	string GetAccountDisplayIdFromJob(IJob job)
	{
		/*
		4-1004	Business Cards
		4-1005	Brochures
		4-1006	Letterheads/Compliment Slips
		4-1007	Magazines & Booklets
		4-1008	Presentation Folders
		4-1009	Other

		4-2004	DPC Sales - Business Card
		4-2005	DPC Sales - Brochures
		4-2006	DPC Sales - Stationery
		4-2007	DPC Sales - Magazines
		4-2008	DPC Sales - Pres Folder
		4-2009	DPC Sales - Other

		4-3020	Wide Format - Adhesives
		4-3021	Wide Format - Banners/Pullups

		4-1010	Discounts Allowed
		8-1050	Freight recovered
		*/
		bool isOffset = job.PrintType == PrintType.O;
		if (job.IsBusinessCard())
			return isOffset ? "4-1004" : "4-2004";

		if (job.IsBrochure())
			return isOffset ? "4-1005" : "4-2005";

		if (job.Template.Category.Contains("Stationery"))
			return isOffset ? "4-1006" : "4-2006";

		if (job.IsMagazine())
			return isOffset ? "4-1007" : "4-2007";

		if (job.Template.Category.Contains("Presentation Folders"))
			return isOffset ? "4-1008" : "4-2008";

		if (job.Template.Category.Contains("Adhesive Signs, Rigid Signs & Stickers"))
			return "4-3020";

		if (job.Template.Category.Contains("Banners/Pull Ups"))
			return "4-3021";

		if (job.IsNCRBook() || job.IsEnvelope())
			return "4-1009";

		return isOffset ? "4-1009" : "4-2009";
	}


}
