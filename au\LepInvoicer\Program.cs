using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Serilog;

namespace LepInvoicer;

public class Program
{
	public static async Task<int> Main(string[] args)
	{
		// Setup logging first
		Log.Logger = new LoggerConfiguration()
			.WriteTo.Console()
			.WriteTo.File("logs/invoicer-.txt", rollingInterval: RollingInterval.Day)
			.CreateLogger();

		try
		{
			Log.Information("Starting LEP Invoicer application");

			var host = CreateHostBuilder(args).Build();
			var invoicer = host.Services.GetRequiredService<IInvoicerService>();

			var result = await invoicer.RunInvoicerAsync();

			Log.Information("LEP Invoicer completed with result: {Result}", result);
			return result;
		}
		catch (Exception ex)
		{
			Log.Fatal(ex, "Application terminated unexpectedly");
			return 1;
		}
		finally
		{
			Log.CloseAndFlush();
		}
	}

	private static IHostBuilder CreateHostBuilder(string[] args) =>
		Host.CreateDefaultBuilder(args)
			.UseSerilog()
			.ConfigureServices((context, services) =>
			{
				services.Configure<InvoicerConfiguration>(context.Configuration.GetSection("Invoicer"));

				// Register NHibernate session (placeholder - should be configured properly)
				services.AddScoped<NHibernate.ISession>(provider =>
				{
					// TODO: Configure NHibernate session factory properly
					throw new NotImplementedException("NHibernate session factory not configured");
				});

				// Register services
				services.AddScoped<IInvoicerService, InvoicerService>();
				services.AddScoped<IMYOBService, MYOBService>();
				services.AddScoped<IEmailService, EmailService>();
				services.AddScoped<LepInvoicer.Services.IPdfService, LepInvoicer.Services.PdfService>();
				services.AddScoped<IDatabaseService, DatabaseService>();
			});
}
