# Job Board Timing Integration Guide

This document explains how the vanilla JavaScript job board integrates with the AngularJS timing system with SignalR real-time communication.

## Overview

The job board (`jobboards1_fg_json_ds.html`) is a vanilla JavaScript/jQuery application using DataTables, while the timing system is built with AngularJS and SignalR for real-time updates. This integration bridges the two systems with modern real-time capabilities.

## Integration Architecture

### 1. Job Board Enhancements
- **Added timing buttons** to the Job ID column
- **Added job details buttons** for quick view
- **JavaScript bridge functions** to detect AngularJS context
- **Fallback mechanisms** when AngularJS isn't available

### 2. Button Integration
Each job row now shows:
```html
<div class="job-id-cell">
    <a href="...">Job ID</a>
    <button onclick="openJobTiming(jobId, status)">▶️</button>
    <button onclick="openJobDetails(jobId)">👁️</button>
</div>
```

### 3. Context Detection
The integration uses smart context detection:

```javascript
// Try AngularJS popup first
if (window.angular && window.angular.element) {
    // Use AngularJS service for modal popup
    JobBoardPopupService.openJobBoardPopup(jobId, status);
} else {
    // Fallback to standalone timing page
    window.open('timing-standalone.html?jobId=123&status=Printing');
}
```

## Files Modified/Created

### Modified Files
- ✅ `jobboards1_fg_json_ds.html` - Added timing buttons and integration functions

### New Files Created
- ✅ `timing-standalone.html` - Standalone timing page for fallback
- ✅ `job-details-readonly.component.html/coffee` - Readonly job details component
- ✅ `job-details-compact.component.html/coffee` - Compact job details component
- ✅ `job-board-popup.html` - Main timing popup template
- ✅ `job-board-popup.controller.coffee` - Popup controller
- ✅ `job-board-popup.service.coffee` - Service to open popups

### SignalR Service Architecture
- ✅ `jobboard.service.coffee` - Main service orchestrator
- ✅ `jobboard-api.service.coffee` - HTTP API service (renamed from jobboard-ng.service.coffee)
- ✅ `jobboard-signalr.service.coffee` - SignalR real-time service
- ✅ `signalr.service.coffee` - Low-level SignalR client

## How It Works

### Scenario 1: AngularJS Context Available (SignalR Mode)
1. User clicks timing button on job board
2. `openJobTiming()` detects AngularJS context
3. Gets `JobBoardPopupService` from AngularJS injector
4. Opens modal popup with timing controls + job details
5. **SignalR connection established** for real-time updates
6. User completes timing, **all connected users see updates instantly**
7. Popup closes, job board updates in real-time

### Scenario 2: Standalone Context (HTTP API Fallback)
1. User clicks timing button on job board
2. `openJobTiming()` can't find AngularJS context
3. Opens `timing-standalone.html` in new window
4. Standalone page loads job data and instructions
5. **Uses HTTP API** for timing operations (no real-time updates)
6. User completes timing in separate window
7. User closes window, returns to job board (manual refresh needed)

## API Integration

### Job Data
- **Endpoint**: `GET /api/orders/job/{id}`
- **Returns**: `JobViewCustDto` with complete job information
- **Used by**: Both popup and standalone timing page

### Production Instructions
- **Endpoint**: `GET /api/production-instructions/{status}`
- **Returns**: Checkpoints and detailed instructions
- **Used by**: Timing popup and standalone page

### Timing Events
- **HTTP Endpoint**: `POST /api/jobtiming/play|pause|resume|finish`
- **SignalR Hub**: `LepCoreHub.StartTiming|PauseTiming|ResumeTiming|FinishTiming`
- **Sends**: Start, Pause, Resume, Finish events
- **Used by**: Both timing interfaces (SignalR preferred for real-time)

## Button Styling

The timing buttons are styled to be compact and unobtrusive:

```css
.timing-btn {
    padding: 2px 6px !important;
    font-size: 11px !important;
    background-color: #5cb85c !important;
}

.details-btn {
    background-color: #5bc0de !important;
}
```

## User Experience

### From Job Board
1. **See job list** with timing buttons next to each job ID
2. **Click green play button** to start timing for current status
3. **Click blue eye button** to view job details quickly
4. **Popup opens** with:
   - Quick checkpoints (boss's version)
   - Job details (readonly)
   - Detailed instructions (if available)
   - Timing controls (Start/Pause/Resume/Finish)

### Timing Workflow
1. **Start timing** - Timer begins, event logged
2. **View checkpoints** - Boss's practical quality control steps
3. **Check job details** - All job information visible
4. **Pause/Resume** - If interruptions occur
5. **Finish timing** - Complete the step, data saved

## Benefits

### For Users
- ✅ **No navigation away** from job board
- ✅ **Quick access** to timing and job details
- ✅ **Quality checkpoints** visible during work
- ✅ **Accurate timing** with pause/resume capability
- ✅ **Real-time updates** - see changes instantly across all users
- ✅ **Works everywhere** - AngularJS with SignalR or standalone with HTTP

### For Developers
- ✅ **No major changes** to existing job board
- ✅ **Graceful fallbacks** when AngularJS unavailable
- ✅ **Reusable components** for other job boards
- ✅ **Clean separation** between vanilla JS and AngularJS
- ✅ **Service abstraction** - easy to switch between SignalR and HTTP
- ✅ **Real-time architecture** - scalable for future enhancements

## Deployment Notes

### File Locations
```
au/LepCore/FrontEnd/app/staff/jobboards/
├── jobboards1_fg_json_ds.html (modified)
├── timing-standalone.html (new)
├── jobboard.service.coffee (new - main orchestrator)
├── jobboard-api.service.coffee (renamed from jobboard-ng.service.coffee)
├── jobboard-signalr.service.coffee (new - SignalR implementation)
├── signalr.service.coffee (new - SignalR client)
└── timing/ (existing timing files)

au/LepCore/BackEnd/Hubs/
└── LepCoreHub.cs (new - SignalR hub)

au/LepCore/FrontEnd/app/common/
├── directives/ (new job details components)
├── templates/ (new popup templates)
├── controllers/ (new popup controllers)
└── services/ (new popup services)
```

### Dependencies
- ✅ **Bootstrap 3** - Already included in LEPCore
- ✅ **AngularJS 1.x** - Already included in LEPCore
- ✅ **jQuery/DataTables** - Already used in job board
- ✅ **Font Awesome** - For icons (can use Glyphicons as fallback)
- ✅ **SignalR Client** - Added via bower_components/signalr.min.js
- ✅ **ASP.NET Core SignalR** - Backend hub for real-time communication

## Testing

### Test Scenarios
1. **AngularJS Context**: Open job board from AngularJS app, test popup
2. **Standalone Context**: Open job board directly, test new window
3. **SignalR Timing**: Test Start/Pause/Resume/Finish with real-time updates
4. **HTTP API Fallback**: Test timing with SignalR disabled
5. **Multi-user Real-time**: Open multiple browser tabs, verify instant updates
6. **Job Details**: Verify readonly job information displays correctly
7. **Instructions**: Check production instructions load properly
8. **HD Column Sorting**: Verify Hours Till Dispatch sorting works in real-time

### Browser Compatibility
- ✅ **Chrome/Edge** - Full support
- ✅ **Firefox** - Full support  
- ✅ **Safari** - Full support
- ✅ **IE11** - Basic support (if needed)

## Future Enhancements

### Possible Improvements
- ✅ **Real-time updates** - ✅ IMPLEMENTED via SignalR WebSocket integration
- **Bulk timing** - Start timing for multiple jobs simultaneously
- **Mobile optimization** - Touch-friendly timing controls for tablets
- **Offline support** - Cache timing data when network unavailable
- **Analytics** - Real-time timing reports and productivity metrics
- **Run grouping** - Real-time updates when jobs are grouped/ungrouped in runs
- **User presence** - Show which users are working on which jobs
- **Push notifications** - Alert users about urgent jobs (low HD values)
