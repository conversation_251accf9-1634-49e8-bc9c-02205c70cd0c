-- Job Flow Analysis
-- Track jobs through their complete workflow with timing

WITH JobFlow AS (
    SELECT
        e.JobId,
        j.Name AS JobName,
        o.Id AS OrderId,
        c.UserName AS CustomerName,
        e.Status,
        ROW_NUMBER() OVER (PARTITION BY e.JobId ORDER BY MIN(e.EventTime)) AS StepSequence,
        MIN(e.EventTime) AS StepStartTime,
        MAX(e.EventTime) AS StepEndTime,
        DATEDIFF(SECOND, MIN(e.EventTime), MAX(e.EventTime)) AS StepDurationSeconds,
        COUNT(DISTINCT e.WorkstationId) AS WorkersInvolved
    FROM JobStepTimingEvent e
        INNER JOIN Job j ON e.JobId = j.Id
        INNER JOIN [Order] o ON j.OrderId = o.Id
        INNER JOIN LepUser c ON o.UserId = c.Id
    GROUP BY e.JobId, j.<PERSON>, o.Id, c.<PERSON>r<PERSON><PERSON>, e.<PERSON>
),
JobTotals AS (
    SELECT
        JobId,
        SUM(StepDurationSeconds) AS TotalJobDurationSeconds,
        MIN(StepStartTime) AS JobStartTime,
        MAX(StepEndTime) AS JobEndTime
    FROM JobFlow
    GROUP BY JobId
)
SELECT
    jf.JobId,
    jf.JobName,
    jf.OrderId,
    jf.CustomerName,
    jf.StepSequence,
    jf.Status,
    jf.StepStartTime,
    jf.StepEndTime,
    jf.StepDurationSeconds,
    FORMAT(DATEADD(SECOND, jf.StepDurationSeconds, 0), 'HH:mm:ss') AS StepFormattedDuration,
    jf.WorkersInvolved,
    jt.TotalJobDurationSeconds,
    FORMAT(DATEADD(SECOND, jt.TotalJobDurationSeconds, 0), 'HH:mm:ss') AS TotalJobFormattedDuration,
    ROUND((CAST(jf.StepDurationSeconds AS FLOAT) / jt.TotalJobDurationSeconds) * 100, 1) AS PercentOfTotalTime,
    LAG(jf.StepEndTime) OVER (PARTITION BY jf.JobId ORDER BY jf.StepSequence) AS PreviousStepEndTime,
    CASE
        WHEN LAG(jf.StepEndTime) OVER (PARTITION BY jf.JobId ORDER BY jf.StepSequence) IS NOT NULL
        THEN DATEDIFF(MINUTE,
            LAG(jf.StepEndTime) OVER (PARTITION BY jf.JobId ORDER BY jf.StepSequence),
            jf.StepStartTime)
    END AS WaitTimeMinutes
FROM JobFlow jf
    INNER JOIN JobTotals jt ON jf.JobId = jt.JobId
ORDER BY jf.JobId, jf.StepSequence;
