// Test F# Interactive development
printfn "Hello from F# Interactive!"

// Let's start with basic types
type CreditType = 
    | Credit
    | Refund
    | Adjustment
    | Other

type Customer = {
    Id: int
    Name: string
    Username: string
}

type Job = {
    Id: int
    OrderId: int
    Description: string
    Price: decimal option
    IsEnabled: bool
}

type Order = {
    Id: int
    OrderNumber: string
    Customer: Customer
    Jobs: Job list
    FinishDate: System.DateTime option
    SubmissionDate: System.DateTime
    PurchaseOrder: string option
    PromotionBenefit: decimal
    PickUpCharge: decimal
    GST: decimal
    IsInvoiced: bool
}

type OrderCredit = {
    Id: int
    OrderId: int
    CustomerId: int
    CreditType: CreditType
    Amount: decimal
    Description: string
    IsInvoiced: bool
    DateCreated: System.DateTime
}

// Test the types
let testCustomer = {
    Id = 1
    Name = "Test Customer"
    Username = "testuser"
}

let testJob = {
    Id = 1
    OrderId = 1
    Description = "Test Job"
    Price = Some 100.0m
    IsEnabled = true
}

let testOrder = {
    Id = 1
    OrderNumber = "O001"
    Customer = testCustomer
    Jobs = [testJob]
    FinishDate = Some System.DateTime.Now
    SubmissionDate = System.DateTime.Now
    PurchaseOrder = Some "PO123"
    PromotionBenefit = 0.0m
    PickUpCharge = 0.0m
    GST = 10.0m
    IsInvoiced = false
}

printfn "Test order: %A" testOrder
