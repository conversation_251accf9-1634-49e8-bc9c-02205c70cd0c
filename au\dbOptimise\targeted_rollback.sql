-- =============================================
-- Targeted Optimization Rollback Script
-- Target: [SRV03].[PRD_AU]
-- Purpose: Rollback targeted optimization changes
-- =============================================

USE [PRD_AU];
GO

SET NOCOUNT ON;
GO

PRINT '=== TARGETED OPTIMIZATION ROLLBACK STARTING ===';
PRINT 'Database: ' + DB_NAME();
PRINT 'Server: ' + @@SERVERNAME;
PRINT 'Rollback Date: ' + CONVERT(VARCHAR(20), GETDATE(), 120);
PRINT '';
PRINT 'WARNING: This will remove indexes created by targeted optimization.';
PRINT 'Ensure you have verified that performance has not improved before proceeding.';
PRINT '';

-- =============================================
-- SECTION 1: IDENTIFY OPTIMIZATION INDEXES
-- =============================================
PRINT '1. IDENTIFYING INDEXES CREATED BY TARGETED OPTIMIZATION...';

SELECT 
    OBJECT_SCHEMA_NAME(object_id) AS schema_name,
    OBJECT_NAME(object_id) AS table_name,
    name AS index_name,
    create_date,
    'DROP INDEX [' + name + '] ON [' + OBJECT_SCHEMA_NAME(object_id) + '].[' + OBJECT_NAME(object_id) + '];' AS drop_command
FROM sys.indexes
WHERE name IN (
    'IX_Order_Status_DateModified',
    'IX_Comment_JobId', 
    'IX_Job_OrderId_Status',
    'IX_LepUser_Customer_Active'
)
ORDER BY create_date DESC;

PRINT '';

-- =============================================
-- SECTION 2: ROLLBACK COMMANDS (COMMENTED OUT FOR SAFETY)
-- =============================================
PRINT '2. ROLLBACK COMMANDS - UNCOMMENT TO EXECUTE';
PRINT 'Review the indexes above and uncomment the section below to execute rollback.';
PRINT '';

/*
-- =============================================
-- REMOVE TARGETED OPTIMIZATION INDEXES
-- =============================================
PRINT 'REMOVING TARGETED OPTIMIZATION INDEXES...';

-- Remove Order table index
IF EXISTS (SELECT 1 FROM sys.indexes WHERE object_id = OBJECT_ID('dbo.Order') AND name = 'IX_Order_Status_DateModified')
BEGIN
    PRINT 'Dropping IX_Order_Status_DateModified...';
    DROP INDEX [IX_Order_Status_DateModified] ON [dbo].[Order];
    PRINT 'SUCCESS: IX_Order_Status_DateModified dropped';
END

-- Remove Comment table index
IF EXISTS (SELECT 1 FROM sys.indexes WHERE object_id = OBJECT_ID('dbo.Comment') AND name = 'IX_Comment_JobId')
BEGIN
    PRINT 'Dropping IX_Comment_JobId...';
    DROP INDEX [IX_Comment_JobId] ON [dbo].[Comment];
    PRINT 'SUCCESS: IX_Comment_JobId dropped';
END

-- Remove Job table index
IF EXISTS (SELECT 1 FROM sys.indexes WHERE object_id = OBJECT_ID('dbo.Job') AND name = 'IX_Job_OrderId_Status')
BEGIN
    PRINT 'Dropping IX_Job_OrderId_Status...';
    DROP INDEX [IX_Job_OrderId_Status] ON [dbo].[Job];
    PRINT 'SUCCESS: IX_Job_OrderId_Status dropped';
END

-- Remove LepUser table index
IF EXISTS (SELECT 1 FROM sys.indexes WHERE object_id = OBJECT_ID('dbo.LepUser') AND name = 'IX_LepUser_Customer_Active')
BEGIN
    PRINT 'Dropping IX_LepUser_Customer_Active...';
    DROP INDEX [IX_LepUser_Customer_Active] ON [dbo].[LepUser];
    PRINT 'SUCCESS: IX_LepUser_Customer_Active dropped';
END

PRINT '';
PRINT 'All targeted optimization indexes have been removed.';
*/

-- =============================================
-- SECTION 3: MANUAL ROLLBACK COMMANDS
-- =============================================
PRINT '3. MANUAL ROLLBACK COMMANDS';
PRINT 'Copy and execute these commands individually if needed:';
PRINT '';
PRINT '-- Drop Order table optimization index';
PRINT 'DROP INDEX [IX_Order_Status_DateModified] ON [dbo].[Order];';
PRINT '';
PRINT '-- Drop Comment table optimization index';
PRINT 'DROP INDEX [IX_Comment_JobId] ON [dbo].[Comment];';
PRINT '';
PRINT '-- Drop Job table optimization index';
PRINT 'DROP INDEX [IX_Job_OrderId_Status] ON [dbo].[Job];';
PRINT '';
PRINT '-- Drop LepUser table optimization index';
PRINT 'DROP INDEX [IX_LepUser_Customer_Active] ON [dbo].[LepUser];';
PRINT '';

-- =============================================
-- SECTION 4: VERIFICATION AFTER ROLLBACK
-- =============================================
PRINT '4. POST-ROLLBACK VERIFICATION QUERIES';
PRINT 'Run these queries after rollback to verify completion:';
PRINT '';

-- Check for remaining optimization indexes
SELECT 
    'REMAINING_OPTIMIZATION_INDEXES' AS check_type,
    COUNT(*) AS count
FROM sys.indexes
WHERE name IN (
    'IX_Order_Status_DateModified',
    'IX_Comment_JobId', 
    'IX_Job_OrderId_Status',
    'IX_LepUser_Customer_Active'
);

-- Show current index status on key tables
SELECT 
    'CURRENT_INDEX_STATUS' AS check_type,
    OBJECT_NAME(object_id) AS table_name,
    COUNT(*) AS index_count
FROM sys.indexes
WHERE object_id IN (
    OBJECT_ID('dbo.Order'),
    OBJECT_ID('dbo.Comment'),
    OBJECT_ID('dbo.Job'),
    OBJECT_ID('dbo.LepUser')
)
    AND type > 0  -- Exclude heaps
GROUP BY object_id
ORDER BY table_name;

PRINT '';
PRINT '=== TARGETED OPTIMIZATION ROLLBACK INFORMATION ===';
PRINT '';
PRINT 'IMPORTANT NOTES:';
PRINT '1. Index rebuilds cannot be "rolled back" - they improve performance permanently';
PRINT '2. Statistics updates cannot be undone - they remain current';
PRINT '3. Only newly created indexes can be dropped';
PRINT '4. Monitor query performance after rollback to ensure no degradation';
PRINT '';
PRINT 'PERFORMANCE IMPACT OF ROLLBACK:';
PRINT '- Order queries may become 30-50% slower without Status/DateModified index';
PRINT '- Comment lookups may become 40-60% slower without JobId index';
PRINT '- Job queries will retain benefits from rebuilt indexes';
PRINT '- Overall system may experience increased I/O after rollback';
PRINT '';
PRINT 'RECOMMENDATION:';
PRINT 'Only perform rollback if specific application issues are identified.';
PRINT 'The optimization changes are generally beneficial for performance.';
PRINT '';
PRINT 'If you need to rollback due to performance issues:';
PRINT '1. Uncomment the rollback section above';
PRINT '2. Execute this script';
PRINT '3. Monitor performance for 24-48 hours';
PRINT '4. Consider re-applying optimizations with different settings';
PRINT '';
PRINT 'For support, review the optimization logs and contact the DBA team.';
