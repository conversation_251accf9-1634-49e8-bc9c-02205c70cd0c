# Timing System Reports

This folder contains SQL reports for analyzing job timing data and worker productivity.

## Report Files

### 1. Current Active Sessions (`01-current-active-sessions.sql`)
- **Purpose**: Real-time monitoring of who is currently working on what
- **Shows**: Active sessions, worker names, job details, current state, duration
- **Use Case**: Live dashboard, supervisor monitoring

### 2. Job Step Performance Analysis (`02-job-step-performance-analysis.sql`)
- **Purpose**: Analyze average time spent at each job status/step
- **Shows**: Average, min, max, median durations by status
- **Use Case**: Process optimization, identifying slow steps

### 3. Worker Productivity Report (`03-worker-productivity-report.sql`)
- **Purpose**: Track individual worker performance and time allocation
- **Shows**: Time spent by worker and status, jobs completed, averages
- **Use Case**: Performance reviews, workload balancing

### 4. Job Bottleneck Analysis (`04-job-bottleneck-analysis.sql`)
- **Purpose**: Identify jobs taking significantly longer than average
- **Shows**: Jobs with performance flags (slow/fast/normal), percentage vs average
- **Use Case**: Quality control, process improvement

### 5. Daily Productivity Dashboard (`05-daily-productivity-dashboard.sql`)
- **Purpose**: Daily summary of work completed by status and worker
- **Shows**: Jobs completed per day, total time worked, work spans
- **Use Case**: Daily reporting, capacity planning

### 6. Job Flow Analysis (`06-job-flow-analysis.sql`)
- **Purpose**: Track jobs through their complete workflow with timing
- **Shows**: Step sequence, durations, wait times between steps
- **Use Case**: Workflow optimization, identifying delays

### 7. Exception Report - Long Running Sessions (`07-exception-report-long-running-sessions.sql`)
- **Purpose**: Find sessions that appear abandoned or running too long
- **Shows**: Sessions with exception flags, hours since last update
- **Use Case**: System maintenance, identifying stuck processes

### 8. Performance Trends Over Time (`08-performance-trends-over-time.sql`)
- **Purpose**: Weekly performance trends by status
- **Shows**: Week-over-week changes in average durations
- **Use Case**: Long-term analysis, trend identification

## Usage Notes

- All reports use advanced SQL Server syntax including window functions, CTEs, and formatting
- Time ranges can be adjusted by modifying the WHERE clauses
- Reports have been tested against the Newman database (PRD_AU catalog)
- Reports use the correct table structure and relationships:
  - `JobStepActiveSession` - Active timing sessions
  - `JobStepTimingEvent` - Historical timing events
  - `Job`, `Order`, `LepUser` - Related entities
  - Order.UserId links to LepUser.Id (not Customer table)
  - EventType values: PLAY=1, PAUSE=2, RESUME=3, FINISH=4
  - CurrentState values: PLAYING=1, PAUSED=2, FINISHED=3

## Customization

You can modify these reports by:
- Changing time ranges (e.g., last 30 days vs last 7 days)
- Adding filters for specific customers, job types, or workers
- Adjusting performance thresholds (e.g., what constitutes "slow")
- Adding additional calculated fields or groupings
