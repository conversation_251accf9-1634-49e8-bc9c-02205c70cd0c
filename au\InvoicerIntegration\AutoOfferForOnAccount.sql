
ALTER Procedure [dbo].[AutoOfferForOnAccountCustomers]
AS
BEGIN
    DECLARE @CurrentTime DATETIME = GETDATE();
    DECLARE @FirstDayOfMonth DATETIME = DATEADD(MONTH, DATEDIFF(MONTH, 0, GETDATE()), 0);
    DECLARE @LastDayOfMonth DATETIME = DATEADD(MONTH, DATEDIFF(MONTH, -1, GETDATE()), -1);

    ;WITH Cust AS (
        SELECT c.CustomerId,
            EDRp = CASE
                WHEN c.MAT3mA >= 10000 AND c.MAT3mA < 15000 THEN 25
                WHEN c.MAT3mA >= 7500 AND c.MAT3mA < 10000 THEN 20
                WHEN c.MAT3mA >= 5000 AND c.MAT3mA < 7500 THEN 15
                WHEN c.MAT3mA >= 2000 AND c.MAT3mA < 5000 THEN 10
                WHEN c.MAT3mA >= 500 AND c.MAT3mA < 2000 THEN 5
                ELSE NULL
            END,
            c.<PERSON>,
            c.MAT3mA,
            c.FranchiseCode
        FROM Customer c
        WHERE c.PaymentTerms IN ('Account', 'OnHold')
            AND (c.FranchiseCode = '' or c.FranchiseCode is null)
            AND c.MYOBPastDue <= 0
            AND (C.ProductPriceCode IN ('P0', NULL, '') AND C.FreightPriceCode IN ('F0', NULL, ''))
          ---  AND (c.RunCreateOffer = 1) -- this is the flag set by UpdateCustomerMYOBBalance
    ),

    ExistingOffers AS (
        SELECT PromotionId, CustomerId
        FROM CustomerOffers
        WHERE DateOfferEnds = @LastDayOfMonth
    )

    INSERT INTO CustomerOffers
            ([DateOffered]
            ,[DateOfferEnds]
            ,[DateTakenUp]
            ,[AllowReuse]
            ,[OrderNumberUsedOn]
            ,[ModifiedBy]
            ,[CreatedBy]
            ,[DateCreated]
            ,[DateModified]
            ,[PromotionId]
            ,[CustomerId])
    SELECT 
           @CurrentTime
           ,@LastDayOfMonth
           ,null
           ,'Y'
           ,null
           ,'system'
           ,'system'
           ,@CurrentTime
           ,@CurrentTime
           ,p.Id
           ,c.CustomerId
        FROM Cust c
        JOIN Promotion p ON p.Discount = c.EDRp AND p.PromotionCode LIKE 'My Reward %'
        LEFT JOIN ExistingOffers eo ON eo.PromotionId = p.Id AND eo.CustomerId = c.CustomerId
        WHERE eo.PromotionId IS NULL;
    
    Update Customer 
        set RunCreateOffer = 0  
        where RunCreateOffer = 1 and PaymentTerms IN ('Account', 'OnHold')
END

GO