﻿namespace LepInvoicerFSharp.Types

open System
open System.Threading.Tasks
open Microsoft.Extensions.Logging
open FsToolkit.ErrorHandling
open LepInvoicerFSharp.Types
open LepInvoicerFSharp.Configuration
open LepInvoicerFSharp.Database
open LepInvoicerFSharp.MYOB
open LepInvoicerFSharp.OrderProcessing

// ============================================================================
// MAIN PROGRAM - Functional application orchestration
// ============================================================================

module Program =

    // ========================================================================
    // FUNCTIONAL APPLICATION PIPELINE - Using FsToolkit.ErrorHandling
    // ========================================================================

    /// Setup logging
    let setupLogging () =
        use loggerFactory = LoggerFactory.Create(fun builder ->
            builder.AddConsole() |> ignore
        )
        loggerFactory.CreateLogger("LepInvoicerFSharp")

    /// Main application workflow using functional composition
    let runInvoicer () : Async<Result<int, string>> =
        asyncResult {
            let logger = setupLogging()
            let startTime = DateTime.Now

            logger.LogInformation("=== LEP Invoicer F# - Functional Implementation ===")

            // Load and validate configuration
            let! config = Configuration.loadAndValidate () |> Async.retn

            logger.LogInformation("Configuration loaded successfully")
            ConfigHelpers.printSummary config

            // Check for pending work (smart optimization)
            let! (hasPendingWork, workDescription) = Database.checkForPendingWork config

            if not hasPendingWork then
                logger.LogInformation("No pending work found - skipping MYOB initialization")
                let elapsed = DateTime.Now - startTime
                logger.LogInformation($"LEP Invoicer completed successfully in {elapsed.TotalMilliseconds:F0}ms. No work to process.")
                return 0
            else
                logger.LogInformation($"Found pending work: {workDescription}")

                // Initialize MYOB (only when work is available)
                let! myobState = MYOBService.initialize config
                logger.LogInformation("MYOB service initialized")

                // Process all work using functional pipeline
                let! stats = OrderProcessing.processAllPendingWork config myobState

                let elapsed = DateTime.Now - startTime
                logger.LogInformation($"LEP Invoicer completed successfully in {elapsed.TotalMilliseconds:F0}ms.")
                logger.LogInformation($"Orders: {stats.OrdersProcessed} (Success: {stats.OrdersSuccessful}, Failed: {stats.OrdersFailed})")
                logger.LogInformation($"Credits: {stats.CreditsProcessed}, Refunds: {stats.RefundsProcessed}")

                return 0
        }



    // ========================================================================
    // ENTRY POINT - Pure functional entry point
    // ========================================================================

    /// Application entry point (functional)
    [<EntryPoint>]
    let main (args: string[]) : int =
        async {
            let! result = runInvoicer ()
            match result with
            | Ok exitCode -> return exitCode
            | Error error ->
                printfn $"Application failed: {error}"
                return 1
        }
        |> Async.RunSynchronously
