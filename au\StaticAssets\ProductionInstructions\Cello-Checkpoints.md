---
status: Cello
title: Cello Checkpoint Standards
type: checkpoints
estimatedDuration: 8
isActive: true
requiredTools:
  - Job Bag
  - Cello materials
  - Cleaning materials for rollers
qualityChecks:
  - Read Job Bag including Additional Information
  - Perform Quality Control check before setup
  - Verify correct Cello type and sides
  - Check run sheet orientation
  - Ensure rollers are clean
  - Stamp runner OK to Run
---

# Cello Checkpoint Standards

**Prior to processing a Run** - *Feb 2014*

## Pre-Cello Processing Checklist ✅

### 1. Job Documentation Review
- [ ] **Read Job Bag** including Additional Information section and action accordingly

### 2. Quality Control Setup Check
- [ ] **Perform Quality Control check** before set-up:
  - [ ] Backup correct
  - [ ] Correct material
  - [ ] Quantity verification

### 3. Cello Setup Verification
- [ ] **Correct Cello type** is loaded
- [ ] **Correct side(s)** being celloed

### 4. Run Sheet Orientation Check
- [ ] **Check run sheet orientation** on track is correct:
  - [ ] **Single sided** – colour bar at front
  - [ ] **Double sided** – colour bar at back

### 5. Equipment Cleanliness
- [ ] **All rollers are clean**
- [ ] **No bits or printers marks** are visible

### 6. Final Sign-Off
- [ ] **Stamp runner 'OK to Run'**
- [ ] **Sign to confirm** all points above have been done

---

> 🎯 **Critical**: Clean rollers and correct orientation prevent quality issues and material waste.
