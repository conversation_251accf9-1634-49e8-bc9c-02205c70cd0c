﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <OutputType>Exe</OutputType>
        <TargetFramework>net8.0-windows7.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="FastReport.Data.Json" Version="2024.1.0" />
        <PackageReference Include="FastReport.OpenSource" Version="2024.1.0" />
        <PackageReference Include="FastReport.OpenSource.Export.PdfSimple" Version="2024.1.0" />
        <PackageReference Include="MYOB.AccountRight.API.SDK" Version="2023.10.534" />
        <PackageReference Include="Newtonsoft.Json" Version="13.0.1" />
    </ItemGroup>

    
    <ItemGroup>
        <ProjectReference Include="..\code\main\main.csproj" />
        <ProjectReference Include="..\LepCore.DTOs\LepCore.DTOs.csproj" />
        <ProjectReference Include="..\lumen\lumen.csproj" />
    </ItemGroup>

</Project>
