namespace LepInvoicerFSharp

open System

// ============================================================================
// REAL DOMAIN TYPES - Matching actual database schema
// ============================================================================

/// Credit type enumeration matching database values
type CreditType = 
    | Credit        // "C"
    | Miscellaneous // "M" 
    | CreditInvoice // "CI"
    | Refund        // "S"
    | Other of string

/// Customer information from CustomerUser table
type Customer = {
    Id: int
    Name: string
    Username: string
}

/// Job information from Job table
type Job = {
    Id: int
    OrderId: int
    Description: string
    Price: decimal option
    IsEnabled: bool  // Enable column
}

/// Order entity matching Order table structure
type Order = {
    Id: int
    OrderNumber: string  // OrderNr
    Customer: Customer
    Jobs: Job list
    FinishDate: DateTime option
    SubmissionDate: DateTime
    PurchaseOrder: string option
    PromotionBenefit: decimal
    PickUpCharge: decimal
    GST: decimal
    IsInvoiced: string option  // Invoiced2 column (Y/F/C/null)
}

/// OrderCredit entity matching OrderCredit table
type OrderCredit = {
    Id: int
    OrderId: int
    CustomerId: int
    CreditType: CreditType
    Amount: decimal
    Description: string
    IsInvoiced: bool  // Invoiced column
    DateCreated: DateTime
    CustomerName: string  // From join with CustomerUser
}

/// Processing statistics
type ProcessingStats = {
    OrdersProcessed: int
    OrdersSuccessful: int
    OrdersFailed: int
    CreditsProcessed: int
    RefundsProcessed: int
    ElapsedTime: TimeSpan
    Errors: string list
}

/// Configuration matching appsettings.json structure
type InvoicerConfig = {
    ConnectionString: string
    InvoiceBatchSize: int
    RefundBatchSize: int
    MinimumFinishDate: DateTime
    IgnoreCustomers: string list
    TestMode: bool
    CreateOrderInvoice: bool
    CreateRefundInvoice: bool
    CreatePdfInvoice: bool
    MYOBConfig: {|
        DeveloperKey: string
        DeveloperSecret: string
        CompanyFileName: string
        ConfirmationUrl: string
    |}
}

// ============================================================================
// PURE BUSINESS LOGIC - No side effects
// ============================================================================

module OrderLogic =
    
    /// Calculate total price for an order (pure function)
    let calculateTotalPrice (order: Order) : decimal option =
        let jobTotal = 
            order.Jobs
            |> List.choose (fun job -> if job.IsEnabled then job.Price else None)
            |> List.sum
        
        if jobTotal > 0m then
            Some (jobTotal + order.PromotionBenefit + order.PickUpCharge + order.GST)
        else
            None
    
    /// Check if order is ready for invoicing (pure function)
    let isReadyForInvoicing (config: InvoicerConfig) (order: Order) : bool =
        let hasValidPrice = calculateTotalPrice order |> Option.isSome
        let isNotIgnored = not (List.contains order.Customer.Name config.IgnoreCustomers)
        let isFinished = 
            match order.FinishDate with
            | Some finishDate -> finishDate >= config.MinimumFinishDate
            | None -> false
        let isNotAlreadyInvoiced = 
            match order.IsInvoiced with
            | Some "Y" | Some "F" | Some "C" -> false  // Already processed
            | _ -> true  // null or other values are OK to process
        
        hasValidPrice && isNotIgnored && isFinished && isNotAlreadyInvoiced

module CreditLogic =
    
    /// Check if credit is ready for invoicing (pure function)
    let isReadyForInvoicing (config: InvoicerConfig) (credit: OrderCredit) : bool =
        let hasValidAmount = credit.Amount > 0m
        let isNotAlreadyInvoiced = not credit.IsInvoiced
        let isNotIgnoredCustomer = not (List.contains credit.CustomerName config.IgnoreCustomers)
        
        hasValidAmount && isNotAlreadyInvoiced && isNotIgnoredCustomer

// ============================================================================
// CONFIGURATION HELPERS
// ============================================================================

module ConfigHelpers =
    
    /// Create default configuration
    let defaultConfig = {
        ConnectionString = ""
        InvoiceBatchSize = 50
        RefundBatchSize = 20
        MinimumFinishDate = DateTime.Now.AddDays(-30.0)
        IgnoreCustomers = []
        TestMode = false
        CreateOrderInvoice = true
        CreateRefundInvoice = true
        CreatePdfInvoice = true
        MYOBConfig = {|
            DeveloperKey = ""
            DeveloperSecret = ""
            CompanyFileName = ""
            ConfirmationUrl = ""
        |}
    }
    
    /// Print configuration summary
    let printSummary (config: InvoicerConfig) =
        printfn "Configuration:"
        printfn "  Test Mode: %b" config.TestMode
        printfn "  Invoice Batch Size: %d" config.InvoiceBatchSize
        printfn "  Refund Batch Size: %d" config.RefundBatchSize
        printfn "  Minimum Finish Date: %s" (config.MinimumFinishDate.ToString("yyyy-MM-dd"))
        printfn "  Ignored Customers: %d" (List.length config.IgnoreCustomers)
        printfn "  MYOB Company File: %s" config.MYOBConfig.CompanyFileName
        printfn "  Create Order Invoice: %b" config.CreateOrderInvoice
        printfn "  Create Refund Invoice: %b" config.CreateRefundInvoice

// ============================================================================
// HELPER FUNCTIONS FOR TYPE CONVERSION
// ============================================================================

module TypeConverters =
    
    /// Convert database credit type string to CreditType
    let convertCreditType (typeStr: string) : CreditType =
        match typeStr with
        | "C" -> Credit
        | "M" -> Miscellaneous
        | "CI" -> CreditInvoice
        | "S" -> Refund
        | other -> Other other
    
    /// Convert CreditType to database string
    let creditTypeToString (creditType: CreditType) : string =
        match creditType with
        | Credit -> "C"
        | Miscellaneous -> "M"
        | CreditInvoice -> "CI"
        | Refund -> "S"
        | Other s -> s
