namespace LepInvoicerFSharp

open System
open System.IO
open Microsoft.Extensions.Configuration
open LepInvoicerFSharp

// ============================================================================
// CONFIGURATION MODULE - Functional configuration loading
// ============================================================================

module Configuration =
    
    /// Load configuration from IConfiguration (pure function)
    let loadFromIConfiguration (config: IConfiguration) : InvoicerConfig =
        let getStringValue key defaultValue =
            let value = config.[key]
            if String.IsNullOrWhiteSpace(value) then defaultValue else value
        
        let getIntValue key defaultValue =
            match Int32.TryParse(config.[key]) with
            | true, value -> value
            | false, _ -> defaultValue
        
        let getBoolValue key defaultValue =
            match Boolean.TryParse(config.[key]) with
            | true, value -> value
            | false, _ -> defaultValue
        
        let getDateTimeValue key defaultValue =
            match DateTime.TryParse(config.[key]) with
            | true, value -> value
            | false, _ -> defaultValue
        
        let getStringListValue key =
            let value = config.[key]
            if String.IsNullOrWhiteSpace(value) then 
                []
            else 
                value.Split([|';'; ','|], StringSplitOptions.RemoveEmptyEntries)
                |> Array.map (fun s -> s.Trim())
                |> Array.toList
        
        {
            ConnectionString = getStringValue "ConnectionString" ""
            InvoiceBatchSize = getIntValue "InvoiceBatchSize" 50
            RefundBatchSize = getIntValue "RefundBatchSize" 20
            MinimumFinishDate = getDateTimeValue "MinimumFinishDate" (DateTime.Now.AddDays(-30.0))
            IgnoreCustomers = getStringListValue "IgnoreCustomers"
            TestMode = getBoolValue "TestMode" false
            MYOBConfig = {|
                CompanyFileName = getStringValue "MYOB:CompanyFileName" ""
                Username = getStringValue "MYOB:Username" ""
                Password = getStringValue "MYOB:Password" ""
                ConfirmationUrl = getStringValue "MYOB:ConfirmationUrl" ""
            |}
        }
    
    /// Load configuration from appsettings.json (IO operation)
    let loadFromFile (filePath: string) : Result<InvoicerConfig, string> =
        try
            if not (File.Exists(filePath)) then
                Ok ConfigHelpers.defaultConfig
            else
                let builder = ConfigurationBuilder()
                let config = 
                    builder
                        .AddJsonFile(filePath, optional = true)
                        .AddEnvironmentVariables()
                        .Build()
                
                Ok (loadFromIConfiguration config)
        with
        | ex -> Error $"Failed to load configuration from {filePath}: {ex.Message}"
    
    /// Load configuration from environment variables and defaults (IO operation)
    let loadFromEnvironment () : Result<InvoicerConfig, string> =
        try
            let builder = ConfigurationBuilder()
            let config = 
                builder
                    .AddEnvironmentVariables("LEPINVOICER_")
                    .Build()
            
            Ok (loadFromIConfiguration config)
        with
        | ex -> Error $"Failed to load configuration from environment: {ex.Message}"
    
    /// Load configuration with precedence: file -> environment -> defaults
    let load () : Result<InvoicerConfig, string> =
        let configFile = "appsettings.json"
        
        // Try to load from file first, then environment, then defaults
        match loadFromFile configFile with
        | Ok fileConfig ->
            match loadFromEnvironment() with
            | Ok envConfig ->
                // Merge configurations (environment overrides file)
                Ok {
                    ConnectionString = if String.IsNullOrWhiteSpace(envConfig.ConnectionString) then fileConfig.ConnectionString else envConfig.ConnectionString
                    InvoiceBatchSize = if envConfig.InvoiceBatchSize = 50 then fileConfig.InvoiceBatchSize else envConfig.InvoiceBatchSize
                    RefundBatchSize = if envConfig.RefundBatchSize = 20 then fileConfig.RefundBatchSize else envConfig.RefundBatchSize
                    MinimumFinishDate = if envConfig.MinimumFinishDate = DateTime.Now.AddDays(-30.0).Date then fileConfig.MinimumFinishDate else envConfig.MinimumFinishDate
                    IgnoreCustomers = if List.isEmpty envConfig.IgnoreCustomers then fileConfig.IgnoreCustomers else envConfig.IgnoreCustomers
                    TestMode = envConfig.TestMode || fileConfig.TestMode
                    MYOBConfig = {|
                        CompanyFileName = if String.IsNullOrWhiteSpace(envConfig.MYOBConfig.CompanyFileName) then fileConfig.MYOBConfig.CompanyFileName else envConfig.MYOBConfig.CompanyFileName
                        Username = if String.IsNullOrWhiteSpace(envConfig.MYOBConfig.Username) then fileConfig.MYOBConfig.Username else envConfig.MYOBConfig.Username
                        Password = if String.IsNullOrWhiteSpace(envConfig.MYOBConfig.Password) then fileConfig.MYOBConfig.Password else envConfig.MYOBConfig.Password
                        ConfirmationUrl = if String.IsNullOrWhiteSpace(envConfig.MYOBConfig.ConfirmationUrl) then fileConfig.MYOBConfig.ConfirmationUrl else envConfig.MYOBConfig.ConfirmationUrl
                    |}
                }
            | Error error -> Error error
        | Error error -> Error error
    
    /// Validate configuration (pure function)
    let validate (config: InvoicerConfig) : Result<InvoicerConfig, string> =
        let errors = [
            if String.IsNullOrWhiteSpace(config.ConnectionString) then "ConnectionString is required"
            if config.InvoiceBatchSize <= 0 then "InvoiceBatchSize must be positive"
            if config.RefundBatchSize <= 0 then "RefundBatchSize must be positive"
            if config.MinimumFinishDate > DateTime.Now then "MinimumFinishDate cannot be in the future"
        ]
        
        if List.isEmpty errors then
            Ok config
        else
            Error (String.concat "; " errors)
    
    /// Load and validate configuration (IO operation)
    let loadAndValidate () : Result<InvoicerConfig, string> =
        match load () with
        | Ok config -> validate config
        | Error error -> Error error
