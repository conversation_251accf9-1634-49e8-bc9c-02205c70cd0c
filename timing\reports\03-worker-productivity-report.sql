-- Worker Productivity Report
-- How much time each worker spends on different job types/statuses

WITH WorkerTiming AS (
    SELECT
        e.UserId,
        e.WorkstationId,
        e.JobId,
        j.Name AS JobName,
        e.Status,
        e.EventType,
        e.EventTime,
        LAG(e.EventTime) OVER (PARTITION BY e.JobId, e.Status, e.UserId ORDER BY e.EventTime) AS PrevEventTime,
        LAG(e.EventType) OVER (PARTITION BY e.JobId, e.Status, e.UserId ORDER BY e.EventTime) AS PrevEventType
    FROM JobStepTimingEvent e
        INNER JOIN Job j ON e.JobId = j.Id
    WHERE e.EventType IN (1, 4) -- PLAY (1) and FINISH (4)
),
WorkerStats AS (
    SELECT
        UserId,
        WorkstationId,
        JobId,
        JobName,
        Status,
        SUM(CASE WHEN EventType = 4 AND PrevEventType = 1 THEN -- FINISH after PLAY
            DATEDIFF(SECOND, PrevEventTime, EventTime)
        END) AS TotalSecondsWorked
    FROM WorkerTiming
    GROUP BY UserId, WorkstationId, JobId, JobName, Status
)
SELECT
    ws.UserId,
    u.UserName,
    ws.WorkstationId,
    ws.Status,
    COUNT(DISTINCT ws.JobId) AS JobsWorked,
    SUM(ws.TotalSecondsWorked) AS TotalSecondsWorked,
    FORMAT(DATEADD(SECOND, SUM(ws.TotalSecondsWorked), 0), 'HH:mm:ss') AS TotalTimeFormatted,
    AVG(ws.TotalSecondsWorked) AS AvgSecondsPerJob,
    FORMAT(DATEADD(SECOND, AVG(ws.TotalSecondsWorked), 0), 'HH:mm:ss') AS AvgTimePerJobFormatted
FROM WorkerStats ws
    INNER JOIN LepUser u ON ws.UserId = u.Id
WHERE ws.TotalSecondsWorked IS NOT NULL
GROUP BY ws.UserId, u.UserName, ws.WorkstationId, ws.Status
ORDER BY ws.UserId, SUM(ws.TotalSecondsWorked) DESC;
