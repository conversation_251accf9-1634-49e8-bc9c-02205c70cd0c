---
status: Printing
title: Press Checkpoint Standards
type: checkpoints
estimatedDuration: 10
isActive: true
requiredTools:
  - Job Bag
  - Color scanning equipment
  - Quality control materials
  - Runners for testing
qualityChecks:
  - Read Additional Information section
  - Check NCR reprint issues
  - Verify correct paper and plates
  - Ensure sufficient stock available
  - Check scanned color bars within standard
  - Verify page numbers match folded sample
  - Check for hickeys, scratches and marks
  - Sign OK to Run sheet
---

# Press Checkpoint Standards

**Prior to Printing a Run** - *Oct 2018*

## Pre-Printing Checklist ✅

### 1. Job Documentation Review
- [ ] **Read Additional Information section** and action accordingly
- [ ] **Check any NCR reprint issues** indicated by red tag

### 2. Materials Verification
- [ ] **Correct Paper and Plates** used
- [ ] **Ensure sufficient stock available** to complete run
  - [ ] On runs of 5000 sheets+, should have been ordered specially
  - [ ] Over and above normal stock replacements

### 3. After Printing Runners Check
- [ ] **Scanned colour bars** are printing within standard
- [ ] **Bookwork** - page numbers match folded sample
  - [ ] If obvious errors spotted (not typos) raise as QC issue
  - [ ] During 'out of hours' check Job Board to see file supplied
- [ ] **Divide sheet into sections** and check for:
  - [ ] Hickeys
  - [ ] Scratches  
  - [ ] General marks
- [ ] **Fix where required**, print more runners and OK any fixes

### 4. Final Setup Verification
- [ ] **Press sheet run quantity** first side (including overs) set correctly as per Run Job Bag

### 5. Final Sign-Off
- [ ] **Sign 'OK to Run' sheet** (final side)
- [ ] **Place in Run Job Bag**

---

> 🎯 **Critical**: Quality control during runner printing prevents waste and ensures consistent production quality.
