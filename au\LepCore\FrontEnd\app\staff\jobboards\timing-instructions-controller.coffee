appStaff = angular.module('app.staff')

appStaff.controller 'TimingInstructionsController', [
    '$scope'
    ($scope) ->

        # Initialize scope variables
        $scope.instructionsRead = false

        # Check if all quality checks are completed
        $scope.allQualityChecksCompleted = () ->
            return true if !$scope.ngDialogData.instructions?.qualityChecks
            
            for check in $scope.ngDialogData.instructions.qualityChecks
                return false if !check.completed
            true

        # Start timing action
        $scope.startTiming = () ->
            result = 
                confirmed: true
                instructionsViewed: !!$scope.ngDialogData.instructions
                qualityChecksPassed: $scope.allQualityChecksCompleted()
            
            $scope.confirm(result)

        @
]
