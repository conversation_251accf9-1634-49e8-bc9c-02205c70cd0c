-- Performance Trends Over Time
-- Weekly performance trends by status

WITH WeeklyTiming AS (
    SELECT
        DATEPART(YEAR, e.EventTime) AS Year,
        DATEPART(WEEK, e.EventTime) AS WeekNumber,
        CONCAT(DATEPART(YEAR, e.EventTime), '-W', FORMAT(DATEPART(WEEK, e.EventTime), '00')) AS YearWeek,
        e.Status,
        e.JobId,
        e.EventType,
        e.EventTime,
        LAG(e.EventTime) OVER (PARTITION BY e.JobId, e.Status ORDER BY e.EventTime) AS PrevEventTime,
        LAG(e.EventType) OVER (PARTITION BY e.JobId, e.Status ORDER BY e.EventTime) AS PrevEventType
    FROM JobStepTimingEvent e
    WHERE e.EventTime >= DATEADD(WEEK, -12, GETDATE()) -- Last 12 weeks
        AND e.EventType IN (1, 4) -- PLAY (1) and FINISH (4)
),
WeeklyStats AS (
    SELECT
        Year,
        WeekNumber,
        YearWeek,
        Status,
        COUNT(DISTINCT JobId) AS JobsCompleted,
        AVG(CASE WHEN EventType = 4 AND PrevEventType = 1 THEN
            DATEDIFF(SECOND, PrevEventTime, EventTime)
        END) AS AvgDurationSeconds
    FROM WeeklyTiming
    GROUP BY Year, WeekNumber, YearWeek, Status
    HAVING COUNT(DISTINCT JobId) >= 1 -- Only include weeks with meaningful data
)
SELECT
    ws.YearWeek,
    ws.Status,
    ws.JobsCompleted,
    ws.AvgDurationSeconds,
    FORMAT(DATEADD(SECOND, ws.AvgDurationSeconds, 0), 'HH:mm:ss') AS AvgFormattedDuration,
    LAG(ws.AvgDurationSeconds) OVER (PARTITION BY ws.Status ORDER BY ws.Year, ws.WeekNumber) AS PrevWeekAvgDuration,
    CASE
        WHEN LAG(ws.AvgDurationSeconds) OVER (PARTITION BY ws.Status ORDER BY ws.Year, ws.WeekNumber) IS NOT NULL
        THEN ROUND(((ws.AvgDurationSeconds / LAG(ws.AvgDurationSeconds) OVER (PARTITION BY ws.Status ORDER BY ws.Year, ws.WeekNumber)) - 1) * 100, 1)
    END AS PercentChangeFromPrevWeek
FROM WeeklyStats ws
ORDER BY ws.Status, ws.Year, ws.WeekNumber;
