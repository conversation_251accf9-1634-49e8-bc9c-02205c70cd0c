# Database Optimization Scripts for PRD_AU

## Overview
This directory contains comprehensive database optimization scripts designed to analyze and improve the performance of the PRD_AU database on SRV03. The scripts focus on index optimization, query performance analysis, and statistics management.

## Files Description

### Core Scripts
1. **`01_database_analysis.sql`** - Comprehensive performance analysis (read-only)
2. **`02_index_optimization.sql`** - Implementation of optimization recommendations
3. **`03_rollback_optimization.sql`** - Rollback script for optimization changes

### Execution Tools
4. **`execute_optimization.bat`** - Windows batch file for automated execution
5. **`execute_optimization.ps1`** - PowerShell script with advanced error handling
6. **`04_execution_instructions.md`** - Detailed execution instructions

### Documentation
7. **`README.md`** - This file

## Quick Start

### Option 1: Automated Execution (Recommended)
```cmd
cd C:\LepSF\au\dbOptimise
execute_optimization.bat
```

### Option 2: PowerShell Execution
```powershell
cd C:\LepSF\au\dbOptimise
.\execute_optimization.ps1
```

### Option 3: Manual Execution
1. Execute `01_database_analysis.sql` in SSMS
2. Review results
3. Execute `02_index_optimization.sql` during maintenance window
4. Monitor performance

## Target Database
- **Server**: SRV03
- **Database**: PRD_AU
- **Focus Tables**: Order, Job, Runs
- **Connection**: Integrated with existing LepCore connection string

## Optimization Areas

### 1. Missing Index Analysis
- Identifies high-impact missing indexes
- Generates CREATE INDEX statements
- Prioritizes by improvement potential

### 2. Index Fragmentation
- Detects fragmented indexes (>10% fragmentation)
- Recommends REBUILD vs REORGANIZE
- Optimizes fill factors

### 3. Unused Index Detection
- Finds indexes with no usage but high maintenance cost
- Provides DROP INDEX recommendations
- Helps reduce storage and update overhead

### 4. Query Performance Analysis
- Identifies top 20 most expensive queries
- Analyzes execution plans
- Highlights bottlenecks (scans, lookups, sorts)

### 5. Table Structure Review
- Flags tables with excessive columns
- Identifies oversized data types
- Recommends structural improvements

### 6. Statistics Management
- Updates outdated statistics
- Improves cardinality estimates
- Enhances query plan quality

## Expected Performance Improvements

### Query Performance
- **Response Time**: 20-50% improvement for queries with missing indexes
- **I/O Reduction**: 30-70% reduction in logical reads
- **CPU Usage**: 10-30% reduction in CPU time

### Index Efficiency
- **Fragmentation**: Reduction from >30% to <5%
- **Storage**: 10-20% reduction in index storage
- **Maintenance**: Improved backup and maintenance speeds

## Safety Features

### Built-in Safeguards
- **Read-only analysis** phase before any changes
- **Idempotent scripts** - safe to run multiple times
- **Error handling** with detailed logging
- **Rollback capabilities** for all changes

### Risk Mitigation
- **Impact assessment** for each recommendation
- **Maintenance window** scheduling recommendations
- **Backup verification** before execution
- **Incremental execution** with confirmation prompts

## Prerequisites

### Permissions Required
- `db_ddladmin` - For creating/dropping indexes
- `db_datawriter` - For updating statistics
- `db_datareader` - For analysis queries

### System Requirements
- SQL Server 2016 or later
- sqlcmd utility installed
- Sufficient disk space (2x largest table size)
- Maintenance window for optimization phase

### Backup Requirements
- **MANDATORY**: Full database backup before optimization
- Verify backup integrity
- Test restore procedure

## Execution Phases

### Phase 1: Analysis (5-10 minutes)
- **Risk**: None (read-only)
- **Output**: Performance analysis report
- **Action**: Review recommendations

### Phase 2: Optimization (15-60 minutes)
- **Risk**: Medium (DDL operations)
- **Output**: Created indexes, rebuilt indexes, updated statistics
- **Action**: Monitor execution progress

### Phase 3: Verification (2-5 minutes)
- **Risk**: None (read-only)
- **Output**: Verification of applied changes
- **Action**: Confirm successful completion

## Monitoring and Maintenance

### Post-Optimization Monitoring
1. **24-48 hours**: Monitor query performance metrics
2. **1 week**: Verify application functionality
3. **1 month**: Assess long-term performance impact

### Regular Maintenance
- **Weekly**: Check index fragmentation levels
- **Monthly**: Review missing index recommendations
- **Quarterly**: Full optimization review

## Troubleshooting

### Common Issues
| Issue | Cause | Solution |
|-------|-------|----------|
| Permission denied | Insufficient rights | Grant db_ddladmin role |
| Lock timeout | High database activity | Execute during maintenance window |
| Disk space error | Insufficient space | Free up disk space or move to larger drive |
| Online operation failed | Edition limitations | Use offline operations |

### Error Recovery
1. **Check error logs** in output files
2. **Review SQL Server error log**
3. **Consider partial rollback** if needed
4. **Contact DBA team** for complex issues

## Performance Baselines

### Before Optimization (Typical)
- Missing indexes: 10-20 high-impact opportunities
- Fragmented indexes: 30-50% of indexes >10% fragmented
- Outdated statistics: 20-40% of tables
- Query performance: Baseline measurements required

### After Optimization (Expected)
- Missing indexes: <5 remaining opportunities
- Fragmented indexes: <10% of indexes >10% fragmented
- Updated statistics: 100% current
- Query performance: 20-50% improvement

## Support and Documentation

### Log Files Location
- **Default**: `C:\LepSF\au\dbOptimise\logs\`
- **Analysis**: `01_analysis_YYYYMMDD_HHMMSS.log`
- **Optimization**: `02_optimization_YYYYMMDD_HHMMSS.log`
- **Verification**: `03_verification_YYYYMMDD_HHMMSS.log`

### Additional Resources
- SQL Server Documentation: Index optimization best practices
- Query Store: For ongoing performance monitoring
- Extended Events: For detailed query analysis

## Version History
- **v1.0**: Initial release with core optimization features
- Focus on PRD_AU database optimization
- Comprehensive analysis and implementation scripts

## Contact and Support
For issues or questions regarding these optimization scripts:
1. Review the execution logs for error details
2. Check the troubleshooting section
3. Consult with the database administration team
4. Document any issues for future improvements

---
**Important**: Always test in a non-production environment first and ensure you have a verified backup before running optimization scripts in production.
