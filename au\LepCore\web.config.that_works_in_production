﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <system.web>
 
    <httpRuntime maxRequestLength="4097152" executionTimeout="3600" targetFramework="4.5" />
  </system.web>
    <system.webServer>
	<rewrite>
            <rules>
                <clear />
                <rule name="LEPONLINE" stopProcessing="true">
                    <match url="(.*)" />
                    <conditions logicalGrouping="MatchAll" trackAllCaptures="false">
                        <add input="{HTTP_HOST}" pattern="^(leponline\.)(.*)$" />
                    </conditions>
                    <action type="Redirect" url="https://my.lepcolourprinters.com.au" appendQueryString="false" redirectType="Found" />
                </rule>
                <rule name="HTTP Redirect to HTTPS" enabled="true" patternSyntax="ECMAScript" stopProcessing="true">
                    <match url="(.*)" />
                    <conditions logicalGrouping="MatchAny" trackAllCaptures="false">
                        <add input="{HTTPS}" pattern="off" />
                    </conditions>
                    <action type="Redirect" url="https://{HTTP_HOST}/{R:1}" redirectType="Found" />
                </rule>
            </rules>
        </rewrite>
    <modules runAllManagedModulesForAllRequests="true"></modules>
      <handlers>
        <add name="aspNetCore" path="*" verb="*" modules="AspNetCoreModuleV2" resourceType="Unspecified" />
      <!-- <add name="Browser Link for HTML" path="*.html" verb="*" type="System.Web.StaticFileHandler, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" resourceType="File" preCondition="integratedMode" /> -->
      </handlers>
	<aspNetCore processPath=".\LepCore.exe" arguments="" stdoutLogEnabled="true" stdoutLogFile=".\logs\stdout" forwardWindowsAuthToken="false" hostingModel="inprocess" requestTimeout="00:20:00" />
	<security>
	 
      <requestFiltering allowDoubleEscaping="true">
        <!-- Measured in Bytes -->
        <requestLimits maxAllowedContentLength="4147483648" />  <!-- 1 GB-->
		     <verbs>
                <remove verb="OPTIONS" />
                <add verb="OPTIONS" allowed="true" />
            </verbs>
      </requestFiltering>
    </security>
        <staticContent>
            <clientCache cacheControlMode="UseMaxAge" cacheControlMaxAge="1.00:00:00" />
        </staticContent>
        <httpRedirect enabled="false" destination="https://my.lepcolourprinters.com.au" exactDestination="false" />
<httpProtocol>
      <customHeaders>
        <remove name="Access-Control-Allow-Origin" />
        <add name="Access-Control-Allow-Origin" value="*" />
        <add name="Access-Control-Allow-Headers" value="*" />
        <add name="Access-Control-Allow-Methods" value="GET, POST,PUT,DELETE, OPTIONS" />
      </customHeaders>
    </httpProtocol>
        
    </system.webServer>
</configuration>