# Timing system constants for consistency between frontend and backend
# These constants match the backend TimingConstants.cs file

window.TimingConstants =
    # Session states (stored as strings in database)
    SessionStates:
        READY: 'READY'
        PLAYING: 'PLAYING'
        PAUSED: 'PAUSED'
        FINISHED: 'FINISHED'
    
    # Event types (stored as strings in database)
    EventTypes:
        START: 'START'
        PAUSE: 'PAUSE'
        RESUME: 'RESUME'
        FINISH: 'FINISH'
    
    # Frontend numeric states (for compatibility with existing frontend code)
    FrontendStates:
        READY: 0
        PLAYING: 1
        PAUSED: 2
        FINISHED: 3
    
    # Convert database string state to frontend numeric state
    toFrontendState: (dbState) ->
        switch dbState
            when @SessionStates.READY then @FrontendStates.READY
            when @SessionStates.PLAYING then @FrontendStates.PLAYING
            when @SessionStates.PAUSED then @FrontendStates.PAUSED
            when @SessionStates.FINISHED then @FrontendStates.FINISHED
            else @FrontendStates.READY
    
    # Convert frontend numeric state to database string state
    toDbState: (frontendState) ->
        switch frontendState
            when @FrontendStates.READY then @SessionStates.READY
            when @FrontendStates.PLAYING then @SessionStates.PLAYING
            when @FrontendStates.PAUSED then @SessionStates.PAUSED
            when @FrontendStates.FINISHED then @SessionStates.FINISHED
            else @SessionStates.READY

# Legacy constants for backward compatibility
# TODO: Replace these with TimingConstants.FrontendStates throughout the codebase
window.TIMING_STATES =
    READY: 0
    PLAYING: 1
    PAUSED: 2
    FINISHED: 3
