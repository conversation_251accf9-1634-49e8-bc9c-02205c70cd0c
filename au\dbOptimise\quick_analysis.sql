-- Quick Database Analysis for PRD_AU
USE [PRD_AU];
GO

PRINT '=== QUICK DATABASE ANALYSIS ===';
PRINT 'Database: ' + DB_NAME();
PRINT 'Server: ' + @@SERVERNAME;
PRINT 'Analysis Date: ' + CONVERT(VARCHAR(20), GETDATE(), 120);
PRINT '';

-- 1. Missing Index Analysis
PRINT '1. TOP MISSING INDEXES:';
SELECT TOP 10
    OBJECT_NAME(mid.object_id) AS table_name,
    ROUND(avg_total_user_cost * avg_user_impact * (user_seeks + user_scans), 0) AS improvement_score,
    mid.equality_columns,
    mid.inequality_columns,
    mid.included_columns,
    'CREATE INDEX [IX_' + OBJECT_NAME(mid.object_id) + '_Missing] ON [' + 
    OBJECT_SCHEMA_NAME(mid.object_id) + '].[' + OBJECT_NAME(mid.object_id) + '] (' +
    ISNULL(mid.equality_columns, '') +
    CASE WHEN mid.equality_columns IS NOT NULL AND mid.inequality_columns IS NOT NULL THEN ', ' ELSE '' END +
    ISNULL(mid.inequality_columns, '') + ')' +
    CASE WHEN mid.included_columns IS NOT NULL THEN ' INCLUDE (' + mid.included_columns + ')' ELSE '' END AS create_script
FROM sys.dm_db_missing_index_details AS mid
INNER JOIN sys.dm_db_missing_index_groups AS mig ON mid.index_handle = mig.index_handle
INNER JOIN sys.dm_db_missing_index_group_stats AS migs ON mig.index_group_handle = migs.group_handle
WHERE mid.database_id = DB_ID()
ORDER BY improvement_score DESC;

PRINT '';

-- 2. Index Fragmentation
PRINT '2. FRAGMENTED INDEXES:';
SELECT TOP 10
    OBJECT_SCHEMA_NAME(ips.object_id) AS schema_name,
    OBJECT_NAME(ips.object_id) AS table_name,
    i.name AS index_name,
    ips.avg_fragmentation_in_percent,
    ips.page_count,
    CASE 
        WHEN ips.avg_fragmentation_in_percent > 30 THEN 'REBUILD'
        WHEN ips.avg_fragmentation_in_percent > 10 THEN 'REORGANIZE'
        ELSE 'OK'
    END AS recommendation
FROM sys.dm_db_index_physical_stats(DB_ID(), NULL, NULL, NULL, 'LIMITED') AS ips
INNER JOIN sys.indexes AS i ON ips.object_id = i.object_id AND ips.index_id = i.index_id
WHERE ips.avg_fragmentation_in_percent > 10
    AND ips.page_count > 100
    AND i.name IS NOT NULL
ORDER BY ips.avg_fragmentation_in_percent DESC;

PRINT '';

-- 3. Table sizes and row counts
PRINT '3. LARGEST TABLES:';
SELECT TOP 10
    t.name AS table_name,
    p.rows AS row_count,
    CAST(ROUND(((SUM(a.total_pages) * 8) / 1024.00), 2) AS NUMERIC(36, 2)) AS total_space_mb,
    CAST(ROUND(((SUM(a.used_pages) * 8) / 1024.00), 2) AS NUMERIC(36, 2)) AS used_space_mb
FROM sys.tables t
INNER JOIN sys.indexes i ON t.object_id = i.object_id
INNER JOIN sys.partitions p ON i.object_id = p.object_id AND i.index_id = p.index_id
INNER JOIN sys.allocation_units a ON p.partition_id = a.container_id
WHERE t.name NOT LIKE 'dt%' AND t.is_ms_shipped = 0 AND i.object_id > 255
GROUP BY t.name, p.rows
ORDER BY used_space_mb DESC;

PRINT '';

-- 4. Focus on Order, Job, Runs tables
PRINT '4. FOCUS TABLES ANALYSIS:';
SELECT 
    t.name AS table_name,
    COUNT(i.index_id) AS index_count,
    SUM(CASE WHEN i.is_primary_key = 1 THEN 1 ELSE 0 END) AS pk_count,
    SUM(CASE WHEN i.type = 2 THEN 1 ELSE 0 END) AS nonclustered_count
FROM sys.tables t
LEFT JOIN sys.indexes i ON t.object_id = i.object_id
WHERE t.name IN ('Order', 'Job', 'Run', 'RunJob', 'Customer', 'LepUser')
GROUP BY t.name
ORDER BY t.name;

PRINT '';

-- 5. Check for unused indexes
PRINT '5. POTENTIALLY UNUSED INDEXES:';
SELECT TOP 10
    OBJECT_SCHEMA_NAME(i.object_id) AS schema_name,
    OBJECT_NAME(i.object_id) AS table_name,
    i.name AS index_name,
    ISNULL(ius.user_seeks, 0) AS user_seeks,
    ISNULL(ius.user_scans, 0) AS user_scans,
    ISNULL(ius.user_lookups, 0) AS user_lookups,
    ISNULL(ius.user_updates, 0) AS user_updates
FROM sys.indexes AS i
LEFT JOIN sys.dm_db_index_usage_stats AS ius ON i.object_id = ius.object_id AND i.index_id = ius.index_id AND ius.database_id = DB_ID()
WHERE i.type_desc IN ('NONCLUSTERED')
    AND i.is_primary_key = 0
    AND i.is_unique_constraint = 0
    AND OBJECTPROPERTY(i.object_id, 'IsUserTable') = 1
    AND (ISNULL(ius.user_seeks, 0) + ISNULL(ius.user_scans, 0) + ISNULL(ius.user_lookups, 0)) = 0
    AND ISNULL(ius.user_updates, 0) > 100
ORDER BY ius.user_updates DESC;

PRINT '';

-- 6. Statistics that need updating
PRINT '6. OUTDATED STATISTICS:';
SELECT TOP 10
    OBJECT_SCHEMA_NAME(s.object_id) AS schema_name,
    OBJECT_NAME(s.object_id) AS table_name,
    s.name AS stats_name,
    STATS_DATE(s.object_id, s.stats_id) AS last_updated,
    DATEDIFF(day, STATS_DATE(s.object_id, s.stats_id), GETDATE()) AS days_old
FROM sys.stats AS s
INNER JOIN sys.tables AS t ON s.object_id = t.object_id
WHERE t.is_ms_shipped = 0
    AND (STATS_DATE(s.object_id, s.stats_id) IS NULL OR DATEDIFF(day, STATS_DATE(s.object_id, s.stats_id), GETDATE()) > 7)
ORDER BY days_old DESC;

PRINT '';
PRINT '=== ANALYSIS COMPLETE ===';
PRINT 'Review results and run optimization scripts as needed.';
