namespace LepInvoicerFSharp

open System
open System.Threading.Tasks
open FsToolkit.ErrorHandling
open LepInvoicerFSharp.Types

// ============================================================================
// MYOB MODULE - Functional MYOB operations using FsToolkit.ErrorHandling
// ============================================================================

module MYOB =

    /// MYOB service state
    type MYOBState = {
        IsInitialized: bool
        CompanyFile: string option
        LastError: string option
    }
    
    /// Create initial MYOB state
    let createInitialState () : MYOBState =
        {
            IsInitialized = false
            CompanyFile = None
            LastError = None
        }
    
    /// Initialize MYOB service (functional)
    let initialize (config: InvoicerConfig) : Async<Result<MYOBState, string>> =
        asyncResult {
            try
                if config.TestMode then
                    // Mock initialization for test mode
                    return {
                        IsInitialized = true
                        CompanyFile = Some config.MYOBConfig.CompanyFileName
                        LastError = None
                    }
                else
                    // Real MYOB initialization would go here
                    // For now, simulate successful initialization
                    do! Async.Sleep(100) // Simulate initialization time

                    return {
                        IsInitialized = true
                        CompanyFile = Some config.MYOBConfig.CompanyFileName
                        LastError = None
                    }
            with
            | ex -> return! Error $"Failed to initialize MYOB: {ex.Message}"
        }
    
    /// Create order invoice in MYOB (functional)
    let createOrderInvoice (state: MYOBState) (order: Order) : Async<Result<string, string>> =
        asyncResult {
            if not state.IsInitialized then
                return! Error "MYOB service not initialized"
            else
                try
                    // Calculate invoice total
                    let totalPrice = OrderLogic.calculateTotalPrice order

                    match totalPrice with
                    | Some price when price > 0m ->
                        // Mock invoice creation
                        let invoiceNumber = $"O{order.Id}"

                        // Simulate MYOB API call
                        do! Async.Sleep(50)

                        return invoiceNumber
                    | _ ->
                        return! Error "Order has invalid price for invoicing"
                with
                | ex -> return! Error $"Failed to create order invoice: {ex.Message}"
        }
    
    /// Create credit invoice in MYOB (functional)
    let createCreditInvoice (state: MYOBState) (credit: OrderCredit) : Async<Result<string, string>> =
        asyncResult {
            if not state.IsInitialized then
                return! Error "MYOB service not initialized"
            else
                try
                    if credit.Amount <= 0m then
                        return! Error "Credit has invalid amount for invoicing"
                    else
                        // Mock credit invoice creation
                        let invoiceNumber = $"CR{credit.Id}"

                        // Simulate MYOB API call
                        do! Async.Sleep(50)

                        return invoiceNumber
                with
                | ex -> return! Error $"Failed to create credit invoice: {ex.Message}"
        }
    
// ============================================================================
// MYOB SERVICE FACADE - High-level functional interface
// ============================================================================

module MYOBService =

    /// Initialize MYOB service and return state
    let initialize (config: InvoicerConfig) : Async<Result<MYOB.MYOBState, string>> =
        MYOB.initialize config

    /// Process order with MYOB (high-level operation)
    let processOrder (state: MYOB.MYOBState) (order: Order) : Async<Result<string, string>> =
        MYOB.createOrderInvoice state order

    /// Process credit with MYOB (high-level operation)
    let processCredit (state: MYOB.MYOBState) (credit: OrderCredit) : Async<Result<string, string>> =
        MYOB.createCreditInvoice state credit
