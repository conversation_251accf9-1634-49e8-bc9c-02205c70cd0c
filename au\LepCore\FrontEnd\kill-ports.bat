@echo off
echo Killing processes on ports 9191 and 35729...

REM Kill all node processes
echo Killing node processes...
taskkill /F /IM node.exe >nul 2>&1
if %errorlevel% == 0 (
    echo Node processes killed
) else (
    echo No node processes to kill
)

REM Kill processes on port 9191
echo Checking port 9191...
for /f "tokens=5" %%a in ('netstat -ano ^| findstr :9191') do (
    echo Killing process %%a on port 9191
    taskkill /F /PID %%a >nul 2>&1
)

REM Kill processes on port 35729
echo Checking port 35729...
for /f "tokens=5" %%a in ('netstat -ano ^| findstr :35729') do (
    echo Killing process %%a on port 35729
    taskkill /F /PID %%a >nul 2>&1
)

echo Waiting for ports to be freed...
timeout /t 2 >nul

echo Done! You can now run gulp.
pause
