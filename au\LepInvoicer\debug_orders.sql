-- Check table structure first
SELECT TOP 1 * FROM [Order] WHERE Id IN (1416838, 1417006);

-- Check specific orders 1416838 and 1417006
SELECT
    Id,
    Invoiced2,
    Invoiced2Details,
    FinishDate
FROM [Order]
WHERE Id IN (1416838, 1417006);

-- Check if they're in Invoicer2Log
SELECT 
    OrderId,
    Success,
    Details,
    DateCreated
FROM Invoicer2Log 
WHERE OrderId IN (1416838, 1417006)
ORDER BY DateCreated DESC;

-- Check what orders are being selected by the current query
SELECT TOP 10
    Id,
    Invoiced2,
    FinishDate
FROM [Order]
WHERE (Invoiced2 IS NULL OR (Invoiced2 NOT IN ('Y', 'F', 'C')))
    AND FinishDate IS NOT NULL
    AND YEAR(FinishDate) != 1
    AND FinishDate >= '2024-02-01'
ORDER BY Id;
