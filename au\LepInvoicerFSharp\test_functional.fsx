// Test our functional approach step by step
open System

printfn "=== Testing Functional LEP Invoicer ==="

// Load our clean modules
#load "Types_Clean.fs"
#load "Configuration_Clean.fs"
#load "Database_Clean.fs"
#load "MYOB_Clean.fs"
#load "OrderProcessing_Clean.fs"

open LepInvoicerFSharp

// Test the types and functions
let testConfig = {
    ConnectionString = "test connection"
    InvoiceBatchSize = 10
    RefundBatchSize = 5
    MinimumFinishDate = DateTime.Now.AddDays(-1.0)
    IgnoreCustomers = []
    TestMode = true
    MYOBConfig = {|
        CompanyFileName = "test.myob"
        Username = "test"
        Password = "test"
        ConfirmationUrl = "http://test"
    |}
}

printfn "\n=== Configuration Test ==="
ConfigHelpers.printSummary testConfig

printfn "\n=== Database Test ==="
let testDatabaseOperations () = async {
    let! ordersResult = Database.getOrdersToInvoice testConfig 5
    match ordersResult with
    | Ok orders -> 
        printfn "Found %d orders to invoice" (List.length orders)
        orders |> List.iter (fun (id, username) -> printfn "  Order %d for %s" id username)
    | Error error -> printfn "Error getting orders: %s" error
    
    let! creditsResult = Database.getCreditsToInvoice testConfig 5
    match creditsResult with
    | Ok credits -> 
        printfn "Found %d credits to invoice" (List.length credits)
        credits |> List.iter (fun credit -> printfn "  Credit %d: $%.2f" credit.Id credit.Amount)
    | Error error -> printfn "Error getting credits: %s" error
}

testDatabaseOperations () |> Async.RunSynchronously

printfn "\n=== MYOB Test ==="
let testMYOBOperations () = async {
    let! myobResult = MYOBService.initialize testConfig
    match myobResult with
    | Ok myobState -> 
        printfn "MYOB initialized successfully"
        printfn "  Company file: %A" myobState.CompanyFile
        printfn "  Is initialized: %b" myobState.IsInitialized
    | Error error -> printfn "MYOB initialization failed: %s" error
}

testMYOBOperations () |> Async.RunSynchronously

printfn "\n=== Full Processing Test ==="
let testFullProcessing () = async {
    let! myobResult = MYOBService.initialize testConfig
    match myobResult with
    | Ok myobState ->
        let! statsResult = OrderProcessing.processAllPendingWork testConfig myobState
        match statsResult with
        | Ok stats ->
            printfn "Processing completed successfully!"
            printfn "  Orders processed: %d (successful: %d, failed: %d)" stats.OrdersProcessed stats.OrdersSuccessful stats.OrdersFailed
            printfn "  Credits processed: %d" stats.CreditsProcessed
            printfn "  Refunds processed: %d" stats.RefundsProcessed
            printfn "  Elapsed time: %A" stats.ElapsedTime
            if not (List.isEmpty stats.Errors) then
                printfn "  Errors: %d" (List.length stats.Errors)
                stats.Errors |> List.iter (printfn "    %s")
        | Error error -> printfn "Processing failed: %s" error
    | Error error -> printfn "MYOB initialization failed: %s" error
}

testFullProcessing () |> Async.RunSynchronously

printfn "\n=== All tests completed! ==="
