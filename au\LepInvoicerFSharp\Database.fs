namespace LepInvoicer<PERSON>harp

open System
open System.Threading.Tasks
open FSharp.Data
open FSharp.Data.SqlClient
open FsToolkit.ErrorHandling
open LepInvoicerFSharp.Types

// ============================================================================
// DATABASE MODULE - F# SQL Type Providers + Functional patterns
// ============================================================================

module Database =

    // SQL Type Provider for compile-time SQL checking
    [<Literal>]
    let private ConnectionString = "Data Source=SRV03;user id=sa; password=*************; Initial Catalog=PRD_AU;TrustServerCertificate=true"

    type LepDb = SqlProgrammabilityProvider<ConnectionString>
    type GetOrdersToInvoice = SqlCommandProvider<"
        SELECT TOP (@batchSize)
            o.Id,
            o.OrderNr,
            c.Name as CustomerName,
            c.Username as CustomerUsername,
            o.FinishDate,
            o.SubmissionDate,
            o.PurchaseOrder,
            o.PromotionBenefit,
            o.PickUpCharge,
            o.GST,
            o.Invoiced2
        FROM [Order] o
        INNER JOIN CustomerUser c ON o.CustomerId = c.Id
        WHERE
            c.Name NOT IN @ignoreCustomers
            AND (o.Invoiced2 IS NULL OR o.Invoiced2 NOT IN ('Y', 'F', 'C'))
            AND o.FinishDate IS NOT NULL
            AND YEAR(o.FinishDate) != 1
            AND CAST(o.FinishDate AS DATE) >= @minFinishDate
        ORDER BY o.Id DESC
    ", ConnectionString>

    type GetOrderJobs = SqlCommandProvider<"
        SELECT
            j.Id,
            j.OrderId,
            j.Price,
            j.Description,
            j.Enable
        FROM Job j
        WHERE j.OrderId = @orderId
    ", ConnectionString>

    type GetCreditsToInvoice = SqlCommandProvider<"
        SELECT TOP (@batchSize)
            oc.Id,
            oc.Type,
            oc.Amount,
            oc.Description,
            oc.OrderId,
            oc.CustomerId,
            oc.Invoiced,
            oc.DateCreated,
            c.Name as CustomerName
        FROM OrderCredit oc
        INNER JOIN CustomerUser c ON oc.CustomerId = c.Id
        LEFT JOIN [Order] o ON oc.OrderId = o.Id
        WHERE
            oc.Invoiced = 0
            AND oc.Type IN ('C', 'M', 'CI')
            AND oc.Amount > 0
            AND c.Name NOT IN @ignoreCustomers
        ORDER BY oc.DateCreated
    ", ConnectionString>

    type GetRefundsToInvoice = SqlCommandProvider<"
        SELECT TOP (@batchSize)
            oc.Id,
            oc.Type,
            oc.Amount,
            oc.Description,
            oc.OrderId,
            oc.CustomerId,
            oc.Invoiced,
            oc.DateCreated,
            c.Name as CustomerName
        FROM OrderCredit oc
        INNER JOIN CustomerUser c ON oc.CustomerId = c.Id
        WHERE
            oc.Invoiced = 0
            AND oc.Type = 'S'
            AND oc.Amount > 0
            AND c.Name NOT IN @ignoreCustomers
        ORDER BY oc.DateCreated
    ", ConnectionString>

    type MarkOrderInvoiced = SqlCommandProvider<"
        UPDATE [Order]
        SET Invoiced2 = 'Y'
        WHERE Id = @orderId
    ", ConnectionString>

    type MarkOrderFailed = SqlCommandProvider<"
        UPDATE [Order]
        SET Invoiced2 = 'F', Invoiced2Details = @errorMessage
        WHERE Id = @orderId
    ", ConnectionString>

    type MarkCreditInvoiced = SqlCommandProvider<"
        UPDATE OrderCredit
        SET Invoiced = 1
        WHERE Id = @creditId
    ", ConnectionString>

    // ========================================================================
    // CONVERSION FUNCTIONS - SQL results to F# domain types
    // ========================================================================

    /// Convert SQL job result to F# Job
    let private convertJob (jobRow: GetOrderJobs.Record) : Job =
        {
            Id = jobRow.Id
            OrderId = jobRow.OrderId
            Price = jobRow.Price
            Description = jobRow.Description |> Option.defaultValue ""
            IsEnabled = jobRow.Enable |> Option.defaultValue true
        }

    /// Convert SQL order result to F# Order (with jobs)
    let private convertOrder (orderRow: GetOrdersToInvoice.Record) (jobs: Job list) : Order =
        {
            Id = orderRow.Id
            OrderNr = orderRow.OrderNr |> Option.defaultValue ""
            Customer = {
                Id = 0 // Would need separate query for customer ID
                Name = orderRow.CustomerName
                Username = orderRow.CustomerUsername
                Email = None
            }
            Jobs = jobs
            Status = Completed
            PaymentStatus = Unpaid
            InvoiceStatus =
                match orderRow.Invoiced2 with
                | Some "Y" -> Invoiced
                | Some "F" -> Failed
                | Some "C" -> CustomerNotFound
                | _ -> NotInvoiced
            FinishDate = orderRow.FinishDate
            SubmissionDate = orderRow.SubmissionDate
            PurchaseOrder = orderRow.PurchaseOrder
            PromotionBenefit = orderRow.PromotionBenefit
            PickUpCharge = orderRow.PickUpCharge
            GST = orderRow.GST
        }

    /// Convert SQL credit result to F# OrderCredit
    let private convertCredit (creditRow: GetCreditsToInvoice.Record) : OrderCredit =
        {
            Id = creditRow.Id
            Type =
                match creditRow.Type with
                | "C" -> Credit
                | "M" -> Miscellaneous
                | "CI" -> CreditInvoice
                | "S" -> Refund
                | _ -> Credit
            Amount = creditRow.Amount
            Description = creditRow.Description
            OrderId = creditRow.OrderId
            CustomerId = creditRow.CustomerId
            IsInvoiced = creditRow.Invoiced
            DateCreated = creditRow.DateCreated
        }

    /// Convert SQL refund result to F# OrderCredit
    let private convertRefund (refundRow: GetRefundsToInvoice.Record) : OrderCredit =
        {
            Id = refundRow.Id
            Type =
                match refundRow.Type with
                | "S" -> Refund
                | _ -> Refund
            Amount = refundRow.Amount
            Description = refundRow.Description
            OrderId = refundRow.OrderId
            CustomerId = refundRow.CustomerId
            IsInvoiced = refundRow.Invoiced
            DateCreated = refundRow.DateCreated
        }

    // ========================================================================
    // FUNCTIONAL DATABASE OPERATIONS - Using FsToolkit.ErrorHandling
    // ========================================================================

    /// Get orders to invoice with functional error handling
    let getOrdersToInvoice (config: InvoicerConfig) (batchSize: int) : Async<Result<(int * string) list, string>> =
        asyncResult {
            try
                // Get candidate orders using type provider
                let! orderRows =
                    GetOrdersToInvoice.AsyncExecute(
                        batchSize = (batchSize * 3), // Get more to account for filtering
                        ignoreCustomers = config.IgnoreCustomers,
                        minFinishDate = config.MinimumFinishDate
                    )
                    |> Async.map Array.toList

                // Get jobs for each order and convert to domain types
                let! ordersWithJobs =
                    orderRows
                    |> List.map (fun orderRow -> asyncResult {
                        let! jobRows = GetOrderJobs.AsyncExecute(orderId = orderRow.Id)
                        let jobs = jobRows |> Array.map convertJob |> Array.toList
                        let order = convertOrder orderRow jobs
                        return order
                    })
                    |> List.sequenceAsyncResultM

                // Filter orders using business logic and take only what we need
                let validOrders =
                    ordersWithJobs
                    |> List.filter (OrderLogic.isReadyForInvoicing config)
                    |> List.take (min batchSize (List.length ordersWithJobs))
                    |> List.map (fun order -> (order.Id, order.Customer.Username))

                return validOrders
            with
            | ex -> return! Error $"Failed to get orders to invoice: {ex.Message}"
        }

    /// Get credits to invoice with functional error handling
    let getCreditsToInvoice (config: InvoicerConfig) (batchSize: int) : Async<Result<OrderCredit list, string>> =
        asyncResult {
            try
                let! creditRows =
                    GetCreditsToInvoice.AsyncExecute(
                        batchSize = batchSize,
                        ignoreCustomers = config.IgnoreCustomers
                    )
                    |> Async.map Array.toList

                let credits =
                    creditRows
                    |> List.map convertCredit
                    |> List.filter (CreditLogic.isReadyForInvoicing config)

                return credits
            with
            | ex -> return! Error $"Failed to get credits to invoice: {ex.Message}"
        }

    /// Get refunds to invoice with functional error handling
    let getRefundsToInvoice (config: InvoicerConfig) (batchSize: int) : Async<Result<OrderCredit list, string>> =
        asyncResult {
            try
                let! refundRows =
                    GetRefundsToInvoice.AsyncExecute(
                        batchSize = batchSize,
                        ignoreCustomers = config.IgnoreCustomers
                    )
                    |> Async.map Array.toList

                let refunds =
                    refundRows
                    |> List.map convertRefund
                    |> List.filter (CreditLogic.isReadyForInvoicing config)

                return refunds
            with
            | ex -> return! Error $"Failed to get refunds to invoice: {ex.Message}"
        }

    /// Mark order as invoiced
    let markOrderInvoiced (orderId: int) : Async<Result<unit, string>> =
        asyncResult {
            try
                let! _ = MarkOrderInvoiced.AsyncExecute(orderId = orderId)
                return ()
            with
            | ex -> return! Error $"Failed to mark order {orderId} as invoiced: {ex.Message}"
        }

    /// Mark order as failed
    let markOrderFailed (orderId: int) (errorMessage: string) : Async<Result<unit, string>> =
        asyncResult {
            try
                let! _ = MarkOrderFailed.AsyncExecute(orderId = orderId, errorMessage = errorMessage)
                return ()
            with
            | ex -> return! Error $"Failed to mark order {orderId} as failed: {ex.Message}"
        }

    /// Mark credit as invoiced
    let markCreditInvoiced (creditId: int) : Async<Result<unit, string>> =
        asyncResult {
            try
                let! _ = MarkCreditInvoiced.AsyncExecute(creditId = creditId)
                return ()
            with
            | ex -> return! Error $"Failed to mark credit {creditId} as invoiced: {ex.Message}"
        }

    /// Check for pending work (functional composition)
    let checkForPendingWork (config: InvoicerConfig) : Async<Result<bool * string, string>> =
        asyncResult {
            let! orders = getOrdersToInvoice config config.InvoiceBatchSize
            let! credits = getCreditsToInvoice config config.RefundBatchSize
            let! refunds = getRefundsToInvoice config config.RefundBatchSize

            let totalWork = List.length orders + List.length credits + List.length refunds
            let hasPendingWork = totalWork > 0

            let workDescription =
                [
                    if not (List.isEmpty orders) then $"{List.length orders} orders"
                    if not (List.isEmpty credits) then $"{List.length credits} credits"
                    if not (List.isEmpty refunds) then $"{List.length refunds} refunds"
                ]
                |> String.concat ", "
                |> fun desc -> if String.IsNullOrEmpty(desc) then "No work" else desc

            return (hasPendingWork, workDescription)
        }
    
    /// Database operation monad (like Haskell's IO monad)
    type DbOperation<'T> = DbConnection -> Task<Result<'T, string>>
    
    // ========================================================================
    // MONADIC OPERATIONS - Haskell-like database monad
    // ========================================================================
    
    /// Return/Pure for DbOperation (like Haskell's return)
    let dbReturn (value: 'T) : DbOperation<'T> =
        fun _ -> Task.FromResult(Success value)
    
    /// Bind for DbOperation (like Haskell's >>=)
    let dbBind (operation: DbOperation<'T>) (f: 'T -> DbOperation<'U>) : DbOperation<'U> =
        fun conn -> task {
            let! result = operation conn
            match result with
            | Success value -> 
                let nextOp = f value
                return! nextOp conn
            | Failure error -> 
                return Failure error
        }
    
    /// Map for DbOperation (like Haskell's fmap)
    let dbMap (f: 'T -> 'U) (operation: DbOperation<'T>) : DbOperation<'U> =
        fun conn -> task {
            let! result = operation conn
            return Result.map f result
        }
    
    /// Lift IO operation into DbOperation
    let dbLiftIO (ioOperation: unit -> Task<'T>) : DbOperation<'T> =
        fun _ -> task {
            try
                let! result = ioOperation()
                return Success result
            with
            | ex -> return Failure ex.Message
        }
    
    /// Lift async operation into DbOperation
    let dbLiftAsync (asyncOperation: unit -> Async<'T>) : DbOperation<'T> =
        fun _ -> task {
            try
                let! result = Async.StartAsTask(asyncOperation())
                return Success result
            with
            | ex -> return Failure ex.Message
        }
    
    // ========================================================================
    // MOCK DATA FUNCTIONS - Create mock data for testing
    // ========================================================================

    /// Create mock customer (pure function)
    let private createMockCustomer (id: int) (name: string) (username: string) : Customer =
        {
            Id = id
            Name = name
            Username = username
            Email = Some $"{username}@example.com"
        }

    /// Create mock job (pure function)
    let private createMockJob (id: int) (orderId: int) (price: decimal option) : Job =
        {
            Id = id
            OrderId = orderId
            Price = price
            Description = $"Mock job {id}"
            IsEnabled = true
        }

    /// Create mock order (pure function)
    let private createMockOrder (id: int) (customerName: string) (hasValidPrice: bool) : Order =
        let customer = createMockCustomer id customerName customerName
        let jobs =
            if hasValidPrice then
                [createMockJob (id * 10) id (Some 100m)]
            else
                [createMockJob (id * 10) id None]

        {
            Id = id
            OrderNr = $"ORD{id:D6}"
            Customer = customer
            Jobs = jobs
            Status = Completed
            PaymentStatus = Unpaid
            InvoiceStatus = NotInvoiced
            FinishDate = Some DateTime.Now
            SubmissionDate = Some (DateTime.Now.AddDays(-1.0))
            PurchaseOrder = None
            PromotionBenefit = 0m
            PickUpCharge = 0m
            GST = 10m
        }

    /// Create mock credit (pure function)
    let private createMockCredit (id: int) (amount: decimal) : OrderCredit =
        {
            Id = id
            Type = Credit
            Amount = amount
            Description = Some $"Mock credit {id}"
            OrderId = Some (id + 1000)
            CustomerId = id
            IsInvoiced = false
            DateCreated = DateTime.Now
        }
    
    // ========================================================================
    // DATABASE QUERY OPERATIONS - Functional database queries
    // ========================================================================
    
    /// Get orders to invoice (mock implementation)
    let getOrdersToInvoice (config: InvoicerConfig) (batchSize: int) : DbOperation<(int * string) list> =
        fun conn -> task {
            try
                // Create mock orders for testing
                let mockOrders = [
                    createMockOrder 1001 "TestCustomer1" true   // Valid order
                    createMockOrder 1002 "TestCustomer2" false  // Zero-price order (should be filtered)
                    createMockOrder 1003 "TestCustomer3" true   // Valid order
                ]

                // Filter orders using business logic
                let validOrders =
                    mockOrders
                    |> List.filter (OrderLogic.isReadyForInvoicing config)
                    |> List.take (min batchSize (List.length mockOrders))
                    |> List.map (fun order -> (order.Id, order.Customer.Username))

                return Success validOrders
            with
            | ex -> return Failure $"Failed to get orders to invoice: {ex.Message}"
        }
    
    /// Get credits to invoice (functional database operation)
    let getCreditsToInvoice (config: InvoicerConfig) (batchSize: int) : DbOperation<OrderCredit list> =
        fun conn -> task {
            try
                let creditsQuery = 
                    conn.Session.Query<lep.order.OrderCredit>()
                        .Where(fun c -> not c.Invoiced)
                        .Where(fun c -> c.Type = "C" || c.Type = "M" || c.Type = "CI")
                        .Where(fun c -> c.Order <> null)
                        .Where(fun c -> not (config.IgnoreCustomers |> List.contains c.Order.Customer.Name))
                        .OrderBy(fun c -> c.DateCreated)
                        .Take(batchSize)
                
                let credits = 
                    creditsQuery.ToList() 
                    |> Seq.map convertOrderCredit
                    |> Seq.filter (CreditLogic.isReadyForInvoicing config)
                    |> Seq.toList
                
                return Success credits
            with
            | ex -> return Failure $"Failed to get credits to invoice: {ex.Message}"
        }
    
    /// Get refunds to invoice (functional database operation)
    let getRefundsToInvoice (config: InvoicerConfig) (batchSize: int) : DbOperation<OrderCredit list> =
        fun conn -> task {
            try
                let refundsQuery = 
                    conn.Session.Query<lep.order.OrderCredit>()
                        .Where(fun r -> not r.Invoiced)
                        .Where(fun r -> r.Type = "S")
                        .Where(fun r -> r.Customer <> null)
                        .Where(fun r -> not (config.IgnoreCustomers |> List.contains r.Customer.Name))
                        .OrderBy(fun r -> r.DateCreated)
                        .Take(batchSize)
                
                let refunds = 
                    refundsQuery.ToList() 
                    |> Seq.map convertOrderCredit
                    |> Seq.filter (CreditLogic.isReadyForInvoicing config)
                    |> Seq.toList
                
                return Success refunds
            with
            | ex -> return Failure $"Failed to get refunds to invoice: {ex.Message}"
        }
    
    /// Get specific order by ID (functional database operation)
    let getOrderById (orderId: int) : DbOperation<Order option> =
        fun conn -> task {
            try
                let orderQuery = 
                    conn.Session.Query<lep.order.IOrder>()
                        .Where(fun o -> o.Id = orderId)
                        .FirstOrDefault()
                
                let result = 
                    if orderQuery = null then None
                    else Some (convertOrder orderQuery)
                
                return Success result
            with
            | ex -> return Failure $"Failed to get order {orderId}: {ex.Message}"
        }
    
    /// Execute SQL command (functional database operation)
    let executeSql (sql: string) : DbOperation<int> =
        fun conn -> task {
            try
                use command = new SqlCommand(sql, conn.Connection)
                let! rowsAffected = command.ExecuteNonQueryAsync()
                return Success rowsAffected
            with
            | ex -> return Failure $"Failed to execute SQL: {ex.Message}"
        }
    
    /// Mark order as invoiced (functional database operation)
    let markOrderInvoiced (orderId: int) : DbOperation<unit> =
        let sql = $"UPDATE [Order] SET Invoiced2 = 'Y' WHERE Id = {orderId}"
        executeSql sql |> dbMap ignore
    
    /// Mark order as failed (functional database operation)
    let markOrderFailed (orderId: int) (errorMessage: string) : DbOperation<unit> =
        let escapedMessage = errorMessage.Replace("'", "''")
        let sql = $"UPDATE [Order] SET Invoiced2 = 'F', Invoiced2Details = '{escapedMessage}' WHERE Id = {orderId}"
        executeSql sql |> dbMap ignore
    
    /// Mark credit as invoiced (functional database operation)
    let markCreditInvoiced (creditId: int) : DbOperation<unit> =
        let sql = $"UPDATE OrderCredit SET Invoiced = 1 WHERE Id = {creditId}"
        executeSql sql |> dbMap ignore
    
    // ========================================================================
    // WORK DETECTION - Functional pending work detection
    // ========================================================================
    
    /// Check for pending work (functional composition)
    let checkForPendingWork (config: InvoicerConfig) : DbOperation<bool * string> =
        fun conn -> task {
            let! ordersResult = getOrdersToInvoice config config.InvoiceBatchSize conn
            let! creditsResult = getCreditsToInvoice config config.RefundBatchSize conn  
            let! refundsResult = getRefundsToInvoice config config.RefundBatchSize conn
            
            match ordersResult, creditsResult, refundsResult with
            | Success orders, Success credits, Success refunds ->
                let totalWork = List.length orders + List.length credits + List.length refunds
                let hasPendingWork = totalWork > 0
                
                let workDescription = 
                    [
                        if not (List.isEmpty orders) then $"{List.length orders} orders"
                        if not (List.isEmpty credits) then $"{List.length credits} credits"  
                        if not (List.isEmpty refunds) then $"{List.length refunds} refunds"
                    ]
                    |> String.concat ", "
                    |> fun desc -> if String.IsNullOrEmpty(desc) then "No work" else desc
                
                return Success (hasPendingWork, workDescription)
            | _ ->
                return Failure "Failed to check for pending work"
        }
