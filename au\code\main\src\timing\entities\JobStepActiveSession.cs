using System;
using lep.timing.enums;
using lep;
using lep.job;
using lep.user;

namespace lep.timing.entities
{
    public class JobStepActiveSession : IEntity
    {
        public virtual int Id { get; set; }
        public virtual int JobId { get; set; }
        public virtual int UserId { get; set; }
        public virtual string Status { get; set; }
        public virtual Guid SessionId { get; set; }
        public virtual TimingSessionState CurrentState { get; set; }
        public virtual DateTime StartTime { get; set; }
        public virtual DateTime LastEventTime { get; set; }
        public virtual int TotalPausedDuration { get; set; }
        public virtual bool IsActive { get; set; }
        public virtual DateTime DateCreated { get; set; }
        public virtual DateTime DateModified { get; set; }

        // Navigation properties
        public virtual IJob Job { get; set; }
        public virtual IUser User { get; set; }

        // Calculated properties
        public virtual TimeSpan CurrentWorkingDuration
        {
            get
            {
                if (CurrentState == TimingSessionState.FINISHED)
                {
                    return TimeSpan.FromSeconds(0);
                }

                var totalElapsed = (DateTime.Now - StartTime).TotalSeconds;
                var workingTime = totalElapsed - TotalPausedDuration;
                return TimeSpan.FromSeconds(Math.Max(0, workingTime));
            }
        }

        public virtual TimeSpan TotalElapsedTime
        {
            get
            {
                return DateTime.Now - StartTime;
            }
        }

        public JobStepActiveSession()
        {
            SessionId = Guid.NewGuid();
            StartTime = DateTime.Now;
            LastEventTime = DateTime.Now;
            DateCreated = DateTime.Now;
            DateModified = DateTime.Now;
            IsActive = true;
            TotalPausedDuration = 0;
            CurrentState = TimingSessionState.PLAYING;
        }
    }
}
