namespace LepInvoicerFSharp

open System
open Microsoft.Extensions.Logging
open LepInvoicerFSharp

// ============================================================================
// MAIN PROGRAM - Functional application pipeline
// ============================================================================

module Program =
    
    /// Setup logging
    let setupLogging () =
        use loggerFactory = LoggerFactory.Create(fun builder ->
            builder.AddConsole() |> ignore
        )
        loggerFactory.CreateLogger("LepInvoicerFSharp")
    
    /// Main application workflow using functional composition
    let runInvoicer () : AsyncResult<int, string> =
        async {
            let logger = setupLogging()
            let startTime = DateTime.Now
            
            logger.LogInformation("=== LEP Invoicer F# - Functional Implementation ===")
            
            // Load and validate configuration
            let configResult = Configuration.loadAndValidate ()
            
            match configResult with
            | Ok config ->
                logger.LogInformation("Configuration loaded successfully")
                ConfigHelpers.printSummary config
                
                // Check for pending work (smart optimization)
                let! workResult = Database.checkForPendingWork config
                
                match workResult with
                | Ok (hasPendingWork, workDescription) ->
                    if not hasPendingWork then
                        logger.LogInformation("No pending work found - skipping MYOB initialization")
                        let elapsed = DateTime.Now - startTime
                        logger.LogInformation($"LEP Invoicer completed successfully in {elapsed.TotalMilliseconds:F0}ms. No work to process.")
                        return Ok 0
                    else
                        logger.LogInformation($"Found pending work: {workDescription}")
                        
                        // Initialize MYOB (only when work is available)
                        let! myobResult = MYOBService.initialize config
                        
                        match myobResult with
                        | Ok myobState ->
                            logger.LogInformation("MYOB service initialized")
                            
                            // Process all work using functional pipeline
                            let! statsResult = OrderProcessing.processAllPendingWork config myobState
                            
                            match statsResult with
                            | Ok stats ->
                                let elapsed = DateTime.Now - startTime
                                logger.LogInformation($"LEP Invoicer completed successfully in {elapsed.TotalMilliseconds:F0}ms.")
                                logger.LogInformation($"Orders: {stats.OrdersProcessed} (Success: {stats.OrdersSuccessful}, Failed: {stats.OrdersFailed})")
                                logger.LogInformation($"Credits: {stats.CreditsProcessed}, Refunds: {stats.RefundsProcessed}")
                                
                                if not (List.isEmpty stats.Errors) then
                                    logger.LogWarning($"Errors encountered: {List.length stats.Errors}")
                                    stats.Errors |> List.iter (logger.LogError)
                                
                                return Ok 0
                            | Error error ->
                                logger.LogError($"Processing failed: {error}")
                                return Error error
                        | Error error ->
                            logger.LogError($"MYOB initialization failed: {error}")
                            return Error error
                | Error error ->
                    logger.LogError($"Failed to check for pending work: {error}")
                    return Error error
            | Error error ->
                printfn $"Configuration loading failed: {error}"
                return Error error
        }
    
    /// Application entry point (functional)
    [<EntryPoint>]
    let main (args: string[]) : int =
        async {
            let! result = runInvoicer ()
            match result with
            | Ok exitCode -> return exitCode
            | Error error -> 
                printfn $"Application failed: {error}"
                return 1
        }
        |> Async.RunSynchronously
