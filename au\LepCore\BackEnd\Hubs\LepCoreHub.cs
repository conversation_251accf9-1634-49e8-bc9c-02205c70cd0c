using Microsoft.AspNetCore.SignalR;
using Microsoft.AspNetCore.Authorization;
using Serilog;
using System;
using System.Collections.Generic;
using System.Collections.Concurrent;
using System.Threading.Tasks;
using System.Linq;
using System.Text.Json;
using System.Text.Json.Serialization;
using lep.timing;
using lep.timing.dto;
using lep.job;
using lep.order;
using lep.jobmonitor.impl;
using lep.jobmonitor;

namespace LepCore.BackEnd.Hubs
{
	// //[Authorize]
	[AllowAnonymous]
    public class LepCoreHub : Hub
    {
        private readonly ITimingApplication _timingApp;
        private readonly JobBoard _globalJobBoard;
        private readonly JobBoardDTOHelper _jobBoardHelper;

        // Track user connections and their subscriptions
        private static readonly ConcurrentDictionary<string, string> UserConnections = new();
        private static readonly ConcurrentDictionary<string, HashSet<string>> UserSubscriptions = new();

        public LepCoreHub(ITimingApplication timingApp, JobBoard globalJobBoard, JobBoardDTOHelper jobBoardHelper)
        {
            _timingApp = timingApp;
            _globalJobBoard = globalJobBoard;
            _jobBoardHelper = jobBoardHelper;
        }

        #region Connection Management

        public override async Task OnConnectedAsync()
        {
            var userId = GetUserId();
            UserConnections[Context.ConnectionId] = userId;
            UserSubscriptions[userId] = new HashSet<string>();

            Log.Information("🔌 SignalR: User {UserId} connected with ConnectionId {ConnectionId}", userId, Context.ConnectionId);
            await base.OnConnectedAsync();
        }

        public override async Task OnDisconnectedAsync(Exception exception)
        {
            var userId = GetUserId();
            UserConnections.TryRemove(Context.ConnectionId, out _);
            UserSubscriptions.TryRemove(userId, out _);

            Log.Information("🔌 SignalR: User {UserId} disconnected", userId);
            await base.OnDisconnectedAsync(exception);
        }

        #endregion

        #region Job Board Subscriptions

        [HubMethodName("SubscribeToJobBoard")]
        public async Task SubscribeToJobBoard(string boardType, string facility = null)
        {
            var userId = GetUserId();
            var groupName = $"JobBoard_{boardType}_{facility ?? "All"}";

            await Groups.AddToGroupAsync(Context.ConnectionId, groupName);

            if (UserSubscriptions.TryGetValue(userId, out var subscriptions))
            {
                subscriptions.Add(groupName);
            }

            Log.Information("📋 SignalR: User {UserId} subscribed to {GroupName}", userId, groupName);
            await Clients.Caller.SendAsync("SubscriptionConfirmed", groupName);
        }

        [HubMethodName("UnsubscribeFromJobBoard")]
        public async Task UnsubscribeFromJobBoard(string boardType, string facility = null)
        {
            var userId = GetUserId();
            var groupName = $"JobBoard_{boardType}_{facility ?? "All"}";

            await Groups.RemoveFromGroupAsync(Context.ConnectionId, groupName);

            if (UserSubscriptions.TryGetValue(userId, out var subscriptions))
            {
                subscriptions.Remove(groupName);
            }

            Log.Information("📋 SignalR: User {UserId} unsubscribed from {GroupName}", userId, groupName);
        }

        #endregion

        #region Timing Operations (Real-time)

        [HubMethodName("StartTiming")]
        public async Task StartTiming(int jobId, string status, string notes = null, bool instructionsViewed = false, bool qualityChecksPassed = false)
        {
            try
            {
                var userId = int.Parse(GetUserId());
                var request = new TimingActionRequest
                {
                    JobId = jobId,
                    Status = status,
                    Notes = notes,
                     //WorkstationId = GetWorkstationId()
                };

                var result = await _timingApp.Start(request, userId);

                // Send response to caller with JobId included
                await Clients.Caller.SendAsync("TimingActionResult", new
                {
                    JobId = jobId,
                    Success = result.Success,
                    Message = result.Message,
                    SessionId = result.SessionId,
                    ButtonState = result.ButtonState,
                    ErrorCode = result.ErrorCode
                });

                // Broadcast timing state change to all job board subscribers
                if (result.Success)
                {
                    await BroadcastTimingStateChange(jobId, status, result.ButtonState);
                }

                Log.Information("🎬 SignalR: Timing started for Job {JobId}, Status {Status}, Success: {Success}",
                    jobId, status, result.Success);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "❌ SignalR: Error starting timing for Job {JobId}", jobId);
                await Clients.Caller.SendAsync("TimingActionError", new { JobId = jobId, error = ex.Message });
            }
        }

        [HubMethodName("PauseTiming")]
        public async Task PauseTiming(int jobId, string status, string notes = null)
        {
            try
            {
                var userId = int.Parse(GetUserId());
                var request = new TimingActionRequest
                {
                    JobId = jobId,
                    Status = status,
                    Notes = notes,
                    //WorkstationId = GetWorkstationId()
                };

                var result = await _timingApp.Pause(request, userId);

                await Clients.Caller.SendAsync("TimingActionResult", new
                {
                    JobId = jobId,
                    Success = result.Success,
                    Message = result.Message,
                    SessionId = result.SessionId,
                    ButtonState = result.ButtonState,
                    ErrorCode = result.ErrorCode
                });

                if (result.Success)
                {
                    await BroadcastTimingStateChange(jobId, status, result.ButtonState);
                }

                Log.Information("⏸️ SignalR: Timing paused for Job {JobId}, Status {Status}, Success: {Success}",
                    jobId, status, result.Success);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "❌ SignalR: Error pausing timing for Job {JobId}", jobId);
                await Clients.Caller.SendAsync("TimingActionError", new { JobId = jobId, error = ex.Message });
            }
        }

        [HubMethodName("ResumeTiming")]
        public async Task ResumeTiming(int jobId, string status, string notes = null)
        {
            try
            {
                var userId = int.Parse(GetUserId());
                var request = new TimingActionRequest
                {
                    JobId = jobId,
                    Status = status,
                    Notes = notes,
                    //WorkstationId = GetWorkstationId()
                };

                var result = await _timingApp.Resume(request, userId);

                await Clients.Caller.SendAsync("TimingActionResult", new
                {
                    JobId = jobId,
                    Success = result.Success,
                    Message = result.Message,
                    SessionId = result.SessionId,
                    ButtonState = result.ButtonState,
                    ErrorCode = result.ErrorCode
                });

                if (result.Success)
                {
                    await BroadcastTimingStateChange(jobId, status, result.ButtonState);
                }

                Log.Information("▶️ SignalR: Timing resumed for Job {JobId}, Status {Status}, Success: {Success}",
                    jobId, status, result.Success);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "❌ SignalR: Error resuming timing for Job {JobId}", jobId);
                await Clients.Caller.SendAsync("TimingActionError", new { JobId = jobId, error = ex.Message });
            }
        }

        [HubMethodName("FinishTiming")]
        public async Task FinishTiming(int jobId, string status, string notes = null)
        {
            try
            {
                var userId = int.Parse(GetUserId());
                var request = new TimingActionRequest
                {
                    JobId = jobId,
                    Status = status,
                    Notes = notes,
                    //WorkstationId = GetWorkstationId()
                };

                var result = await _timingApp.Finish(request, userId);

                await Clients.Caller.SendAsync("TimingActionResult", new
                {
                    JobId = jobId,
                    Success = result.Success,
                    Message = result.Message,
                    SessionId = result.SessionId,
                    ButtonState = result.ButtonState,
                    ErrorCode = result.ErrorCode
                });

                if (result.Success)
                {
                    await BroadcastTimingStateChange(jobId, status, result.ButtonState);

                    // Don't broadcast job status change for timing finish - it doesn't change the job status
                    // Only timing state changes, which is already broadcast above
                }

                Log.Information("✅ SignalR: Timing finished for Job {JobId}, Status {Status}, Success: {Success}",
                    jobId, status, result.Success);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "❌ SignalR: Error finishing timing for Job {JobId}", jobId);
                await Clients.Caller.SendAsync("TimingActionError", new { JobId = jobId, error = ex.Message });
            }
        }

        [HubMethodName("GetTimingState")]
        public async Task GetTimingState(int jobId, string status)
        {
            try
            {
                var userId = int.Parse(GetUserId());
                var result = await _timingApp.GetButtonState(jobId, userId, status);

                await Clients.Caller.SendAsync("TimingStateResult", result);

                Log.Information("🔍 SignalR: Retrieved timing state for Job {JobId}, Status {Status}", jobId, status);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "❌ SignalR: Error getting timing state for Job {JobId}", jobId);
                await Clients.Caller.SendAsync("TimingStateError", new { jobId, status, error = ex.Message });
            }
        }

        [HubMethodName("StopAllSessions")]
        public async Task StopAllSessions()
        {
            try
            {
                var userId = int.Parse(GetUserId());
                var result = await _timingApp.StopAllUserSessions(userId);

                await Clients.Caller.SendAsync("StopAllSessionsResult", result);

                Log.Information("🛑 SignalR: Stopped all sessions for User {UserId}", userId);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "❌ SignalR: Error stopping all sessions for User {UserId}", GetUserId());
                await Clients.Caller.SendAsync("StopAllSessionsError", new { error = ex.Message });
            }
        }

        #endregion

        #region Job Board Operations

        [HubMethodName("GetJobs")]
        public async Task GetJobs(string facilityStr = "FG", string board = "All")
        {
            try
            {
                if (_globalJobBoard.Entries.Any())
                {
                    Facility facility = Facility.PM;
                    try
                    {
                        facility = (Facility)Enum.Parse(typeof(Facility), facilityStr);
                    }
                    catch (Exception)
                    {
                        // Default to PM if parsing fails
                    }

                    JobBoardTypes jobboard = JobBoardTypes.All;
                    try
                    {
                        jobboard = (JobBoardTypes)Enum.Parse(typeof(JobBoardTypes), board);
                    }
                    catch (Exception)
                    {
                        // Default to All if parsing fails
                    }

                    // Filter entries by facility and board (same logic as JobBoardController)
                    var filteredEntries = _globalJobBoard.Entries
                        .Where(e => e.Facility == facility)
                        .Where(e => e.RowType == "1J")
                        .Where(e => board == "All" || e.BoardsToAppear.Contains(jobboard))
                        .ToList();

                    // Convert BoardsToAppear to string arrays
                    foreach (var entry in filteredEntries)
                    {
                        // Ensure BoardsToAppear is not null
                        if (entry.BoardsToAppear == null)
                        {
                            entry.BoardsToAppear = Array.Empty<JobBoardTypes>();
                        }
                    }

                    // Populate timing states
                    if (_jobBoardHelper != null)
                    {
                        await _jobBoardHelper.PopulateTimingStatesAsync(filteredEntries);
                    }

                    var result = new
                    {
                        Version = _globalJobBoard.Version,
                        Data = filteredEntries
                    };

                    await Clients.Caller.SendAsync("GetJobsResult", result);

                    Log.Information("📋 SignalR: Retrieved {Count} jobs for Facility {Facility}, Board {Board}",
                        filteredEntries.Count, facilityStr, board);
                }
                else
                {
                    // No entries available, return version 0
                    var result = new { version = 0 };
                    await Clients.Caller.SendAsync("GetJobsResult", result);

                    Log.Information("📋 SignalR: No job board entries available for Facility {Facility}, Board {Board}",
                        facilityStr, board);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "❌ SignalR: Error getting jobs for Facility {Facility}, Board {Board}", facilityStr, board);
                await Clients.Caller.SendAsync("GetJobsError", new { facilityStr, board, error = ex.Message });
            }
        }

        [HubMethodName("GetVersion")]
        public async Task GetVersion()
        {
            try
            {
                // Return current job board version from GlobalJobBoard
                var version = _globalJobBoard.Version;

                await Clients.Caller.SendAsync("GetVersionResult", version);

                Log.Information("📋 SignalR: Retrieved version {Version}", version);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "❌ SignalR: Error getting version");
                await Clients.Caller.SendAsync("GetVersionError", new { error = ex.Message });
            }
        }

        [HubMethodName("GetProductionInstructions")]
        public async Task GetProductionInstructions(string status)
        {
            try
            {
                // For now, return null - this could be delegated to a ProductionInstructionsController if it exists
                await Clients.Caller.SendAsync("GetProductionInstructionsResult", null);

                Log.Information("📋 SignalR: Production instructions not implemented for Status {Status}", status);
            }
            catch (Exception ex)
            {
                Log.Warning(ex, "⚠️ SignalR: Production instructions not available for Status {Status}", status);
                await Clients.Caller.SendAsync("GetProductionInstructionsResult", null);
            }
        }

        #endregion

        #region Broadcasting Methods

        private async Task BroadcastTimingStateChange(int jobId, string status, object buttonState)
        {
            await Clients.All.SendAsync("TimingStateChanged", new
            {
                jobId,
                status,
                buttonState,
                timestamp = DateTime.Now
            });
        }

        private async Task BroadcastJobStatusChange(int jobId)
        {
            await Clients.All.SendAsync("JobStatusChanged", new
            {
                jobId,
                timestamp = DateTime.Now
            });
        }

        public async Task BroadcastJobBoardUpdate(string boardType, string facility = null)
        {
            var groupName = $"JobBoard_{boardType}_{facility ?? "All"}";
            await Clients.Group(groupName).SendAsync("JobBoardRefreshRequired", new
            {
                boardType,
                facility,
                timestamp = DateTime.Now
            });
        }

        #endregion

        #region Helper Methods

        private string GetUserId()
        {
            return Context.User?.Identity?.Name ?? "1"; // Default to user 1 for testing
        }

        private string GetWorkstationId()
        {
            return Context.GetHttpContext()?.Request.Headers["X-Workstation-Id"].FirstOrDefault()
                ?? $"SignalR-{Environment.MachineName}-{DateTime.Now:yyyyMMdd}";
        }



        #endregion
    }
}
