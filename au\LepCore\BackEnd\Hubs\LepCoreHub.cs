using Microsoft.AspNetCore.SignalR;
using Microsoft.AspNetCore.Authorization;
using Serilog;
using System;
using System.Collections.Generic;
using System.Collections.Concurrent;
using System.Threading.Tasks;
using System.Linq;
using lep.timing;
using lep.timing.dto;
using lep.job;
using lep.order;

namespace LepCore.BackEnd.Hubs
{
	// //[Authorize]
	[AllowAnonymous]
    public class LepCoreHub : Hub
    {
        private readonly ITimingApplication _timingApp;
        private readonly IJobApplication _jobApp;
        private readonly IOrderApplication _orderApp;

        // Track user connections and their subscriptions
        private static readonly ConcurrentDictionary<string, string> UserConnections = new();
        private static readonly ConcurrentDictionary<string, HashSet<string>> UserSubscriptions = new();

        public LepCoreHub(ITimingApplication timingApp, IJobApplication jobApp, IOrderApplication orderApp)
        {
            _timingApp = timingApp;
            _jobApp = jobApp;
            _orderApp = orderApp;
        }

        #region Connection Management

        public override async Task OnConnectedAsync()
        {
            var userId = GetUserId();
            UserConnections[Context.ConnectionId] = userId;
            UserSubscriptions[userId] = new HashSet<string>();

            Log.Information("🔌 SignalR: User {UserId} connected with ConnectionId {ConnectionId}", userId, Context.ConnectionId);
            await base.OnConnectedAsync();
        }

        public override async Task OnDisconnectedAsync(Exception exception)
        {
            var userId = GetUserId();
            UserConnections.TryRemove(Context.ConnectionId, out _);
            UserSubscriptions.TryRemove(userId, out _);

            Log.Information("🔌 SignalR: User {UserId} disconnected", userId);
            await base.OnDisconnectedAsync(exception);
        }

        #endregion

        #region Job Board Subscriptions

        [HubMethodName("SubscribeToJobBoard")]
        public async Task SubscribeToJobBoard(string boardType, string facility = null)
        {
            var userId = GetUserId();
            var groupName = $"JobBoard_{boardType}_{facility ?? "All"}";

            await Groups.AddToGroupAsync(Context.ConnectionId, groupName);

            if (UserSubscriptions.TryGetValue(userId, out var subscriptions))
            {
                subscriptions.Add(groupName);
            }

            Log.Information("📋 SignalR: User {UserId} subscribed to {GroupName}", userId, groupName);
            await Clients.Caller.SendAsync("SubscriptionConfirmed", groupName);
        }

        [HubMethodName("UnsubscribeFromJobBoard")]
        public async Task UnsubscribeFromJobBoard(string boardType, string facility = null)
        {
            var userId = GetUserId();
            var groupName = $"JobBoard_{boardType}_{facility ?? "All"}";

            await Groups.RemoveFromGroupAsync(Context.ConnectionId, groupName);

            if (UserSubscriptions.TryGetValue(userId, out var subscriptions))
            {
                subscriptions.Remove(groupName);
            }

            Log.Information("📋 SignalR: User {UserId} unsubscribed from {GroupName}", userId, groupName);
        }

        #endregion

        #region Timing Operations (Real-time)

        [HubMethodName("StartTiming")]
        public async Task StartTiming(int jobId, string status, string notes = null, bool instructionsViewed = false, bool qualityChecksPassed = false)
        {
            try
            {
                var userId = int.Parse(GetUserId());
                var request = new TimingActionRequest
                {
                    JobId = jobId,
                    Status = status,
                    Notes = notes,
                    InstructionsViewed = instructionsViewed,
                    QualityChecksPassed = qualityChecksPassed,
                    WorkstationId = GetWorkstationId()
                };

                var result = await _timingApp.Play(request, userId);

                // Send response to caller
                await Clients.Caller.SendAsync("TimingActionResult", result);

                // Broadcast timing state change to all job board subscribers
                if (result.Success)
                {
                    await BroadcastTimingStateChange(jobId, status, result.ButtonState);
                }

                Log.Information("🎬 SignalR: Timing started for Job {JobId}, Status {Status}, Success: {Success}",
                    jobId, status, result.Success);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "❌ SignalR: Error starting timing for Job {JobId}", jobId);
                await Clients.Caller.SendAsync("TimingActionError", new { jobId, error = ex.Message });
            }
        }

        [HubMethodName("PauseTiming")]
        public async Task PauseTiming(int jobId, string status, string notes = null)
        {
            try
            {
                var userId = int.Parse(GetUserId());
                var request = new TimingActionRequest
                {
                    JobId = jobId,
                    Status = status,
                    Notes = notes,
                    WorkstationId = GetWorkstationId()
                };

                var result = await _timingApp.Pause(request, userId);

                await Clients.Caller.SendAsync("TimingActionResult", result);

                if (result.Success)
                {
                    await BroadcastTimingStateChange(jobId, status, result.ButtonState);
                }

                Log.Information("⏸️ SignalR: Timing paused for Job {JobId}, Status {Status}, Success: {Success}",
                    jobId, status, result.Success);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "❌ SignalR: Error pausing timing for Job {JobId}", jobId);
                await Clients.Caller.SendAsync("TimingActionError", new { jobId, error = ex.Message });
            }
        }

        [HubMethodName("ResumeTiming")]
        public async Task ResumeTiming(int jobId, string status, string notes = null)
        {
            try
            {
                var userId = int.Parse(GetUserId());
                var request = new TimingActionRequest
                {
                    JobId = jobId,
                    Status = status,
                    Notes = notes,
                    WorkstationId = GetWorkstationId()
                };

                var result = await _timingApp.Resume(request, userId);

                await Clients.Caller.SendAsync("TimingActionResult", result);

                if (result.Success)
                {
                    await BroadcastTimingStateChange(jobId, status, result.ButtonState);
                }

                Log.Information("▶️ SignalR: Timing resumed for Job {JobId}, Status {Status}, Success: {Success}",
                    jobId, status, result.Success);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "❌ SignalR: Error resuming timing for Job {JobId}", jobId);
                await Clients.Caller.SendAsync("TimingActionError", new { jobId, error = ex.Message });
            }
        }

        [HubMethodName("FinishTiming")]
        public async Task FinishTiming(int jobId, string status, string notes = null)
        {
            try
            {
                var userId = int.Parse(GetUserId());
                var request = new TimingActionRequest
                {
                    JobId = jobId,
                    Status = status,
                    Notes = notes,
                    WorkstationId = GetWorkstationId()
                };

                var result = await _timingApp.Finish(request, userId);

                await Clients.Caller.SendAsync("TimingActionResult", result);

                if (result.Success)
                {
                    await BroadcastTimingStateChange(jobId, status, result.ButtonState);
                    await BroadcastJobStatusChange(jobId); // Job might move to next status
                }

                Log.Information("✅ SignalR: Timing finished for Job {JobId}, Status {Status}, Success: {Success}",
                    jobId, status, result.Success);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "❌ SignalR: Error finishing timing for Job {JobId}", jobId);
                await Clients.Caller.SendAsync("TimingActionError", new { jobId, error = ex.Message });
            }
        }

        [HubMethodName("GetTimingState")]
        public async Task GetTimingState(int jobId, string status)
        {
            try
            {
                var userId = int.Parse(GetUserId());
                var result = await _timingApp.GetTimingState(jobId, status, userId);

                await Clients.Caller.SendAsync("TimingStateResult", result);

                Log.Information("🔍 SignalR: Retrieved timing state for Job {JobId}, Status {Status}", jobId, status);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "❌ SignalR: Error getting timing state for Job {JobId}", jobId);
                await Clients.Caller.SendAsync("TimingStateError", new { jobId, status, error = ex.Message });
            }
        }

        [HubMethodName("StopAllSessions")]
        public async Task StopAllSessions()
        {
            try
            {
                var userId = int.Parse(GetUserId());
                var result = await _timingApp.StopAllSessions(userId);

                await Clients.Caller.SendAsync("StopAllSessionsResult", result);

                Log.Information("🛑 SignalR: Stopped all sessions for User {UserId}", userId);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "❌ SignalR: Error stopping all sessions for User {UserId}", GetUserId());
                await Clients.Caller.SendAsync("StopAllSessionsError", new { error = ex.Message });
            }
        }

        #endregion

        #region Job Board Operations

        [HubMethodName("GetJobs")]
        public async Task GetJobs(string facilityStr = "FG", string board = "All")
        {
            try
            {
                // This would need to be implemented in your job application layer
                // For now, return a placeholder response
                var result = new
                {
                    version = 1,
                    data = new object[] { }, // Empty for now - implement job loading logic
                    timestamp = DateTime.Now
                };

                await Clients.Caller.SendAsync("GetJobsResult", result);

                Log.Information("📋 SignalR: Retrieved jobs for Facility {Facility}, Board {Board}", facilityStr, board);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "❌ SignalR: Error getting jobs for Facility {Facility}, Board {Board}", facilityStr, board);
                await Clients.Caller.SendAsync("GetJobsError", new { facilityStr, board, error = ex.Message });
            }
        }

        [HubMethodName("GetVersion")]
        public async Task GetVersion()
        {
            try
            {
                // Return current job board version - implement your versioning logic
                var version = DateTime.Now.Ticks; // Simple timestamp-based version

                await Clients.Caller.SendAsync("GetVersionResult", version);

                Log.Information("📋 SignalR: Retrieved version {Version}", version);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "❌ SignalR: Error getting version");
                await Clients.Caller.SendAsync("GetVersionError", new { error = ex.Message });
            }
        }

        [HubMethodName("GetProductionInstructions")]
        public async Task GetProductionInstructions(string status)
        {
            try
            {
                // This would need to be implemented to load production instructions
                // For now, return null to match API service behavior
                object instructions = null;

                await Clients.Caller.SendAsync("GetProductionInstructionsResult", instructions);

                Log.Information("📋 SignalR: Retrieved production instructions for Status {Status}", status);
            }
            catch (Exception ex)
            {
                Log.Warning(ex, "⚠️ SignalR: Production instructions not available for Status {Status}", status);
                await Clients.Caller.SendAsync("GetProductionInstructionsResult", null);
            }
        }

        #endregion

        #region Job Operations

        [HubMethodName("GetJobDetails")]
        public async Task GetJobDetails(int jobId)
        {
            try
            {
                var job = _jobApp.GetJob(jobId);
                await Clients.Caller.SendAsync("JobDetailsResult", job);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "❌ SignalR: Error getting job details for Job {JobId}", jobId);
                await Clients.Caller.SendAsync("JobDetailsError", new { jobId, error = ex.Message });
            }
        }

        [HubMethodName("UpdateJobStatus")]
        public async Task UpdateJobStatus(int jobId, string newStatus)
        {
            try
            {
                // Implement job status update logic
                // var result = await _jobApp.UpdateStatus(jobId, newStatus);

                // Broadcast to all subscribers
                await BroadcastJobStatusChange(jobId);

                Log.Information("🔄 SignalR: Job {JobId} status updated to {Status}", jobId, newStatus);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "❌ SignalR: Error updating job status for Job {JobId}", jobId);
                await Clients.Caller.SendAsync("JobUpdateError", new { jobId, error = ex.Message });
            }
        }

        #endregion

        #region Broadcasting Methods

        private async Task BroadcastTimingStateChange(int jobId, string status, object buttonState)
        {
            await Clients.All.SendAsync("TimingStateChanged", new
            {
                jobId,
                status,
                buttonState,
                timestamp = DateTime.Now
            });
        }

        private async Task BroadcastJobStatusChange(int jobId)
        {
            await Clients.All.SendAsync("JobStatusChanged", new
            {
                jobId,
                timestamp = DateTime.Now
            });
        }

        public async Task BroadcastJobBoardUpdate(string boardType, string facility = null)
        {
            var groupName = $"JobBoard_{boardType}_{facility ?? "All"}";
            await Clients.Group(groupName).SendAsync("JobBoardRefreshRequired", new
            {
                boardType,
                facility,
                timestamp = DateTime.Now
            });
        }

        #endregion

        #region Helper Methods

        private string GetUserId()
        {
            return Context.User?.Identity?.Name ?? "1"; // Default to user 1 for testing
        }

        private string GetWorkstationId()
        {
            return Context.GetHttpContext()?.Request.Headers["X-Workstation-Id"].FirstOrDefault()
                ?? $"SignalR-{Environment.MachineName}-{DateTime.Now:yyyyMMdd}";
        }

        #endregion
    }
}
