<!DOCTYPE html>
<html ng-app="timingApp">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Job Timing - LEPCore</title>
    
    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css">
    
    <!-- AngularJS -->
    <script src="https://ajax.googleapis.com/ajax/libs/angularjs/1.6.9/angular.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/angular-ui-bootstrap/2.5.0/ui-bootstrap-tpls.min.js"></script>
    
    <style>
        body {
            padding: 20px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .timing-container {
            max-width: 1000px;
            margin: 0 auto;
        }
        
        .job-header {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-left: 4px solid #007bff;
        }
        
        .timing-controls {
            background: #fff;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .timer-display {
            text-align: center;
            margin: 20px 0;
        }
        
        .timer-display .time {
            font-size: 2.5em;
            font-weight: bold;
            color: #007bff;
            font-family: 'Courier New', monospace;
        }
        
        .timer-display .status {
            font-size: 1.2em;
            margin-top: 10px;
        }
        
        .btn-group .btn {
            margin-right: 10px;
        }
        
        .instructions-panel {
            background: #fff;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 20px;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
        }
        
        .error {
            color: #dc3545;
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
    </style>
</head>
<body ng-controller="TimingController">
    <div class="timing-container">
        <!-- Loading State -->
        <div ng-if="loading" class="loading">
            <i class="fa fa-spinner fa-spin fa-3x"></i>
            <p>Loading job details...</p>
        </div>
        
        <!-- Error State -->
        <div ng-if="error" class="error">
            <i class="fa fa-exclamation-triangle"></i>
            {{error}}
        </div>
        
        <!-- Main Content -->
        <div ng-if="!loading && !error">
            <!-- Job Header -->
            <div class="job-header">
                <h2>
                    <i class="fa fa-clock-o"></i>
                    Production Timing
                </h2>
                <div ng-if="jobData">
                    <h4>{{jobData.name}} <small>#{{jobData.id}}</small></h4>
                    <p class="text-muted">
                        Status: <strong>{{status}}</strong> | 
                        Order: <strong>{{jobData.orderId}}</strong> |
                        Quantity: <strong>{{jobData.quantity | number}}</strong>
                    </p>
                </div>
            </div>
            
            <!-- Timing Controls -->
            <div class="timing-controls">
                <h4>Timing Controls</h4>
                
                <!-- Timer Display -->
                <div class="timer-display">
                    <div class="time">{{formatElapsedTime(elapsedTime)}}</div>
                    <div class="status">
                        <span ng-if="!isActive" class="label label-default">Ready to Start</span>
                        <span ng-if="isActive && !isPaused" class="label label-success">
                            <i class="fa fa-play"></i> Running
                        </span>
                        <span ng-if="isActive && isPaused" class="label label-warning">
                            <i class="fa fa-pause"></i> Paused
                        </span>
                    </div>
                </div>
                
                <!-- Control Buttons -->
                <div class="text-center">
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-success btn-lg" ng-click="startTiming()" ng-if="!isActive">
                            <i class="fa fa-play"></i> Start
                        </button>
                        <button type="button" class="btn btn-warning btn-lg" ng-click="pauseTiming()" ng-if="isActive && !isPaused">
                            <i class="fa fa-pause"></i> Pause
                        </button>
                        <button type="button" class="btn btn-info btn-lg" ng-click="resumeTiming()" ng-if="isActive && isPaused">
                            <i class="fa fa-play"></i> Resume
                        </button>
                        <button type="button" class="btn btn-primary btn-lg" ng-click="finishTiming()" ng-if="isActive">
                            <i class="fa fa-stop"></i> Finish
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Instructions Panel -->
            <div class="instructions-panel" ng-if="instructions">
                <h4>{{instructions.title}}</h4>
                <div ng-bind-html="instructions.content"></div>
            </div>
            
            <!-- Job Details Panel -->
            <div class="instructions-panel" ng-if="jobData">
                <h4>Job Details</h4>
                <div class="row">
                    <div class="col-md-6">
                        <dl class="dl-horizontal">
                            <dt>Job ID:</dt>
                            <dd>{{jobData.id}}</dd>
                            <dt>Name:</dt>
                            <dd>{{jobData.name}}</dd>
                            <dt>Order:</dt>
                            <dd>{{jobData.orderId}}</dd>
                            <dt>Status:</dt>
                            <dd>{{status}}</dd>
                        </dl>
                    </div>
                    <div class="col-md-6">
                        <dl class="dl-horizontal">
                            <dt>Quantity:</dt>
                            <dd>{{jobData.quantity | number}}</dd>
                            <dt>Template:</dt>
                            <dd>{{jobData.template}}</dd>
                            <dt>Customer:</dt>
                            <dd>{{jobData.customerName}}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Footer -->
        <div class="text-center" style="margin-top: 30px;">
            <button type="button" class="btn btn-default" onclick="window.close()">
                <i class="fa fa-times"></i> Close Window
            </button>
            <button type="button" class="btn btn-info" ng-click="openFullJob()" ng-if="jobData">
                <i class="fa fa-external-link"></i> View Full Job
            </button>
        </div>
    </div>

    <script>
        // Create standalone timing app
        angular.module('timingApp', ['ui.bootstrap'])
        .controller('TimingController', ['$scope', '$http', '$location', '$sce', function($scope, $http, $location, $sce) {
            // Get URL parameters
            const urlParams = new URLSearchParams(window.location.search);
            $scope.jobId = urlParams.get('jobId');
            $scope.status = urlParams.get('status');
            
            // Initialize state
            $scope.loading = true;
            $scope.error = null;
            $scope.jobData = null;
            $scope.instructions = null;
            
            // Timing state
            $scope.isActive = false;
            $scope.isPaused = false;
            $scope.elapsedTime = 0;
            $scope.startTime = null;
            $scope.pausedTime = 0;
            
            let timerInterval = null;
            
            // Load data
            $scope.loadData = function() {
                if (!$scope.jobId || !$scope.status) {
                    $scope.error = 'Missing job ID or status parameters';
                    $scope.loading = false;
                    return;
                }
                
                // Load job data
                $http.get('/api/orders/job/' + $scope.jobId)
                    .then(function(response) {
                        $scope.jobData = response.data;
                    })
                    .catch(function(error) {
                        console.error('Error loading job:', error);
                        $scope.error = 'Failed to load job details';
                    });
                
                // Load instructions
                $http.get('/api/production-instructions/' + $scope.status)
                    .then(function(response) {
                        if (response.data && response.data.instructions) {
                            $scope.instructions = {
                                title: response.data.title,
                                content: $sce.trustAsHtml(response.data.instructions)
                            };
                        }
                    })
                    .catch(function(error) {
                        console.warn('Instructions not available:', error);
                    })
                    .finally(function() {
                        $scope.loading = false;
                    });
            };
            
            // Timing functions
            $scope.startTiming = function() {
                $scope.isActive = true;
                $scope.isPaused = false;
                $scope.startTime = new Date();
                $scope.pausedTime = 0;
                $scope.elapsedTime = 0;
                $scope.startTimer();
                $scope.sendTimingEvent('Start');
            };
            
            $scope.pauseTiming = function() {
                $scope.isPaused = true;
                $scope.stopTimer();
                $scope.sendTimingEvent('Pause');
            };
            
            $scope.resumeTiming = function() {
                $scope.isPaused = false;
                $scope.pausedTime += $scope.elapsedTime;
                $scope.startTime = new Date();
                $scope.startTimer();
                $scope.sendTimingEvent('Resume');
            };
            
            $scope.finishTiming = function() {
                $scope.isActive = false;
                $scope.isPaused = false;
                $scope.stopTimer();
                $scope.sendTimingEvent('Finish');
            };
            
            $scope.startTimer = function() {
                $scope.stopTimer();
                timerInterval = setInterval(function() {
                    if (!$scope.isPaused && $scope.isActive) {
                        const now = new Date();
                        $scope.elapsedTime = Math.floor((now - $scope.startTime) / 1000);
                        $scope.$apply();
                    }
                }, 1000);
            };
            
            $scope.stopTimer = function() {
                if (timerInterval) {
                    clearInterval(timerInterval);
                    timerInterval = null;
                }
            };
            
            $scope.sendTimingEvent = function(eventType) {
                const eventData = {
                    jobId: $scope.jobId,
                    status: $scope.status,
                    eventType: eventType,
                    timestamp: new Date().toISOString(),
                    elapsedSeconds: $scope.elapsedTime + $scope.pausedTime
                };
                
                $http.post('/api/job-timing/event', eventData)
                    .then(function(response) {
                        console.log('Timing event sent:', eventType);
                    })
                    .catch(function(error) {
                        console.error('Error sending timing event:', error);
                    });
            };
            
            $scope.formatElapsedTime = function(seconds) {
                if (!seconds) return '00:00';
                
                const hours = Math.floor(seconds / 3600);
                const minutes = Math.floor((seconds % 3600) / 60);
                const secs = seconds % 60;
                
                if (hours > 0) {
                    return hours.toString().padStart(2, '0') + ':' + 
                           minutes.toString().padStart(2, '0') + ':' + 
                           secs.toString().padStart(2, '0');
                } else {
                    return minutes.toString().padStart(2, '0') + ':' + 
                           secs.toString().padStart(2, '0');
                }
            };
            
            $scope.openFullJob = function() {
                if ($scope.jobData) {
                    window.open('/#!/staff/order/' + $scope.jobData.orderId + '/job/' + $scope.jobData.id, '_blank');
                }
            };
            
            // Cleanup on destroy
            $scope.$on('$destroy', function() {
                $scope.stopTimer();
            });
            
            // Initialize
            $scope.loadData();
        }]);
    </script>
</body>
</html>
