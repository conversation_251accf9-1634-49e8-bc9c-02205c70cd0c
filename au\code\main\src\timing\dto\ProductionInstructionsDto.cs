using System;
using System.Collections.Generic;
using System.Linq;

namespace lep.timing.dto
{
    public class ProductionInstructionsDto
    {
        public int Id { get; set; }
        public string Status { get; set; }
        public int? JobTypeId { get; set; }
        public string JobTypeName { get; set; }
        public string Title { get; set; }
        public string Instructions { get; set; }
        public string SafetyNotes { get; set; }
        public int? EstimatedDuration { get; set; }
        public string RequiredTools { get; set; }
        public string QualityChecks { get; set; }
        public bool IsActive { get; set; }
        public int DisplayOrder { get; set; }
        public DateTime DateCreated { get; set; }
        public DateTime DateModified { get; set; }
        public string CreatedByName { get; set; }
        public string ModifiedByName { get; set; }
        
        // Calculated properties
        public List<string> RequiredToolsList 
        { 
            get 
            { 
                if (string.IsNullOrWhiteSpace(RequiredTools))
                    return new List<string>();
                    
                return RequiredTools.Split(',')
                    .Select(t => t.Trim())
                    .Where(t => !string.IsNullOrWhiteSpace(t))
                    .ToList();
            }
        }
        
        public string EstimatedDurationFormatted
        {
            get
            {
                if (!EstimatedDuration.HasValue)
                    return null;
                    
                var duration = TimeSpan.FromMinutes(EstimatedDuration.Value);
                if (duration.TotalHours >= 1)
                    return $"{(int)duration.TotalHours}h {duration.Minutes}m";
                return $"{duration.Minutes}m";
            }
        }
        
        public bool HasSafetyNotes => !string.IsNullOrWhiteSpace(SafetyNotes);
        public bool HasQualityChecks => !string.IsNullOrWhiteSpace(QualityChecks);
        public bool HasRequiredTools => RequiredToolsList.Any();
        public bool HasEstimatedDuration => EstimatedDuration.HasValue;
    }
}
