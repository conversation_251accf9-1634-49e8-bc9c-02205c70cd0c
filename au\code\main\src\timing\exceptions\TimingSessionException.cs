using System;

namespace lep.timing.exceptions
{
    public class TimingSessionException : Exception
    {
        public Guid? SessionId { get; }
        public string ErrorCode { get; }
        
        public TimingSessionException(string message, string errorCode = null) 
            : base(message)
        {
            ErrorCode = errorCode;
        }
        
        public TimingSessionException(string message, Guid sessionId, string errorCode = null) 
            : base(message)
        {
            SessionId = sessionId;
            ErrorCode = errorCode;
        }
        
        public TimingSessionException(string message, Exception innerException, string errorCode = null) 
            : base(message, innerException)
        {
            ErrorCode = errorCode;
        }
        
        // Factory methods for common session errors
        public static TimingSessionException SessionNotActive(Guid sessionId)
        {
            return new TimingSessionException(
                $"Timing session {sessionId} is not active",
                sessionId,
                "SESSION_NOT_ACTIVE");
        }
        
        public static TimingSessionException SessionAlreadyFinished(Guid sessionId)
        {
            return new TimingSessionException(
                $"Timing session {sessionId} has already been finished",
                sessionId,
                "SESSION_ALREADY_FINISHED");
        }
        
        public static TimingSessionException SessionCorrupted(Guid sessionId)
        {
            return new TimingSessionException(
                $"Timing session {sessionId} data is corrupted",
                sessionId,
                "SESSION_CORRUPTED");
        }
        
        public static TimingSessionException ConcurrentModification(Guid sessionId)
        {
            return new TimingSessionException(
                $"Timing session {sessionId} was modified by another user",
                sessionId,
                "CONCURRENT_MODIFICATION");
        }
        
        public static TimingSessionException DatabaseError(string operation, Exception innerException)
        {
            return new TimingSessionException(
                $"Database error during timing operation: {operation}",
                innerException,
                "DATABASE_ERROR");
        }
    }
}
