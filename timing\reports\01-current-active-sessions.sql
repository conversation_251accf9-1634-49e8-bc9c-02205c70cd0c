-- Current Active Sessions Report
-- Who is currently working on what and for how long

SELECT
    s.SessionId,
    s.JobId,
    j.Name AS JobName,
    o.Id AS OrderId,
    c.UserName AS CustomerName,
    s.Status,
    u.UserName,
    s.StartTime,
    s.CurrentState,
    CASE s.CurrentState
        WHEN 0 THEN 'Ready'
        WHEN 1 THEN 'Playing'
        WHEN 2 THEN 'Paused'
        WHEN 3 THEN 'Finished'
    END AS StateDescription,
    -- Calculate current working duration
    CASE
        WHEN s.CurrentState = 1 THEN -- Playing
            DATEDIFF(SECOND, s.StartTime, GETDATE()) - s.TotalPausedDuration
        ELSE
            DATEDIFF(SECOND, s.StartTime, s.LastEventTime) - s.TotalPausedDuration
    END AS CurrentWorkingDurationSeconds,
    FORMAT(DATEADD(SECOND,
        CASE
            WHEN s.CurrentState = 1 THEN
                DATEDIFF(SECOND, s.StartTime, GETDATE()) - s.TotalPausedDuration
            ELSE
                DATEDIFF(SECOND, s.StartTime, s.LastEventTime) - s.TotalPausedDuration
        END, 0), 'HH:mm:ss') AS FormattedDuration,
    DATEDIFF(MINUTE, s.StartTime, GETDATE()) AS MinutesSinceStart,
    s.LastEventTime
FROM JobStepActiveSession s
    INNER JOIN Job j ON s.JobId = j.Id
    INNER JOIN [Order] o ON j.OrderId = o.Id
    INNER JOIN LepUser c ON o.UserId = c.Id
    INNER JOIN LepUser u ON s.UserId = u.Id
WHERE s.IsActive = 1
ORDER BY s.StartTime DESC;
