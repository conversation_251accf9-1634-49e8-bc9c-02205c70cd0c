-- =====================================================
-- INVESTIGATION: Invoice Total Mismatches
-- =====================================================
-- This script helps investigate orders where MYOB invoice totals 
-- don't match the expected order totals (like order 0416689)

-- =====================================================
-- QUERY 1: Find Recent Multi-Job Orders with Totals
-- =====================================================
-- Find orders invoiced after June 6th, 2025 with multiple jobs and their totals
SELECT 
    o.Id as OrderId,
    o.OrderNr,
    o.Invoiced2,
    o.Invoiced2Details,
    o.Finish<PERSON>ate,
    o.PriceOfJobs as OrderTotal,
    COUNT(j.Id) as JobCount,
    SUM(ISNULL(j.Price, 0)) as JobsTotal,
    o.PriceOfJobs - SUM(ISNULL(j.Price, 0)) as Difference,
    c.Name as CustomerName,
    -- Additional freight/discount info
    ISNULL(pd.Price, 0) as FreightPrice,
    ISNULL(o.PromotionBenefit, 0) as DiscountAmount
FROM [Order] o
    INNER JOIN Job j ON j.OrderId = o.Id
    INNER JOIN Customer c ON c.Id = o.CustomerId
    LEFT JOIN PackDetail pd ON pd.OrderId = o.Id
WHERE o.Invoiced2 = 'Y'
    AND o.FinishDate >= '2025-06-06'
    AND o.PriceOfJobs > 0
GROUP BY o.Id, o.OrderNr, o.Invoiced2, o.Invoiced2Details, o.FinishDate, o.PriceOfJobs, c.Name, pd.Price, o.PromotionBenefit
HAVING COUNT(j.Id) > 1
ORDER BY o.FinishDate DESC, o.Id DESC;

-- =====================================================
-- QUERY 2: Detailed Job Analysis for Specific Order
-- =====================================================
-- Analyze specific order (replace 1416689 with actual order ID)
SELECT 
    'ORDER SUMMARY' as Section,
    CAST(o.Id as VARCHAR(50)) as OrderId,
    o.OrderNr as OrderNumber,
    CAST(o.PriceOfJobs as VARCHAR(50)) as OrderTotal,
    c.Name as CustomerName,
    CAST(o.FinishDate as VARCHAR(50)) as FinishDate
FROM [Order] o
    INNER JOIN Customer c ON c.Id = o.CustomerId
WHERE o.OrderNr = '0416689'

UNION ALL

SELECT 
    'JOB DETAILS' as Section,
    CAST(j.Id as VARCHAR(50)) as JobId,
    j.Description as JobDescription,
    CAST(j.Price as VARCHAR(50)) as JobPrice,
    '' as Extra1,
    '' as Extra2
FROM [Order] o
    INNER JOIN Job j ON j.OrderId = o.Id
WHERE o.OrderNr = '0416689'

UNION ALL

SELECT 
    'FREIGHT/PACK' as Section,
    CAST(pd.Id as VARCHAR(50)) as PackDetailId,
    ISNULL(o.Courier, 'No Courier') as CourierInfo,
    CAST(ISNULL(pd.Price, 0) as VARCHAR(50)) as FreightPrice,
    '' as Extra1,
    '' as Extra2
FROM [Order] o
    LEFT JOIN PackDetail pd ON pd.OrderId = o.Id
WHERE o.OrderNr = '0416689'

UNION ALL

SELECT 
    'PROMOTION/DISCOUNT' as Section,
    ISNULL(p.PromotionCode, 'No Promotion') as PromotionCode,
    ISNULL(p.ShortDescription, '') as PromotionDesc,
    CAST(ISNULL(o.PromotionBenefit, 0) as VARCHAR(50)) as DiscountAmount,
    '' as Extra1,
    '' as Extra2
FROM [Order] o
    LEFT JOIN Promotion p ON p.Id = o.PromotionId
WHERE o.OrderNr = '0416689';

-- =====================================================
-- QUERY 3: Check Invoicer2Log for Processing Details
-- =====================================================
-- Check what was logged during invoice processing
SELECT 
    il.OrderId,
    il.JobCount,
    il.Total as LoggedTotal,
    il.FinishDate,
    il.Success,
    il.Details,
    il.DateCreated as ProcessedDate
FROM Invoicer2Log il
    INNER JOIN [Order] o ON o.Id = il.OrderId
WHERE o.OrderNr = '0416689'
ORDER BY il.DateCreated DESC;

-- =====================================================
-- QUERY 4: Find All Orders with Potential Issues
-- =====================================================
-- Find orders where job totals don't match order totals
SELECT 
    o.Id as OrderId,
    o.OrderNr,
    o.PriceOfJobs as OrderTotal,
    SUM(ISNULL(j.Price, 0)) as JobsTotal,
    COUNT(j.Id) as JobCount,
    ABS(o.PriceOfJobs - SUM(ISNULL(j.Price, 0))) as Difference,
    CASE 
        WHEN ABS(o.PriceOfJobs - SUM(ISNULL(j.Price, 0))) > 0.01 THEN 'MISMATCH'
        ELSE 'OK'
    END as Status
FROM [Order] o
    INNER JOIN Job j ON j.OrderId = o.Id
WHERE o.Invoiced2 = 'Y'
    AND o.FinishDate >= '2025-06-06'
    AND o.PriceOfJobs > 0
GROUP BY o.Id, o.OrderNr, o.PriceOfJobs
HAVING ABS(o.PriceOfJobs - SUM(ISNULL(j.Price, 0))) > 0.01
ORDER BY ABS(o.PriceOfJobs - SUM(ISNULL(j.Price, 0))) DESC;
