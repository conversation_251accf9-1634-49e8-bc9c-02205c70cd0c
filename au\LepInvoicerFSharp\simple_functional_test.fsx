// Simple functional test without external dependencies
open System

printfn "=== Simple Functional LEP Invoicer Test ==="

// ============================================================================
// CORE DOMAIN TYPES - Pure functional types
// ============================================================================

type CreditType = 
    | Credit
    | Refund
    | Adjustment
    | Other

type Customer = {
    Id: int
    Name: string
    Username: string
}

type Job = {
    Id: int
    OrderId: int
    Description: string
    Price: decimal option
    IsEnabled: bool
}

type Order = {
    Id: int
    OrderNumber: string
    Customer: Customer
    Jobs: Job list
    FinishDate: DateTime option
    SubmissionDate: DateTime
    PurchaseOrder: string option
    PromotionBenefit: decimal
    PickUpCharge: decimal
    GST: decimal
    IsInvoiced: bool
}

type OrderCredit = {
    Id: int
    OrderId: int
    CustomerId: int
    CreditType: CreditType
    Amount: decimal
    Description: string
    IsInvoiced: bool
    DateCreated: DateTime
}

type ProcessingStats = {
    OrdersProcessed: int
    OrdersSuccessful: int
    OrdersFailed: int
    CreditsProcessed: int
    RefundsProcessed: int
    ElapsedTime: TimeSpan
    Errors: string list
}

type InvoicerConfig = {
    ConnectionString: string
    InvoiceBatchSize: int
    RefundBatchSize: int
    MinimumFinishDate: DateTime
    IgnoreCustomers: string list
    TestMode: bool
}

// ============================================================================
// FUNCTIONAL ERROR HANDLING
// ============================================================================

type AsyncResult<'T, 'Error> = Async<Result<'T, 'Error>>

module AsyncResult =
    let retn (value: 'T) : AsyncResult<'T, 'Error> =
        async { return Ok value }
    
    let bind (f: 'T -> AsyncResult<'U, 'Error>) (asyncResult: AsyncResult<'T, 'Error>) : AsyncResult<'U, 'Error> =
        async {
            let! result = asyncResult
            match result with
            | Ok value -> return! f value
            | Error error -> return Error error
        }

// ============================================================================
// PURE BUSINESS LOGIC
// ============================================================================

module OrderLogic =
    let calculateTotalPrice (order: Order) : decimal option =
        let jobTotal = 
            order.Jobs
            |> List.choose (fun job -> if job.IsEnabled then job.Price else None)
            |> List.sum
        
        if jobTotal > 0m then
            Some (jobTotal + order.PromotionBenefit + order.PickUpCharge + order.GST)
        else
            None
    
    let isReadyForInvoicing (config: InvoicerConfig) (order: Order) : bool =
        let hasValidPrice = calculateTotalPrice order |> Option.isSome
        let isNotIgnored = not (List.contains order.Customer.Username config.IgnoreCustomers)
        let isFinished = 
            match order.FinishDate with
            | Some finishDate -> finishDate >= config.MinimumFinishDate
            | None -> false
        let isNotAlreadyInvoiced = not order.IsInvoiced
        
        hasValidPrice && isNotIgnored && isFinished && isNotAlreadyInvoiced

module CreditLogic =
    let isReadyForInvoicing (config: InvoicerConfig) (credit: OrderCredit) : bool =
        let hasValidAmount = credit.Amount > 0m
        let isNotAlreadyInvoiced = not credit.IsInvoiced
        
        hasValidAmount && isNotAlreadyInvoiced

// ============================================================================
// MOCK DATABASE OPERATIONS
// ============================================================================

module Database =
    let getOrdersToInvoice (config: InvoicerConfig) (batchSize: int) : AsyncResult<(int * string) list, string> =
        async {
            let mockOrders = [(1, "customer1"); (2, "customer2"); (3, "customer3")]
            let validOrders = 
                mockOrders
                |> List.filter (fun (_, username) -> not (List.contains username config.IgnoreCustomers))
                |> List.take (min batchSize (List.length mockOrders))
            return Ok validOrders
        }
    
    let getCreditsToInvoice (config: InvoicerConfig) (batchSize: int) : AsyncResult<OrderCredit list, string> =
        async {
            let mockCredits = [
                { Id = 1; OrderId = 1; CustomerId = 1; CreditType = Credit; Amount = 50.0m; Description = "Test credit"; IsInvoiced = false; DateCreated = DateTime.Now }
            ]
            return Ok mockCredits
        }
    
    let getRefundsToInvoice (config: InvoicerConfig) (batchSize: int) : AsyncResult<OrderCredit list, string> =
        async {
            let mockRefunds = [
                { Id = 2; OrderId = 2; CustomerId = 2; CreditType = Refund; Amount = 25.0m; Description = "Test refund"; IsInvoiced = false; DateCreated = DateTime.Now }
            ]
            return Ok mockRefunds
        }
    
    let markOrderInvoiced (orderId: int) : AsyncResult<unit, string> =
        async {
            printfn $"Marking order {orderId} as invoiced"
            return Ok ()
        }
    
    let markCreditInvoiced (creditId: int) : AsyncResult<unit, string> =
        async {
            printfn $"Marking credit {creditId} as invoiced"
            return Ok ()
        }

// ============================================================================
// MOCK MYOB OPERATIONS
// ============================================================================

module MYOB =
    type MYOBState = { IsInitialized: bool; CompanyFile: string option }
    
    let initialize (config: InvoicerConfig) : AsyncResult<MYOBState, string> =
        async {
            printfn "Initializing MYOB..."
            return Ok { IsInitialized = true; CompanyFile = Some "test.myob" }
        }
    
    let createCreditInvoice (state: MYOBState) (credit: OrderCredit) : AsyncResult<string, string> =
        async {
            if state.IsInitialized then
                let invoiceNumber = $"CR{credit.Id}"
                printfn $"Created invoice {invoiceNumber} for credit {credit.Id}"
                return Ok invoiceNumber
            else
                return Error "MYOB not initialized"
        }

// ============================================================================
// ORDER PROCESSING
// ============================================================================

module OrderProcessing =
    let processAllPendingWork (config: InvoicerConfig) (myobState: MYOB.MYOBState) : AsyncResult<ProcessingStats, string> =
        async {
            let startTime = DateTime.Now
            
            let! ordersResult = Database.getOrdersToInvoice config config.InvoiceBatchSize
            let! creditsResult = Database.getCreditsToInvoice config config.RefundBatchSize
            let! refundsResult = Database.getRefundsToInvoice config config.RefundBatchSize
            
            match ordersResult, creditsResult, refundsResult with
            | Ok orders, Ok credits, Ok refunds ->
                // Process orders (mock)
                for (orderId, _) in orders do
                    let! _ = Database.markOrderInvoiced orderId
                    ()
                
                // Process credits
                for credit in credits do
                    let! invoiceResult = MYOB.createCreditInvoice myobState credit
                    match invoiceResult with
                    | Ok _ -> let! _ = Database.markCreditInvoiced credit.Id in ()
                    | Error _ -> ()
                
                // Process refunds
                for refund in refunds do
                    let! invoiceResult = MYOB.createCreditInvoice myobState refund
                    match invoiceResult with
                    | Ok _ -> let! _ = Database.markCreditInvoiced refund.Id in ()
                    | Error _ -> ()
                
                let endTime = DateTime.Now
                let stats = {
                    OrdersProcessed = List.length orders
                    OrdersSuccessful = List.length orders
                    OrdersFailed = 0
                    CreditsProcessed = List.length credits
                    RefundsProcessed = List.length refunds
                    ElapsedTime = endTime - startTime
                    Errors = []
                }
                
                return Ok stats
            | Error error, _, _ -> return Error error
            | _, Error error, _ -> return Error error
            | _, _, Error error -> return Error error
        }

// ============================================================================
// TEST THE FUNCTIONAL APPROACH
// ============================================================================

let testConfig = {
    ConnectionString = "test connection"
    InvoiceBatchSize = 10
    RefundBatchSize = 5
    MinimumFinishDate = DateTime.Now.AddDays(-1.0)
    IgnoreCustomers = []
    TestMode = true
}

printfn "\n=== Running Functional Test ==="

let runTest () = async {
    printfn "1. Initializing MYOB..."
    let! myobResult = MYOB.initialize testConfig
    
    match myobResult with
    | Ok myobState ->
        printfn "2. Processing all pending work..."
        let! statsResult = OrderProcessing.processAllPendingWork testConfig myobState
        
        match statsResult with
        | Ok stats ->
            printfn "3. Processing completed successfully!"
            printfn "   Orders processed: %d (successful: %d, failed: %d)" stats.OrdersProcessed stats.OrdersSuccessful stats.OrdersFailed
            printfn "   Credits processed: %d" stats.CreditsProcessed
            printfn "   Refunds processed: %d" stats.RefundsProcessed
            printfn "   Elapsed time: %A" stats.ElapsedTime
            return Ok 0
        | Error error -> 
            printfn "Processing failed: %s" error
            return Error error
    | Error error -> 
        printfn "MYOB initialization failed: %s" error
        return Error error
}

let result = runTest () |> Async.RunSynchronously
match result with
| Ok exitCode -> printfn "\n=== Test completed successfully with exit code %d ===" exitCode
| Error error -> printfn "\n=== Test failed: %s ===" error
