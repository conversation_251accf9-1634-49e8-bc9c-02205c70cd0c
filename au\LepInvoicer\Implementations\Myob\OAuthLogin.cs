using MYOB.AccountRight.SDK;
using System.Text.RegularExpressions;
using System.Windows.Forms;
using System.Web;

namespace LepInvoicer.Implementations.Myob;

/// <summary>
/// OAuth login helper for MYOB authentication
/// </summary>
internal static class OAuthLogin
{
	private const string OAuthServer = "https://secure.myob.com/oauth2/account/authorize/";
	private const string OAuthScope = "CompanyFile";

	/// <summary>
	/// Get authorization code from MYOB OAuth server
	/// </summary>
	/// <param name="config">API configuration with client ID and redirect URL</param>
	/// <returns>OAuth authorization code</returns>
	public static string GetAuthorizationCode(IApiConfiguration config)
	{
		// Format the URL for OAuth login
		string url = string.Format("{0}?client_id={1}&redirect_uri={2}&scope={3}&response_type=code",
			OAuthServer,
			config.ClientId,
			HttpUtility.UrlEncode(config.RedirectUrl),
			OAuthScope);

		Console.WriteLine($"OAuth URL: {url}");

		string authCode = null;
		Exception exception = null;

		// Create a new STA thread for the WebBrowser control
		var staThread = new Thread(() =>
		{
			try
			{
				// Create a new form with a web browser to display OAuth login page
				var frm = new Form();
				var webB = new WebBrowser();
				frm.Controls.Add(webB);
				webB.Dock = DockStyle.Fill;

				// Add a handler for the web browser to capture content change
				webB.DocumentTitleChanged += (sender, e) => WebBDocumentTitleChanged(sender, e, frm);

				// Navigate to URL and display form
				webB.Navigate(url);
				frm.Size = new Size(800, 600);
				frm.StartPosition = FormStartPosition.CenterParent;
				frm.Text = "MYOB OAuth Login";
				frm.ShowDialog();

				// Retrieve the code from the returned HTML
				authCode = ExtractSubstring(webB.DocumentText, "code=", "<");
			}
			catch (Exception ex)
			{
				exception = ex;
			}
		});

		// Set the thread to STA and start it
		staThread.SetApartmentState(ApartmentState.STA);
		staThread.Start();
		staThread.Join(); // Wait for the thread to complete

		// If there was an exception, throw it
		if (exception != null)
		{
			throw exception;
		}

		return authCode;
	}

	/// <summary>
	/// Handler that is called when HTML title is changed in browser
	/// </summary>
	/// <param name="sender">The web browser control</param>
	/// <param name="e">The event</param>
	/// <param name="frm">The parent form</param>
	private static void WebBDocumentTitleChanged(object sender, EventArgs e, Form frm)
	{
		var webB = (WebBrowser)sender;

		// Check if OAuth code is returned
		if (webB.DocumentText.Contains("code="))
		{
			frm.Close();
		}
	}

	/// <summary>
	/// Extract substring from input based on start and end patterns
	/// </summary>
	/// <param name="input">Input string</param>
	/// <param name="startsWith">Start pattern</param>
	/// <param name="endsWith">End pattern</param>
	/// <returns>Extracted substring</returns>
	private static string ExtractSubstring(string input, string startsWith, string endsWith)
	{
		Match match = Regex.Match(input, startsWith + "(.*)" + endsWith);
		string code = match.Groups[1].Value;
		return code;
	}
}
