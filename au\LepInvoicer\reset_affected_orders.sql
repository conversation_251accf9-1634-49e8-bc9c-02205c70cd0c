-- =====================================================
-- RESET AFFECTED ORDERS FOR RE-PROCESSING
-- =====================================================
-- This script marks orders as not invoiced so they can be re-processed
-- Use this AFTER investigating the issue and implementing fixes

-- =====================================================
-- STEP 1: Preview Orders to be Reset
-- =====================================================
-- First, see which orders will be affected
SELECT 
    o.Id as OrderId,
    o.OrderNr,
    o.Invoiced2 as CurrentStatus,
    o.Invoiced2Details,
    o.FinishDate,
    o.<PERSON>fJobs as OrderTotal,
    COUNT(j.Id) as JobCount,
    SUM(ISNULL(j.Price, 0)) as JobsTotal,
    ABS(o.PriceOfJobs - SUM(ISNULL(j.Price, 0))) as Difference,
    c.Name as CustomerName
FROM [Order] o
    INNER JOIN Job j ON j.OrderId = o.Id
    INNER JOIN Customer c ON c.Id = o.CustomerId
WHERE o.Invoiced2 = 'Y'
    AND o.FinishDate >= '2025-06-06'
    AND o.PriceOfJobs > 0
GROUP BY o.Id, o.OrderNr, o.Invoiced2, o.Invoiced2Details, o.FinishDate, o.PriceOfJobs, c.Name
HAVING COUNT(j.Id) > 1  -- Only multi-job orders
    OR ABS(o.PriceOfJobs - SUM(ISNULL(j.Price, 0))) > 0.01  -- Or orders with total mismatches
ORDER BY o.FinishDate DESC, o.Id DESC;

-- =====================================================
-- STEP 2: Reset Specific Order (Order 0416689)
-- =====================================================
-- Reset the specific order mentioned in the issue
UPDATE [Order] 
SET Invoiced2 = NULL, 
    Invoiced2Details = 'Reset for re-processing due to total mismatch - ' + CONVERT(VARCHAR, GETDATE(), 120)
WHERE OrderNr = '0416689'
    AND Invoiced2 = 'Y';

-- Verify the reset
SELECT 
    Id, OrderNr, Invoiced2, Invoiced2Details, PriceOfJobs
FROM [Order] 
WHERE OrderNr = '0416689';

-- =====================================================
-- STEP 3: Reset All Multi-Job Orders (CAREFUL!)
-- =====================================================
-- UNCOMMENT ONLY AFTER CONFIRMING THE FIX IS WORKING
-- This will reset ALL multi-job orders invoiced after June 6th

/*
UPDATE [Order] 
SET Invoiced2 = NULL, 
    Invoiced2Details = 'Reset for re-processing - multi-job order - ' + CONVERT(VARCHAR, GETDATE(), 120)
WHERE Id IN (
    SELECT o.Id
    FROM [Order] o
        INNER JOIN Job j ON j.OrderId = o.Id
    WHERE o.Invoiced2 = 'Y'
        AND o.FinishDate >= '2025-06-06'
        AND o.PriceOfJobs > 0
    GROUP BY o.Id, o.PriceOfJobs
    HAVING COUNT(j.Id) > 1
);
*/

-- =====================================================
-- STEP 4: Reset Orders with Total Mismatches (CAREFUL!)
-- =====================================================
-- UNCOMMENT ONLY AFTER CONFIRMING THE FIX IS WORKING
-- This will reset orders where job totals don't match order totals

/*
UPDATE [Order] 
SET Invoiced2 = NULL, 
    Invoiced2Details = 'Reset for re-processing - total mismatch - ' + CONVERT(VARCHAR, GETDATE(), 120)
WHERE Id IN (
    SELECT o.Id
    FROM [Order] o
        INNER JOIN Job j ON j.OrderId = o.Id
    WHERE o.Invoiced2 = 'Y'
        AND o.FinishDate >= '2025-06-06'
        AND o.PriceOfJobs > 0
    GROUP BY o.Id, o.PriceOfJobs
    HAVING ABS(o.PriceOfJobs - SUM(ISNULL(j.Price, 0))) > 0.01
);
*/

-- =====================================================
-- STEP 5: Verification Queries
-- =====================================================
-- Check how many orders were reset
SELECT 
    COUNT(*) as ResetOrderCount
FROM [Order] 
WHERE Invoiced2Details LIKE '%Reset for re-processing%'
    AND FinishDate >= '2025-06-06';

-- Check orders ready for re-processing
SELECT 
    COUNT(*) as OrdersReadyForReprocessing
FROM [Order] o
    INNER JOIN Job j ON j.OrderId = o.Id
WHERE (o.Invoiced2 IS NULL OR o.Invoiced2 NOT IN ('Y', 'F', 'C'))
    AND o.FinishDate >= '2025-06-06'
    AND o.PriceOfJobs > 0
GROUP BY o.Id
HAVING COUNT(j.Id) >= 1;
