appStaff = angular.module('app.staff')

appStaff.controller 'JobBoardNgController', [
    '$scope', '$interval', '$state', '$window',
    'JobBoardApiService',
    #'JobBoardSignalRService',
    'ngDialog'
    ($scope, $interval, $state, $window, JobBoardService, ngDialog) ->

        vm = @

        # Timing state constants
        TIMING_STATES = {
            READY: 0
            PLAYING: 1
            PAUSED: 2
            FINISHED: 3
        }





        # Controller properties
        vm.jobs = []
        vm.loading = false
        vm.error = null
        vm.currentVersion = 0
        vm.refreshInterval = null

        # Filter properties (will be loaded from localStorage in init)
        vm.selectedFacility = 'FG'
        vm.selectedBoard = 'All'
        vm.facilities = [
            { value: 'FG', label: 'Forest Glen' }
            { value: 'PM', label: 'Port Melbourne' }
        ]
        vm.boards = [
            { value: 'All', label: 'All Jobs' }
            { value: 'Exceptions', label: 'Exceptions' }
            { value: 'PreFlight', label: 'PreFlight' }
            { value: 'PrePress', label: 'PrePress' }
            { value: 'DPCProduction', label: 'DPC Production' }
            { value: 'WideFormatProduction', label: 'Wide Format Production' }
            { value: 'Plateroom', label: 'Plateroom' }
            { value: 'PressRoom', label: 'Press Room' }
            { value: 'Celloglaze', label: 'Celloglaze' }
            { value: 'Guillotine', label: 'Guillotine' }
            { value: 'Folding', label: 'Folding' }
            { value: 'Stitching', label: 'Stitching' }
            { value: 'LetterPress', label: 'Letter Press' }
            { value: 'Finishing', label: 'Finishing' }
            { value: 'Despatch', label: 'Despatch' }
            { value: 'Outwork', label: 'Outwork' }
            { value: 'PayMe', label: 'Pay Me' }
            { value: 'OnHold', label: 'On Hold' }
        ]

        # Alert counters
        vm.alertCounts = { red: 0, amber: 0 }

        # Timing state
        vm.timingSessions = new Map() # jobId-status -> timing data
        vm.timingIntervals = new Map() # jobId-status -> interval ID

        # Controller methods (will be assigned after function definitions)
        vm.loadJobs = null
        vm.onFilterChange = null
        vm.openJobDetails = null
        vm.openOrderDetails = null
        vm.openRunDetails = null
        vm.formatDuration = null
        vm.formatAge = null
        vm.getHealthClass = null
        vm.computeHoursToDispatch = null
        vm.getFacilityLabel = null

        # Timing methods (will be assigned after function definitions)
        vm.startTiming = null
        vm.pauseTiming = null
        vm.resumeTiming = null
        vm.finishTiming = null
        vm.getTimingButtonState = null
        vm.getTimingDisplay = null
        vm.showInstructions = null

        # Initialize
        init = () ->
            # Load user preferences first
            loadUserPreferences()

            # Set up service event listeners (service-agnostic)
            setupServiceEventListeners()

            # Then load jobs and start auto-refresh
            loadJobs()
            startAutoRefresh()

            # Clean up interval on scope destroy
            $scope.$on '$destroy', () ->
                if vm.refreshInterval
                    $interval.cancel(vm.refreshInterval)
                stopAllTimingIntervals()

        # Set up service event listeners for real-time updates (service-agnostic)
        setupServiceEventListeners = () ->
            # Listen for timing action results (from any service)
            $scope.$on 'timing:actionResult', (event, result) ->
                console.log('🔔 Service: Received timing action result:', result)
                return unless result.Success && result.SessionId

                # Find the job that this timing result applies to
                job = vm.jobs.find((j) -> j.id == result.JobId)
                return unless job

                updateTimingState(result.JobId, job.currentStatus, result)
                $scope.$apply() unless $scope.$$phase

            # Listen for timing action errors (from any service)
            $scope.$on 'timing:actionError', (event, error) ->
                console.error('🔔 Service: Received timing action error:', error)
                toastr.error("Timing action failed: #{error.message || 'Unknown error'}")

            # Listen for timing state changes (from any service)
            $scope.$on 'timing:stateChanged', (event, data) ->
                console.log('🔔 Service: Received timing state change:', data)
                return unless data.jobId && data.buttonState

                job = vm.jobs.find((j) -> j.id == data.jobId)
                return unless job

                updateTimingState(data.jobId, job.currentStatus, {
                    SessionId: data.buttonState.SessionId
                    ButtonState: data.buttonState
                })
                $scope.$apply() unless $scope.$$phase

            # Listen for job board refresh requirements (from any service)
            $scope.$on 'jobboard:refreshRequired', (event, data) ->
                console.log('🔔 Service: Job board refresh required:', data)
                loadJobs()

            # Listen for job status changes (from any service)
            $scope.$on 'job:statusChanged', (event, data) ->
                console.log('🔔 Service: Job status changed:', data)
                # Refresh the job board to get updated job data
                loadJobs()

            # Listen for service-specific events (service-agnostic)
            $scope.$on 'service:getJobsResult', (event, response) ->
                handleJobsLoaded(response)
                $scope.$apply() unless $scope.$$phase

            $scope.$on 'service:getJobsError', (event, error) ->
                handleJobsError(error)
                $scope.$apply() unless $scope.$$phase

            $scope.$on 'service:getVersionResult', (event, version) ->
                console.log('🔔 Service: Version result:', version)
                if version > vm.currentVersion
                    loadJobs()
                $scope.$apply() unless $scope.$$phase

        loadJobs = () ->
            vm.loading = true
            vm.error = null
            console.log("🔄 LOADING JOBS - Service-agnostic call")

            # Call service method - both API and SignalR services will emit events
            JobBoardService.getJobs(vm.selectedFacility, vm.selectedBoard)
                .catch (error) ->
                    # Handle immediate errors (network issues, etc.)
                    vm.error = "Error loading job board: #{error.message || error}"
                    vm.loading = false

        # Event handler for successful job loading (service-agnostic)
        handleJobsLoaded = (response) ->
            console.log("📋 JOBS LOADED EVENT:", response)

            if response.version == 0
                vm.error = 'Job board initializing...'
                # Retry after 10 seconds
                setTimeout(() ->
                    $scope.$apply(() -> loadJobs())
                , 10000)
                return

            vm.currentVersion = response.version
            vm.jobs = processJobData(response.data)
            updateAlertCounts()
            loadTimingStates()
            vm.loading = false

        # Event handler for job loading errors (service-agnostic)
        handleJobsError = (error) ->
            console.error("❌ JOBS ERROR EVENT:", error)
            vm.error = "Error loading job board: #{error.message || error}"
            vm.loading = false

        # Utility functions (defined early so they can be used in processJobData)
        computeHoursToDispatch = (job) ->
            return null if !job.orderDispatchEst
            despatchTime = new Date(job.orderDispatchEst).getTime()
            Math.round((despatchTime - Date.now()) / 1000)

        getHealthClass = (health) ->
            switch health
                when 1 then 'red-alert'
                when 2 then 'amber-alert'
                else ''

        processJobData = (jobs) ->
            jobs.map (job) ->
                # Backend is sending camelCase, so use camelCase property names
                processedJob =
                    id: job.id || 0
                    orderId: job.orderId || 0
                    runId: job.runId || null
                    customerName: job.customerName || ''
                    jobName: job.jobName || ''
                    submittedDate: job.submittedDate || null
                    orderDispatchEst: job.orderDispatchEst || null
                    currentStatus: job.currentStatus || ''
                    template: job.template || ''
                    quantity: job.quantity || 0
                    age: job.age || 0
                    health: job.health || 0
                    runIsBC: job.runIsBC || false

                    # Add timing properties (backend sends camelCase)
                    timingSessionId: job.timingSessionId || null
                    timingState: job.timingState || null
                    timingDuration: job.timingDuration || null
                    canPlay: job.canPlay || true
                    canPause: job.canPause || false
                    canResume: job.canResume || false
                    canFinish: job.canFinish || false

                # Add computed properties
                processedJob.hd = job.hd || computeHoursToDispatch(processedJob)
                processedJob.healthClass = getHealthClass(processedJob.health)

                processedJob

        updateAlertCounts = () ->
            vm.alertCounts = vm.jobs.reduce((counts, job) ->
                if job.health == 1
                    counts.red++
                else if job.health == 2
                    counts.amber++
                counts
            , { red: 0, amber: 0 })

        onFilterChange = () ->
            # Save selections to localStorage
            saveUserPreferences()
            loadJobs()

        startAutoRefresh = () ->
            # Check for updates every 30 seconds (service-agnostic)
            vm.refreshInterval = $interval(() ->
                console.log("🔄 AUTO-REFRESH: Checking version")
                JobBoardService.getVersion()
                    .catch (error) ->
                        console.error('Error checking version:', error)
            , 30000)

        openJobDetails = (jobId) ->
            job = getJobById(jobId)
            $state.go('staff.order.job', { orderId: job.orderId, jobId: jobId })

        openOrderDetails = (orderId) ->
            $state.go('staff.order.view', { orderId: orderId })

        openRunDetails = (runId) ->
            return unless runId && runId != -1
            $state.go('staff.run-edit', { runId: runId })

        getJobById = (jobId) ->
            vm.jobs.find((job) -> job.id == jobId) || {}

        # Additional utility functions
        formatDuration = (seconds) ->
            return '' if !seconds
            duration = moment.duration(Math.abs(seconds), 'seconds')
            sign = if seconds < 0 then '-' else ''
            "#{sign}#{Math.floor(duration.asHours())}h #{duration.minutes()}m"

        formatAge = (days) ->
            "#{Math.floor(days)} days"

        getFacilityLabel = () ->
            facility = vm.facilities.find((f) -> f.value == vm.selectedFacility)
            return facility.label if facility
            vm.selectedFacility

        # localStorage utility functions
        saveUserPreferences = () ->
            try
                localStorage.setItem('jobboard.selectedFacility', vm.selectedFacility)
                localStorage.setItem('jobboard.selectedBoard', vm.selectedBoard)
                localStorage.setItem('jobboard.lastUsed', new Date().toISOString())
            catch error
                console.warn('Could not save job board preferences to localStorage:', error)

        loadUserPreferences = () ->
            try
                savedFacility = localStorage.getItem('jobboard.selectedFacility')
                savedBoard = localStorage.getItem('jobboard.selectedBoard')

                vm.selectedFacility = savedFacility if savedFacility
                vm.selectedBoard = savedBoard if savedBoard

                console.log('Loaded job board preferences:', { facility: vm.selectedFacility, board: vm.selectedBoard })
            catch error
                console.warn('Could not load job board preferences from localStorage:', error)

        clearUserPreferences = () ->
            try
                localStorage.removeItem('jobboard.selectedFacility')
                localStorage.removeItem('jobboard.selectedBoard')
                localStorage.removeItem('jobboard.lastUsed')
                vm.selectedFacility = 'FG'
                vm.selectedBoard = 'All'
                console.log('Cleared job board preferences')
            catch error
                console.warn('Could not clear job board preferences:', error)

        # Timing methods
        startTiming = (jobId, status) ->
            console.log("🎬 START TIMING CLICKED - JobId: #{jobId}, Status: #{status}")

            # Show instructions modal first
            showInstructions(jobId, status).then (result) ->
                console.log("📋 INSTRUCTIONS MODAL RESULT:", result)

                unless result.confirmed
                    console.log("❌ INSTRUCTIONS MODAL CANCELLED")
                    return

                console.log("🚀 SENDING START TIMING REQUEST - JobId: #{jobId}, Status: #{status}")

                # Service-agnostic call - events will handle the response
                JobBoardService.startTiming(jobId, status, null, result.instructionsViewed, result.qualityChecksPassed)
                    .catch (error) ->
                        console.error("❌ START TIMING ERROR:", error)
                        handleTimingError(jobId, status, error.data || error)

        pauseTiming = (jobId, status) ->
            # Service-agnostic call - events will handle the response
            JobBoardService.pauseTiming(jobId, status)
                .catch (error) ->
                    console.error("Error pausing timing:", error)

        resumeTiming = (jobId, status) ->
            # Service-agnostic call - events will handle the response
            JobBoardService.resumeTiming(jobId, status)
                .catch (error) ->
                    console.error("Error resuming timing:", error)

        finishTiming = (jobId, status) ->
            # Service-agnostic call - events will handle the response
            JobBoardService.finishTiming(jobId, status)
                .catch (error) ->
                    console.error("Error finishing timing:", error)

        showInstructions = (jobId, status) ->
            job = getJobById(jobId)

            # Load production instructions (service-agnostic)
            # Note: This method still returns a promise because we need the result for the dialog
            JobBoardService.getProductionInstructions(status)
                .then (instructions) ->
                    ngDialog.openConfirm
                        template: 'staff/jobboards/timing-instructions-modal.html'
                        className: 'ngdialog-theme-default timing-instructions-modal'
                        data:
                            job: job
                            status: status
                            instructions: instructions
                        controller: 'TimingInstructionsController'
                .catch () ->
                    # No instructions available, proceed directly
                    { confirmed: true, instructionsViewed: false, qualityChecksPassed: false }

        getTimingButtonState = (jobId, status) ->
            key = "#{jobId}-#{status}"
            session = vm.timingSessions.get(key)

            if session
                switch session.state
                    when 'PLAYING' then { canPlay: false, canPause: true, canResume: false, canFinish: true, currentState: 'PLAYING' }
                    when 'PAUSED'  then { canPlay: false, canPause: false, canResume: true, canFinish: true, currentState: 'PAUSED' }
                    when 'FINISHED' then { canPlay: true, canPause: false, canResume: false, canFinish: false, currentState: 'FINISHED' }
                    else                { canPlay: true, canPause: false, canResume: false, canFinish: false, currentState: 'READY' }
            else
                # Check if job has timing state data from backend (using camelCase properties from JSON)
                job = vm.jobs.find((j) -> j.id == jobId)
                unless job && job.timingSessionId
                    return { canPlay: true, canPause: false, canResume: false, canFinish: false, currentState: 'READY' }

                # Map numeric state to string using constants
                stateMap = {}
                stateMap[TIMING_STATES.READY] = 'READY'
                stateMap[TIMING_STATES.PLAYING] = 'PLAYING'
                stateMap[TIMING_STATES.PAUSED] = 'PAUSED'
                stateMap[TIMING_STATES.FINISHED] = 'FINISHED'

                currentStateStr = stateMap[parseInt(job.timingState)] || 'READY'

                {
                    canPlay: job.canPlay || false
                    canPause: job.canPause || false
                    canResume: job.canResume || false
                    canFinish: job.canFinish || false
                    currentState: currentStateStr
                }

        getTimingDisplay = (jobId, status) ->
            key = "#{jobId}-#{status}"
            session = vm.timingSessions.get(key)

            return '00:00' if !session

            if session.state == 'PLAYING'
                elapsed = Math.floor((new Date() - session.startTime) / 1000)
                totalDuration = session.lastDuration + elapsed
            else
                totalDuration = session.lastDuration

            formatTimingDuration(totalDuration)

        formatTimingDuration = (totalSeconds) ->
            return '00:00' if !totalSeconds
            hours = Math.floor(totalSeconds / 3600)
            minutes = Math.floor((totalSeconds % 3600) / 60)
            seconds = Math.floor(totalSeconds % 60)

            if hours > 0
                "#{pad(hours)}:#{pad(minutes)}:#{pad(seconds)}"
            else
                "#{pad(minutes)}:#{pad(seconds)}"

        pad = (num) ->
            if num < 10 then "0#{num}" else num.toString()

        parseDuration = (durationStr) ->
            return 0 if !durationStr
            parts = durationStr.split('.')[0].split(':')  # Remove milliseconds
            hours = parseInt(parts[0]) || 0
            minutes = parseInt(parts[1]) || 0
            seconds = parseInt(parts[2]) || 0
            (hours * 3600) + (minutes * 60) + seconds

        handleTimingError = (jobId, status, errorResponse) ->
            console.log("Handling timing error:", errorResponse)

            # Check for session already exists error - the response should have ErrorCode property
            isSessionExists = errorResponse.ErrorCode == 'SESSION_ALREADY_EXISTS' ||
                             (errorResponse.Message && errorResponse.Message.includes('Active timing session already exists'))

            unless isSessionExists
                toastr.error("Failed to start timing: #{errorResponse.Message || 'Unknown error'}")
                return

            console.log("Session already exists for job #{jobId}, fetching current state...")
            # Fetch the current timing state and update UI (service-agnostic)
            JobBoardService.getTimingState(jobId, status)
                .then (response) ->
                    console.log("Fetched timing state response:", response)
                    updateTimingState(jobId, status, response)
                    toastr.info('Session already active - showing current state')
                .catch (error) ->
                    console.error("Error fetching timing state:", error)
                    toastr.error("Error retrieving session state")

        updateTimingState = (jobId, status, response) ->
            key = "#{jobId}-#{status}"
            console.log("🔄 UPDATE TIMING STATE CALLED - JobId: #{jobId}, Status: #{status}")
            console.log("🔄 RESPONSE:", response)
            console.log("🔄 RESPONSE KEYS:", Object.keys(response) if response)
            console.log("🔄 RESPONSE.SessionId:", response?.SessionId)
            console.log("🔄 RESPONSE.ButtonState:", response?.ButtonState)

            # Both API and SignalR now return the same structure
            sessionId = response?.SessionId
            buttonState = response?.ButtonState

            unless buttonState
                console.warn("⚠️ NO BUTTON STATE in response:", response)
                return

            # SessionId can be null for jobs without active timing sessions
            if sessionId
                console.log("🔄 Processing job with active session:", sessionId)
            else
                console.log("🔄 Processing job without active session (default state)")

            # Map numeric state to string using constants
            stateMap = {}
            stateMap[TIMING_STATES.READY] = 'READY'
            stateMap[TIMING_STATES.PLAYING] = 'PLAYING'
            stateMap[TIMING_STATES.PAUSED] = 'PAUSED'
            stateMap[TIMING_STATES.FINISHED] = 'FINISHED'

            # Use ButtonState properties from consistent response structure
            console.log("🔄 BUTTON STATE KEYS:", Object.keys(buttonState) if buttonState)
            currentState = buttonState?.CurrentState
            state = stateMap[currentState] || 'READY'

            # Parse duration from "00:15:29.1080388" to seconds
            durationStr = buttonState?.CurrentDuration || "00:00:00.0000000"
            totalSeconds = parseDuration(durationStr)

            console.log("🔄 UPDATING TIMING STATE DETAILS:",
                "JobId:", jobId,
                "Status:", status,
                "SessionId:", sessionId,
                "State:", currentState,
                "Duration:", durationStr,
                "Parsed Seconds:", totalSeconds)

            # Only create timing session if there's an active sessionId
            if sessionId
                vm.timingSessions.set(key, {
                    sessionId: sessionId
                    state: state
                    startTime: new Date()  # Will be adjusted below
                    lastDuration: totalSeconds
                })
            else
                # Remove any existing session for jobs without active timing
                vm.timingSessions.delete(key)

            # Update the job object in vm.jobs to reflect new button states
            job = vm.jobs.find((j) -> j.id == jobId)
            console.log("🔄 FOUND JOB OBJECT:", job?.id, "for JobId:", jobId)

            unless job && currentState != undefined
                console.warn("⚠️ COULD NOT UPDATE JOB OBJECT - Job:", !!job, "CurrentState:", currentState)
            else
                console.log("🔄 UPDATING JOB OBJECT - Before:", {
                    timingSessionId: job.timingSessionId,
                    timingState: job.timingState,
                    canPlay: job.canPlay,
                    canPause: job.canPause,
                    canResume: job.canResume,
                    canFinish: job.canFinish
                })

                job.timingSessionId = sessionId
                job.timingState = currentState.toString()
                job.timingDuration = totalSeconds

                # Set button states based on current state (same logic as backend)
                # Use ButtonState properties from consistent response structure
                job.canPlay = buttonState?.CanPlay || false
                job.canPause = buttonState?.CanPause || false
                job.canResume = buttonState?.CanResume || false
                job.canFinish = buttonState?.CanFinish || false

                console.log("🔄 UPDATED JOB OBJECT - After:", {
                    timingSessionId: job.timingSessionId,
                    timingState: job.timingState,
                    canPlay: job.canPlay,
                    canPause: job.canPause,
                    canResume: job.canResume,
                    canFinish: job.canFinish
                })

            # Adjust start time to reflect actual duration and manage intervals
            if sessionId && state == 'PLAYING'
                now = new Date()
                adjustedStart = new Date(now.getTime() - (totalSeconds * 1000))
                session = vm.timingSessions.get(key)
                if session
                    session.startTime = adjustedStart
                startTimingInterval(jobId, status)
            else
                stopTimingInterval(jobId, status)

        loadTimingStates = () ->
            unless vm.jobs && vm.jobs.length > 0
                console.log("No jobs to load timing states for")
                return

            console.log("Loading timing states from job data for #{vm.jobs.length} jobs")

            for job in vm.jobs
                unless job?.id && job?.currentStatus
                    console.warn("Skipping invalid job:", job)
                    continue

                console.log("Job #{job.id} timing properties:", {
                    timingSessionId: job.timingSessionId,
                    timingState: job.timingState,
                    timingDuration: job.timingDuration,
                    canPlay: job.canPlay,
                    canPause: job.canPause,
                    canResume: job.canResume,
                    canFinish: job.canFinish
                })

                # Convert duration from seconds to TimeSpan format
                durationSeconds = job.timingDuration || 0
                hours = Math.floor(durationSeconds / 3600)
                minutes = Math.floor((durationSeconds % 3600) / 60)
                seconds = durationSeconds % 60
                durationStr = "#{pad(hours)}:#{pad(minutes)}:#{pad(seconds)}.0000000"

                # Create button state object from job data (using PascalCase for API compatibility)
                buttonState = {
                    SessionId: job.timingSessionId
                    CurrentState: parseInt(job.timingState) || 0
                    CurrentDuration: durationStr
                    CanPlay: job.canPlay || false
                    CanPause: job.canPause || false
                    CanResume: job.canResume || false
                    CanFinish: job.canFinish || false
                }

                if job.timingSessionId
                    console.log("Found existing timing session for job #{job.id}: state #{job.timingState}, duration #{job.timingDuration}s")
                else
                    console.log("No active timing session for job #{job.id}, setting up default state")
                    # Clear any stale session data
                    vm.timingSessions.delete("#{job.id}-#{job.currentStatus}")

                console.log("Created ButtonState for job #{job.id}:", buttonState)

                # Create response object that matches API/SignalR structure
                response = {
                    JobId: job.id
                    Success: true
                    Message: if job.timingSessionId then "Timing state loaded from job data" else "Default timing state set"
                    SessionId: job.timingSessionId
                    ButtonState: buttonState
                    ErrorCode: null
                }

                try
                    console.log("Calling updateTimingState for job #{job.id} with response:", response)
                    updateTimingState(job.id, job.currentStatus, response)
                    console.log("Successfully updated timing state for job #{job.id}")
                catch error
                    console.error("Error updating timing state for job #{job.id}:", error)

            console.log("Finished loading timing states for #{vm.jobs.length} jobs")

        startTimingInterval = (jobId, status) ->
            key = "#{jobId}-#{status}"
            stopTimingInterval(jobId, status) # Clear existing

            intervalId = setInterval(() ->
                session = vm.timingSessions.get(key)
                return unless session && session.state == 'PLAYING'
                $scope.$apply() # Trigger digest for live updates
            , 1000)

            vm.timingIntervals.set(key, intervalId)

        stopTimingInterval = (jobId, status) ->
            key = "#{jobId}-#{status}"
            intervalId = vm.timingIntervals.get(key)

            return unless intervalId
            clearInterval(intervalId)
            vm.timingIntervals.delete(key)

        stopAllTimingIntervals = () ->
            vm.timingIntervals.forEach (intervalId) ->
                clearInterval(intervalId)
            vm.timingIntervals.clear()

        # Assign methods to vm
        vm.loadJobs = loadJobs
        vm.onFilterChange = onFilterChange
        vm.openJobDetails = openJobDetails
        vm.openOrderDetails = openOrderDetails
        vm.openRunDetails = openRunDetails
        vm.formatDuration = formatDuration
        vm.formatAge = formatAge
        vm.getHealthClass = getHealthClass
        vm.computeHoursToDispatch = computeHoursToDispatch
        vm.getFacilityLabel = getFacilityLabel
        vm.startTiming = startTiming
        vm.pauseTiming = pauseTiming
        vm.resumeTiming = resumeTiming
        vm.finishTiming = finishTiming
        vm.getTimingButtonState = getTimingButtonState
        vm.getTimingDisplay = getTimingDisplay
        vm.showInstructions = showInstructions
        vm.clearUserPreferences = clearUserPreferences

        # Expose controller to scope for template access
        $scope.vm = vm
        init()

        @
]
