using lep.configuration;
using lep.extensionmethods;
using lep.job;

using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Printing;
using System.Drawing.Text;
using System.IO;
using System.Linq;
using System.Reflection;
using Serilog;

namespace lep.despatch.impl.label
{
	public class WideFormatProcessingThumbnailLabel : BaseFurtherProcessingLabel
	{
		#region Constructors

		public WideFormatProcessingThumbnailLabel()
		{
		}

		#endregion Constructors

		public override IJob Job
		{
			get { return job; }

			set {
				job = value;
				FormatPrintContent();
			}
		}

		#region Protected Methods

		protected override void OnPrintPage(PrintPageEventArgs e)
		{
			base.OnPrintPage(e);
			var g = e.Graphics;

			g.SmoothingMode = SmoothingMode.None;
			g.TextRenderingHint = TextRenderingHint.SingleBitPerPixel;

			g.PixelOffsetMode = PixelOffsetMode.None;
			g.CompositingQuality = CompositingQuality.HighQuality;
			g.InterpolationMode = InterpolationMode.HighQualityBicubic;

			pageNumber++;
			if (pageNumber == 1)
			{
				#region title

				var title = "Wide Format: " + Job.Template.Name;
				g.DrawString(title, titleFont, Brushes.Black, rTitle, titleAlignment);

				#endregion title

				#region print DPC processing instruction in top part

				var strMiddle = processingText1.ToString();
				var hMiddle = (int)g.MeasureString(strMiddle, defaultFont).Height;
				var rMiddle = new Rectangle(left, top + hTitle + 5, width, (266 - top - hTitle - 5));

				g.DrawString(strMiddle, defaultFont, Brushes.Black, rMiddle, middleFormat);

				var strRight = processingText2.ToString();
				var hRight = (int)g.MeasureString(strRight, defaultFont).Height;
				var rright = new Rectangle(width / 2, top + hTitle + 50, width, 200);

				g.DrawString(strRight, defaultFont, Brushes.Black, rright, rightFormat);

				#endregion print DPC processing instruction in top part

				#region draw basic info like cust, job, order

				var basicJobInformation = basicInformation.ToString();
				var hBasicInfo = (int)g.MeasureString(basicJobInformation, defaultFont).Height;
				g.DrawString(basicJobInformation, defaultFont, Brushes.Black, rBasicInfoTxt, middleFormat);

				#endregion draw basic info like cust, job, order

				DrawEDD(g);
				DrawThumbnail(g);
				DrawBarcode(g);
			}

			DrawSpecialInstructions(e, g);

			if (pageNumber == 2)
			{
				e.HasMorePages = false;
			}
		}

		#endregion Protected Methods

		#region Private Methods

		public new void FormatPrintContent()
		{
			try
			{
				basicInformation.AppendFormat(fmt, "Job Name", Job.Name).AppendLine();
				basicInformation.AppendFormat(fmt, "Order #", Job.Order.OrderNr).AppendLine();
				basicInformation.AppendFormat(fmt, "Job # ", Job.JobNr).AppendLine();

				if (Job.ReOrderSourceJobId != 0)
				{
					basicInformation.AppendFormat(fmt, "Orig Job #", Job.ReOrderSourceJobId.ToString()).AppendLine();
				}

				basicInformation.Append(Job.Order.Courier).AppendLine();
				if (Job.Order.Jobs.Count() > 1)
				{
					basicInformation.Append("*** Multi Job Order ***");
				}

				processingText1.AppendFormat(fmt, "Job   #", Job.JobNr).AppendLine();
				processingText1.AppendFormat(fmt, "Stock", Job.FinalStock.Name).AppendLine();
				processingText1.AppendFormat(fmt, "Courier", Job.Order.Courier).AppendLine();
				processingText1.AppendFormat(fmt, "Size", Job.GetJobSize()).AppendLine();
				processingText1.AppendFormat(fmt, "Front", Job.FrontPrinting.ToDescription()).AppendLine();
				processingText1.AppendFormat(fmt, "Back", Job.BackPrinting.ToDescription()).AppendLine();

				if (Job.Pages > 0)
					processingText1.AppendFormat(fmt, "Sheet/pad", Job.Pages).AppendLine();

				processingText1.AppendFormat(fmt, "Quantity", Job.Quantity).AppendLine();

				//processingText1.AppendFormat(fmt, "Print type", "Wide Format").AppendLine();
				if (Job.Order.CustomerLogoRequired)
				{
					processingText1.Append("*** Logo label required ***").AppendLine();
				}
				if (Job.SendSamples == true)
				{
					processingText1.Append("*** Send samples ***").AppendLine();
				}

				//if (Job.SpecialInstructions != null && Job.SpecialInstructions.Length > 0)
				//{
				//    processingText1.Append( Job.SpecialInstructions ).AppendLine() ;

				//}

				//if (Job.Scoring)
				//{
				//    processingText1.Append("Scoring :Yes").AppendLine();
				//}
				//else
				//{
				//    processingText1.Append("Scoring :No").AppendLine();
				//}

				//if (Job.Perforating)
				//{
				//    processingText1.Append("Perforation :Yes").AppendLine();
				//}
				//else
				//{
				//    processingText1.Append("Perforation :No").AppendLine();
				//}

				//processingText1.AppendFormat(fmt, "Diecut", Job.DieCutType.ToDescription()).AppendLine();

				//if (Job.Magnet)
				//{
				//    processingText1.Append("Magnet : Yes").AppendLine();
				//}
				//else
				//{
				//    processingText1.Append("Magnet : No").AppendLine();
				//}

				if (Job.FoldedSize != null)
				{
					processingText1.AppendFormat(fmt, "Fold", Job.FoldedSize.PaperSize.Name).AppendLine();
				}

				if (Job.FoldedSize != null)
				{
					var size = Job.FoldedSize.Height.ToString() + "x" + Job.FoldedSize.Width.ToString();
					var title = Job.IsBusinessCard() ? "Scoring" : "Finish size";
					processingText1.AppendFormat(fmt, title, size).AppendLine();
				}

				if (Job.HoleDrilling != HoleDrilling.None)
				{
					processingText1.AppendFormat(fmt, "Hole Drilling", Job.HoleDrilling.ToDescription()).AppendLine();

					if (Job.NumberOfHoles != null && Job.NumberOfHoles > 0)
					{
						processingText1.AppendFormat(fmt, "# Of Holes", Job.NumberOfHoles).AppendLine();
					}
				}

				if (Job.RoundOption != RoundOption.None)
				{
					processingText1.AppendFormat(fmt, "Round Corner", Job.RoundOption.ToDescription()).AppendLine();

					if (Job.RoundOption == RoundOption.Custom)
					{
						processingText1.AppendLine(Job.CustomDieCut);
					}
					else if (Job.RoundDetailOption != RoundDetailOption.None)
					{
						processingText1.AppendLine(Job.RoundDetailOption.ToDescription());
					}
				}

				processingText1.AppendLine("Printed____\nCello____\nPacked____ ");

				strInstructions = "";
				if (Job.Order.CustomerLogoRequired)
				{
					strInstructions = "*** Logo label required ***" + '\n';
				}
				if (Job.SendSamples == true)
				{
					strInstructions += "*** Send samples ***" + '\n';
				}

				if (Job.SpecialInstructions != null && Job.SpecialInstructions.Length > 0)
				{
					strInstructions += Job.SpecialInstructions + "\n";
				}

				if (Job.ProductionInstructions != null && Job.ProductionInstructions.Length > 0)
				{
					strInstructions += Job.ProductionInstructions;
				}
				strInstructions += "\n" + job.TopLevelPackages();
			}
			catch (Exception ex)
			{
				Log.Error(ex.Message, ex);
				throw;
			}
		}

		#endregion Private Methods
	}
}
