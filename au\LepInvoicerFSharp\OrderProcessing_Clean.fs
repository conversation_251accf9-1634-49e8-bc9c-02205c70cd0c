namespace LepInvoicerFSharp

open System
open LepInvoicerFSharp

// ============================================================================
// ORDER PROCESSING MODULE - Functional processing
// ============================================================================

module OrderProcessing =
    
    /// Process all pending work (functional composition)
    let processAllPendingWork (config: InvoicerConfig) (myobState: MYOB.MYOBState) : AsyncResult<ProcessingStats, string> =
        async {
            let startTime = DateTime.Now
            
            // Get all pending work
            let! ordersResult = Database.getOrdersToInvoice config config.InvoiceBatchSize
            let! creditsResult = Database.getCreditsToInvoice config config.RefundBatchSize
            let! refundsResult = Database.getRefundsToInvoice config config.RefundBatchSize
            
            match ordersResult, creditsResult, refundsResult with
            | Ok orders, Ok credits, Ok refunds ->
                // Process orders
                let! orderResults = 
                    orders
                    |> List.map (fun (orderId, customerUsername) -> 
                        async {
                            // For now, just mark as processed (mock implementation)
                            let! result = Database.markOrderInvoiced orderId
                            return result |> Result.map (fun _ -> true)
                        })
                    |> Async.Sequential
                
                // Process credits  
                let! creditResults =
                    credits
                    |> List.map (fun credit -> 
                        async {
                            let! invoiceResult = MYOBService.processCredit myobState credit
                            match invoiceResult with
                            | Ok invoiceNumber ->
                                let! markResult = Database.markCreditInvoiced credit.Id
                                return markResult |> Result.map (fun _ -> true)
                            | Error error -> return Error error
                        })
                    |> Async.Sequential
                
                // Process refunds
                let! refundResults =
                    refunds
                    |> List.map (fun refund -> 
                        async {
                            let! invoiceResult = MYOBService.processCredit myobState refund // Same as credit
                            match invoiceResult with
                            | Ok invoiceNumber ->
                                let! markResult = Database.markCreditInvoiced refund.Id
                                return markResult |> Result.map (fun _ -> true)
                            | Error error -> return Error error
                        })
                    |> Async.Sequential
                
                // Count successful operations
                let successfulOrders = orderResults |> Array.filter (function | Ok true -> true | _ -> false) |> Array.length
                let successfulCredits = creditResults |> Array.filter (function | Ok true -> true | _ -> false) |> Array.length
                let successfulRefunds = refundResults |> Array.filter (function | Ok true -> true | _ -> false) |> Array.length
                
                // Collect errors
                let errors = 
                    [
                        yield! orderResults |> Array.choose (function | Error e -> Some e | _ -> None)
                        yield! creditResults |> Array.choose (function | Error e -> Some e | _ -> None)
                        yield! refundResults |> Array.choose (function | Error e -> Some e | _ -> None)
                    ]
                    |> Array.toList
                
                let endTime = DateTime.Now
                let stats = {
                    OrdersProcessed = List.length orders
                    OrdersSuccessful = successfulOrders
                    OrdersFailed = List.length orders - successfulOrders
                    CreditsProcessed = successfulCredits
                    RefundsProcessed = successfulRefunds
                    ElapsedTime = endTime - startTime
                    Errors = errors
                }
                
                return Ok stats
                
            | Error error, _, _ -> return Error error
            | _, Error error, _ -> return Error error
            | _, _, Error error -> return Error error
        }
