@echo off
REM =============================================
REM Database Optimization Execution Batch File
REM Target: [SRV03].[PRD_AU]
REM Purpose: Automated execution of database optimization scripts
REM Author: Database Optimization Assistant
REM =============================================

setlocal enabledelayedexpansion

REM Set variables
set SERVER=srv03
set DATABASE=PRD_AU
set USERNAME=sa
set PASSWORD=11_Fore5tGl5n
set SCRIPT_DIR=%~dp0
set LOG_DIR=%SCRIPT_DIR%logs
set TIMESTAMP=%date:~-4,4%%date:~-10,2%%date:~-7,2%_%time:~0,2%%time:~3,2%%time:~6,2%
set TIMESTAMP=%TIMESTAMP: =0%

REM Create logs directory if it doesn't exist
if not exist "%LOG_DIR%" mkdir "%LOG_DIR%"

echo =============================================
echo Database Optimization Execution Starting
echo =============================================
echo Server: %SERVER%
echo Database: %DATABASE%
echo Timestamp: %TIMESTAMP%
echo Script Directory: %SCRIPT_DIR%
echo Log Directory: %LOG_DIR%
echo.

REM Check if sqlcmd is available
sqlcmd -? >nul 2>&1
if errorlevel 1 (
    echo ERROR: sqlcmd is not available or not in PATH
    echo Please install SQL Server Command Line Utilities
    pause
    exit /b 1
)

REM Test database connection
echo Testing database connection...
sqlcmd -S %SERVER% -U %USERNAME% -P "%PASSWORD%" -d %DATABASE% -Q "SELECT 'Connection successful' AS Status, GETDATE() AS CurrentTime" -h -1
if errorlevel 1 (
    echo ERROR: Cannot connect to database
    echo Please check connection parameters
    pause
    exit /b 1
)
echo Connection test successful!
echo.

REM Prompt for confirmation
echo WARNING: This will execute database optimization scripts on %SERVER%.%DATABASE%
echo.
echo The following operations will be performed:
echo 1. Database analysis (read-only)
echo 2. Index optimization (creates indexes, rebuilds fragmented indexes)
echo 3. Statistics updates
echo.
echo IMPORTANT: Ensure you have a recent backup of the database!
echo.
set /p CONFIRM="Do you want to continue? (Y/N): "
if /i not "%CONFIRM%"=="Y" (
    echo Operation cancelled by user
    pause
    exit /b 0
)

echo.
echo =============================================
echo Phase 1: Database Analysis
echo =============================================
echo Executing analysis script...
set ANALYSIS_LOG=%LOG_DIR%\01_analysis_%TIMESTAMP%.log

sqlcmd -S %SERVER% -U %USERNAME% -P "%PASSWORD%" -d %DATABASE% -i "%SCRIPT_DIR%01_database_analysis.sql" -o "%ANALYSIS_LOG%" -r 1
if errorlevel 1 (
    echo ERROR: Analysis script failed
    echo Check log file: %ANALYSIS_LOG%
    pause
    exit /b 1
)
echo Analysis completed successfully
echo Log file: %ANALYSIS_LOG%
echo.

REM Prompt before optimization
echo =============================================
echo Phase 2: Optimization Implementation
echo =============================================
echo.
echo The analysis is complete. Review the results in: %ANALYSIS_LOG%
echo.
set /p CONTINUE="Do you want to proceed with optimization implementation? (Y/N): "
if /i not "%CONTINUE%"=="Y" (
    echo Optimization cancelled. Analysis results are available in the log file.
    pause
    exit /b 0
)

echo.
echo Executing optimization script...
echo This may take several minutes depending on database size...
set OPTIMIZATION_LOG=%LOG_DIR%\02_optimization_%TIMESTAMP%.log

sqlcmd -S %SERVER% -U %USERNAME% -P "%PASSWORD%" -d %DATABASE% -i "%SCRIPT_DIR%02_index_optimization.sql" -o "%OPTIMIZATION_LOG%" -r 1 -t 3600
if errorlevel 1 (
    echo ERROR: Optimization script failed
    echo Check log file: %OPTIMIZATION_LOG%
    echo.
    echo You may need to run the rollback script if partial changes were made
    pause
    exit /b 1
)
echo Optimization completed successfully
echo Log file: %OPTIMIZATION_LOG%
echo.

REM Post-optimization verification
echo =============================================
echo Phase 3: Post-Optimization Verification
echo =============================================
echo Running verification queries...
set VERIFICATION_LOG=%LOG_DIR%\03_verification_%TIMESTAMP%.log

echo -- Post-optimization verification > "%VERIFICATION_LOG%"
echo -- Generated on %date% %time% >> "%VERIFICATION_LOG%"
echo. >> "%VERIFICATION_LOG%"

REM Check newly created indexes
echo Checking newly created indexes... >> "%VERIFICATION_LOG%"
sqlcmd -S %SERVER% -U %USERNAME% -P "%PASSWORD%" -d %DATABASE% -Q "SELECT 'NEWLY_CREATED_INDEXES' AS check_type, COUNT(*) AS count FROM sys.indexes WHERE create_date >= DATEADD(hour, -1, GETDATE()) AND name LIKE 'IX_%'" -h -1 >> "%VERIFICATION_LOG%"

REM Check index fragmentation
echo Checking index fragmentation levels... >> "%VERIFICATION_LOG%"
sqlcmd -S %SERVER% -U %USERNAME% -P "%PASSWORD%" -d %DATABASE% -Q "SELECT 'FRAGMENTATION_CHECK' AS check_type, COUNT(*) AS high_fragmentation_indexes FROM sys.dm_db_index_physical_stats(DB_ID(), NULL, NULL, NULL, 'LIMITED') WHERE avg_fragmentation_in_percent > 30 AND page_count > 100" -h -1 >> "%VERIFICATION_LOG%"

REM Check statistics update dates
echo Checking statistics update status... >> "%VERIFICATION_LOG%"
sqlcmd -S %SERVER% -U %USERNAME% -P "%PASSWORD%" -d %DATABASE% -Q "SELECT 'STATISTICS_CHECK' AS check_type, COUNT(*) AS recently_updated FROM sys.stats s INNER JOIN sys.tables t ON s.object_id = t.object_id WHERE STATS_DATE(s.object_id, s.stats_id) >= DATEADD(hour, -1, GETDATE())" -h -1 >> "%VERIFICATION_LOG%"

echo Verification completed
echo Verification log: %VERIFICATION_LOG%
echo.

REM Summary
echo =============================================
echo Optimization Summary
echo =============================================
echo.
echo All phases completed successfully!
echo.
echo Generated files:
echo - Analysis log: %ANALYSIS_LOG%
echo - Optimization log: %OPTIMIZATION_LOG%
echo - Verification log: %VERIFICATION_LOG%
echo.
echo Next steps:
echo 1. Review all log files for any warnings or errors
echo 2. Monitor database performance over the next 24-48 hours
echo 3. Run application tests to ensure functionality
echo 4. Keep the rollback script available if needed: 03_rollback_optimization.sql
echo.
echo Rollback instructions:
echo If you need to rollback the changes, execute:
echo sqlcmd -S %SERVER% -U %USERNAME% -P "%PASSWORD%" -d %DATABASE% -i "%SCRIPT_DIR%03_rollback_optimization.sql"
echo.

REM Open log directory
set /p OPEN_LOGS="Do you want to open the logs directory? (Y/N): "
if /i "%OPEN_LOGS%"=="Y" (
    explorer "%LOG_DIR%"
)

echo.
echo Database optimization execution completed!
echo Press any key to exit...
pause >nul

endlocal
