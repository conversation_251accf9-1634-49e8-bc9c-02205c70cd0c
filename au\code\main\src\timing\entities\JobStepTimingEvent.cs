using System;
using lep.timing.enums;
using lep;
using lep.job;
using lep.user;

namespace lep.timing.entities
{
    public class JobStepTimingEvent : IEntity
    {
        public virtual int Id { get; set; }
        public virtual int JobId { get; set; }
        public virtual int UserId { get; set; }
        public virtual string Status { get; set; }
        public virtual TimingEventType EventType { get; set; }
        public virtual DateTime EventTime { get; set; }
        public virtual Guid SessionId { get; set; }
        public virtual string Notes { get; set; }
        public virtual string IPAddress { get; set; }
        public virtual string UserAgent { get; set; }
        public virtual bool InstructionsViewed { get; set; }
        public virtual DateTime? InstructionsViewedAt { get; set; }
        public virtual string WorkstationId { get; set; }
        public virtual bool? QualityChecksPassed { get; set; }
        public virtual string CompletionNotes { get; set; }
        public virtual DateTime DateCreated { get; set; }

        // Navigation properties
        public virtual IJob Job { get; set; }
        public virtual IUser User { get; set; }

        public JobStepTimingEvent()
        {
            DateCreated = DateTime.Now;
            EventTime = DateTime.Now;
            InstructionsViewed = false;
        }
    }
}
