-- =============================================
-- Index Optimization Implementation Script
-- Target: [SRV03].[PRD_AU]
-- Purpose: Create missing indexes and optimize existing ones
-- Author: Database Optimization Assistant
-- Date: $(date)
-- =============================================

USE [PRD_AU];
GO

SET NOCOUNT ON;
GO

PRINT '=== INDEX OPTIMIZATION STARTING ===';
PRINT 'Database: ' + DB_NAME();
PRINT 'Server: ' + @@SERVERNAME;
PRINT 'Execution Date: ' + CONVERT(VARCHAR(20), GETDATE(), 120);
PRINT '';

-- =============================================
-- SECTION 1: CREATE MISSING INDEXES
-- =============================================
PRINT '1. CREATING MISSING INDEXES (HIGH IMPACT)...';

-- Create a temporary table to store missing index recommendations
IF OBJECT_ID('tempdb..#MissingIndexes') IS NOT NULL
    DROP TABLE #MissingIndexes;

CREATE TABLE #MissingIndexes (
    id INT IDENTITY(1,1),
    improvement_measure FLOAT,
    create_script NVARCHAR(MAX),
    rollback_script NVARCHAR(MAX),
    table_name NVARCHAR(128),
    impact_level NVARCHAR(10)
);

-- Insert missing index recommendations
INSERT INTO #MissingIndexes (improvement_measure, create_script, rollback_script, table_name, impact_level)
SELECT 
    ROUND(avg_total_user_cost * avg_user_impact * (user_seeks + user_scans), 0) AS improvement_measure,
    'CREATE INDEX [IX_' + 
    OBJECT_NAME(mid.object_id, mid.database_id) + '_' + 
    REPLACE(REPLACE(REPLACE(ISNULL(mid.equality_columns, ''), ', ', '_'), '[', ''), ']', '') +
    CASE WHEN mid.inequality_columns IS NOT NULL THEN '_' + REPLACE(REPLACE(REPLACE(mid.inequality_columns, ', ', '_'), '[', ''), ']', '') ELSE '' END +
    '] ON [' + OBJECT_SCHEMA_NAME(mid.object_id, mid.database_id) + '].[' + OBJECT_NAME(mid.object_id, mid.database_id) + '] (' +
    ISNULL(mid.equality_columns, '') +
    CASE WHEN mid.equality_columns IS NOT NULL AND mid.inequality_columns IS NOT NULL THEN ', ' ELSE '' END +
    ISNULL(mid.inequality_columns, '') + ')' +
    CASE WHEN mid.included_columns IS NOT NULL THEN ' INCLUDE (' + mid.included_columns + ')' ELSE '' END +
    ' WITH (FILLFACTOR = 90, ONLINE = ON);' AS create_script,
    'DROP INDEX [IX_' + 
    OBJECT_NAME(mid.object_id, mid.database_id) + '_' + 
    REPLACE(REPLACE(REPLACE(ISNULL(mid.equality_columns, ''), ', ', '_'), '[', ''), ']', '') +
    CASE WHEN mid.inequality_columns IS NOT NULL THEN '_' + REPLACE(REPLACE(REPLACE(mid.inequality_columns, ', ', '_'), '[', ''), ']', '') ELSE '' END +
    '] ON [' + OBJECT_SCHEMA_NAME(mid.object_id, mid.database_id) + '].[' + OBJECT_NAME(mid.object_id, mid.database_id) + '];' AS rollback_script,
    OBJECT_NAME(mid.object_id, mid.database_id) AS table_name,
    CASE 
        WHEN ROUND(avg_total_user_cost * avg_user_impact * (user_seeks + user_scans), 0) > 100000 THEN 'HIGH'
        WHEN ROUND(avg_total_user_cost * avg_user_impact * (user_seeks + user_scans), 0) > 50000 THEN 'MEDIUM'
        ELSE 'LOW'
    END AS impact_level
FROM sys.dm_db_missing_index_details AS mid
INNER JOIN sys.dm_db_missing_index_groups AS mig ON mid.index_handle = mig.index_handle
INNER JOIN sys.dm_db_missing_index_group_stats AS migs ON mig.index_group_handle = migs.group_handle
WHERE mid.database_id = DB_ID()
    AND ROUND(avg_total_user_cost * avg_user_impact * (user_seeks + user_scans), 0) > 50000
ORDER BY improvement_measure DESC;

-- Execute high-impact missing index creation
DECLARE @sql NVARCHAR(MAX);
DECLARE @table_name NVARCHAR(128);
DECLARE @impact_level NVARCHAR(10);
DECLARE @improvement FLOAT;

DECLARE missing_index_cursor CURSOR FOR
SELECT create_script, table_name, impact_level, improvement_measure
FROM #MissingIndexes
WHERE impact_level = 'HIGH'
ORDER BY improvement_measure DESC;

OPEN missing_index_cursor;
FETCH NEXT FROM missing_index_cursor INTO @sql, @table_name, @impact_level, @improvement;

WHILE @@FETCH_STATUS = 0
BEGIN
    PRINT 'Creating high-impact index for table: ' + @table_name + ' (Impact: ' + CAST(@improvement AS VARCHAR(20)) + ')';
    
    BEGIN TRY
        EXEC sp_executesql @sql;
        PRINT 'SUCCESS: Index created successfully.';
    END TRY
    BEGIN CATCH
        PRINT 'ERROR: ' + ERROR_MESSAGE();
        PRINT 'Failed SQL: ' + @sql;
    END CATCH
    
    PRINT '';
    FETCH NEXT FROM missing_index_cursor INTO @sql, @table_name, @impact_level, @improvement;
END

CLOSE missing_index_cursor;
DEALLOCATE missing_index_cursor;

PRINT '';

-- =============================================
-- SECTION 2: INDEX MAINTENANCE (REBUILD/REORGANIZE)
-- =============================================
PRINT '2. PERFORMING INDEX MAINTENANCE...';

-- Create temporary table for fragmented indexes
IF OBJECT_ID('tempdb..#FragmentedIndexes') IS NOT NULL
    DROP TABLE #FragmentedIndexes;

CREATE TABLE #FragmentedIndexes (
    id INT IDENTITY(1,1),
    schema_name NVARCHAR(128),
    table_name NVARCHAR(128),
    index_name NVARCHAR(128),
    fragmentation_percent FLOAT,
    page_count BIGINT,
    maintenance_script NVARCHAR(MAX),
    impact_level NVARCHAR(10)
);

-- Insert fragmented indexes
INSERT INTO #FragmentedIndexes (schema_name, table_name, index_name, fragmentation_percent, page_count, maintenance_script, impact_level)
SELECT 
    OBJECT_SCHEMA_NAME(ips.object_id) AS schema_name,
    OBJECT_NAME(ips.object_id) AS table_name,
    i.name AS index_name,
    ips.avg_fragmentation_in_percent,
    ips.page_count,
    CASE 
        WHEN ips.avg_fragmentation_in_percent > 30 AND ips.page_count > 1000 THEN 'ALTER INDEX [' + i.name + '] ON [' + OBJECT_SCHEMA_NAME(ips.object_id) + '].[' + OBJECT_NAME(ips.object_id) + '] REBUILD WITH (ONLINE = ON, FILLFACTOR = 90);'
        WHEN ips.avg_fragmentation_in_percent > 10 AND ips.page_count > 1000 THEN 'ALTER INDEX [' + i.name + '] ON [' + OBJECT_SCHEMA_NAME(ips.object_id) + '].[' + OBJECT_NAME(ips.object_id) + '] REORGANIZE;'
        ELSE 'No action required'
    END AS maintenance_script,
    CASE 
        WHEN ips.avg_fragmentation_in_percent > 30 THEN 'HIGH'
        WHEN ips.avg_fragmentation_in_percent > 10 THEN 'MEDIUM'
        ELSE 'LOW'
    END AS impact_level
FROM sys.dm_db_index_physical_stats(DB_ID(), NULL, NULL, NULL, 'LIMITED') AS ips
INNER JOIN sys.indexes AS i ON ips.object_id = i.object_id AND ips.index_id = i.index_id
WHERE ips.avg_fragmentation_in_percent > 10
    AND ips.page_count > 100
    AND i.name IS NOT NULL
    AND maintenance_script != 'No action required';

-- Execute index maintenance for high fragmentation
DECLARE maintenance_cursor CURSOR FOR
SELECT maintenance_script, table_name, index_name, fragmentation_percent, impact_level
FROM #FragmentedIndexes
WHERE impact_level IN ('HIGH', 'MEDIUM')
ORDER BY fragmentation_percent DESC;

DECLARE @index_name NVARCHAR(128);
DECLARE @fragmentation FLOAT;

OPEN maintenance_cursor;
FETCH NEXT FROM maintenance_cursor INTO @sql, @table_name, @index_name, @fragmentation, @impact_level;

WHILE @@FETCH_STATUS = 0
BEGIN
    PRINT 'Maintaining index: ' + @index_name + ' on table: ' + @table_name + ' (Fragmentation: ' + CAST(@fragmentation AS VARCHAR(10)) + '%)';
    
    BEGIN TRY
        EXEC sp_executesql @sql;
        PRINT 'SUCCESS: Index maintenance completed.';
    END TRY
    BEGIN CATCH
        PRINT 'ERROR: ' + ERROR_MESSAGE();
        PRINT 'Failed SQL: ' + @sql;
    END CATCH
    
    PRINT '';
    FETCH NEXT FROM maintenance_cursor INTO @sql, @table_name, @index_name, @fragmentation, @impact_level;
END

CLOSE maintenance_cursor;
DEALLOCATE maintenance_cursor;

PRINT '';

-- =============================================
-- SECTION 3: STATISTICS UPDATE
-- =============================================
PRINT '3. UPDATING STATISTICS...';

-- Update statistics for tables with high activity
DECLARE @stats_sql NVARCHAR(MAX);
DECLARE stats_cursor CURSOR FOR
SELECT 'UPDATE STATISTICS [' + OBJECT_SCHEMA_NAME(object_id) + '].[' + OBJECT_NAME(object_id) + '] WITH FULLSCAN;'
FROM sys.tables
WHERE OBJECT_NAME(object_id) IN ('Order', 'Job', 'Runs')
   OR object_id IN (
       SELECT DISTINCT object_id 
       FROM #MissingIndexes 
       UNION 
       SELECT DISTINCT OBJECT_ID(schema_name + '.' + table_name) 
       FROM #FragmentedIndexes
   );

OPEN stats_cursor;
FETCH NEXT FROM stats_cursor INTO @stats_sql;

WHILE @@FETCH_STATUS = 0
BEGIN
    PRINT 'Updating statistics: ' + @stats_sql;
    
    BEGIN TRY
        EXEC sp_executesql @stats_sql;
        PRINT 'SUCCESS: Statistics updated.';
    END TRY
    BEGIN CATCH
        PRINT 'ERROR: ' + ERROR_MESSAGE();
    END CATCH
    
    PRINT '';
    FETCH NEXT FROM stats_cursor INTO @stats_sql;
END

CLOSE stats_cursor;
DEALLOCATE stats_cursor;

-- Cleanup temporary tables
DROP TABLE #MissingIndexes;
DROP TABLE #FragmentedIndexes;

PRINT '';
PRINT '=== INDEX OPTIMIZATION COMPLETED ===';
PRINT 'All high-impact optimizations have been applied.';
PRINT 'Monitor query performance and adjust as needed.';

-- Final verification query
PRINT '';
PRINT '=== POST-OPTIMIZATION VERIFICATION ===';
SELECT 
    COUNT(*) AS total_indexes,
    SUM(CASE WHEN is_disabled = 1 THEN 1 ELSE 0 END) AS disabled_indexes,
    AVG(fill_factor) AS avg_fill_factor
FROM sys.indexes
WHERE object_id IN (SELECT object_id FROM sys.tables WHERE is_ms_shipped = 0)
    AND type > 0;
