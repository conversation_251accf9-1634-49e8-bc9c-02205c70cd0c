using lep.extensionmethods;
using lep.job;
using lep.run;
using lep.security;
using lep.timing;

using Serilog;
using NHibernate;
using NHibernate.Criterion;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using lep.job.impl;
using NHibernate.Transform;

namespace lep.jobmonitor.impl
{
	/// <summary>
	/// JobBoardDTOHelper class
	///		encapsulates and isolates logic for creating or updating the DTO from a given job
	///		so the JobBoard DTO can remain anemic
	/// </summary>
	public class JobBoardDTOHelper : BaseApplication, IInitializingObject
	{
		#region Readonly

		//private static readonly ILog Log = LogManager.GetLogger("JobBoard");

		#endregion Readonly

		#region Constants

		public const string AMBER_ALERT = "amberAlert";
		public const string NORMAL = "noAlert";
		public const string RED_ALERT = "redAlert";
		private const string STR_DateFormat = "dd/MM/yy HH:mm";

		#endregion Constants

		#region Constructors


		public JobBoardDTOHelper(ISession sf, ISecurityApplication _securityApp,
			  JobBoard globalJobBoard,
			  IScheduleApplication scheduleApplication,
			  IJobApplication jobApplication,
			  ITimingApplication timingApplication
		  ) : base(sf, _securityApp)
		{
			GlobalJobBoard = globalJobBoard;
			ScheduleApplication = scheduleApplication;
			JobApplication = jobApplication;
			TimingApplication = timingApplication;

		}


		public void AfterPropertiesSet()
		{
			//LoadJobBoardLayout();
		}

		#endregion Constructors

		#region Properties

		public JobBoard GlobalJobBoard { get; set; }
		public IJobApplication JobApplication { get; set; }
		public IList<ISchedule> MySchedules { get; set; }
		public IScheduleApplication ScheduleApplication { get; set; }
		public ITimingApplication TimingApplication { get; set; }
		public DirectoryInfo JobBoardLayoutDirectory { get; set; }

		public Dictionary<string, Dictionary<string, JobBoardDisplayColumnSetting>> JobBoardLayouts
		{
			get;
			protected set;
		}

		#endregion Properties


		public static int numberOfWeekendsBetween(DateTime d0, DateTime d1)
		{
			int ndays = 1 + Convert.ToInt32((d1 - d0).TotalDays);
			int nsaturdays = (ndays + Convert.ToInt32(d0.DayOfWeek)) / 7;
			return nsaturdays;
		}
		public static TimeSpan HoursTillDispatch(DateTime d0, DateTime d1)
		{
			var hrd = TimeSpan.Zero;
			hrd = d1 - d0;
			hrd = hrd.Subtract(TimeSpan.FromHours(numberOfWeekendsBetween(d0, d1) * 48));
			return hrd;
		}

		#region Public Methods

		public string GetHoursRemainingToDispatch(IJob job)
		{
			var now = DateTime.Now;
			string sign = "";
			var hrd = TimeSpan.Zero;
			if (job.Status == JobStatusOptions.Dispatched || job.IsOpenish())
			{
				return "";
			}
			if (job.RequiredByDate != null)
			{
				var deadLine = job.RequiredByDate.Value;
				hrd = HoursTillDispatch(now, deadLine);
				if (deadLine < now) sign = "-";
			}
			else

			if (job.Order.DispatchEst != null)
			{
				var deadLine = job.Order.DispatchEst.Value;
				hrd = HoursTillDispatch(now, deadLine);
				if (deadLine < now) sign = "-";
			}
			var r = "";
			if (hrd != TimeSpan.Zero)
			{
				int h = Math.Abs((int)hrd.TotalHours);
				int m = Math.Abs((int)(hrd.TotalMinutes - ((int)hrd.TotalHours * 60)));
				var s = h.ToString("d2") + ":" + m.ToString("d2");
				r = sign + s;
			}
			return r;
		}

		public static TimeSpan? GetHoursRemainingToDispatch2(IJob job)
		{
			var now = DateTime.Now;

			TimeSpan? hrd = null;

			//if (job.Status == JobStatusOptions.Dispatched || job.IsOpenish())
			//{
			//	return null;
			//}
			if (job.RequiredByDate != null)
			{
				var deadLine = job.RequiredByDate.Value;
				hrd = HoursTillDispatch(now, deadLine);
			}
			else

			if (job.Order.DispatchEst != null)
			{
				var deadLine = job.Order.DispatchEst.Value;
				hrd = HoursTillDispatch(now, deadLine);
			}
			return hrd;
		}

		public static TimeSpan? GetHoursRemainingToDispatch2Run(DateTime? from)
		{
			var now = DateTime.Now;

			TimeSpan? hrd = null;

			if (from != null)
			{
				var deadLine = from.Value;
				hrd = HoursTillDispatch(now, deadLine);
			}
			return hrd;
		}


		// Create a JobDetailDTO from a given job
		public JobDetailsDTO Create(IJob j)
		{
			var jd = new JobDetailsDTO();
			try
			{
				jd.Id = j.Id;
				jd.Facility = j.Facility.Value;

				jd.CurrentStatus = j.Status.ToString();


				jd.NextStatus = j.NextStatus.ToString();

				if (j.ReceivedDate.HasValue)
				{
					jd.SubmittedDate = j.ReceivedDate.Value.ToString(STR_DateFormat); //?
				}

				jd.DespatchByDate = j.RequiredByDate != null ? j.RequiredByDate.Value.ToString(STR_DateFormat) : "";
				jd.StatusDate = j.StatusDate == DateTime.MinValue ? null : (DateTime?)j.StatusDate;//.ToString(STR_DateFormat);

				jd.RunId = j.Runs.Any() ? j.Runs[0].Id : null;
				jd.RunIsBC = j.Runs.Any() && j.Runs[0].IsBusinessCard;

				jd.OrderId = j.Order.Id;
				jd.CustomerName = j.Order.Customer.Name;
				jd.PressDetailPaperSize = "";

				jd.JobName = j.Name;
				jd.Template = j.Template.Name;
				jd.Stock = j.FinalStock.StockString();

				jd.Cello = j.Celloglaze.ToDescription().Replace("None", ""); //Replace("M", "Matt").Replace("f", "Front").Replace("b", "Back").Replace("G", "Gloss")
				jd.Folding = j.FoldedSize?.PaperSize.Name.Replace("Business Card", "BC") ?? "";
				jd.TrimSize = j.GetJobSize();
				jd.Quantity = j.Quantity;

				jd.Age = JobAging.GetStandardAge(j);

				var x = GetHoursRemainingToDispatch2(j);
				jd.HD = x == null ? null : (long?)x.Value.TotalSeconds;

				jd.Health = DetermineJobHealth(j);
				jd.UpddateAt = DateTime.Now;
				jd.BoardsToAppear = JobBoardSelectionHelper.GetBoardsToAppearIn(j);

				jd.RowType = "1J";

				jd.OrderDispatchEst = j.Order.DispatchEst;

				jd.PressDetailPaperSize = j.GetPressDetailsPaperSizes();
				jd.PressSheets = j.GetNumberOfPressSheets();

				//jd.PressDetailPaperSize = j.PressDetailPaperSizes;
				//jd.PressSheets = j.PressDetailPressSheets;
				jd.PrintByDate = j.PrintByDate ?? null;
				jd.Version = DateTime.Now.Ticks;
			}
			catch (Exception ex)
			{
				Log.Error(ex.StackTrace, ex);
			}

			return jd;
		}

		public JobDetailsDTO CreateFromRun(int rId, int health)
		{
			var jd = new JobDetailsDTO();
			try
			{
				var r = Session.Get<IRun>(rId);
				jd.Id = 0;
				jd.CurrentStatus = r.Status.ToString();
				var nextMinStatusOfJobUnderThisRun = r.Jobs.Select(j => j.NextStatus).Distinct().Min();
				jd.NextStatus = nextMinStatusOfJobUnderThisRun.ToString();
				jd.SubmittedDate = "";
				var earliestEDDs = r.Jobs.Select(_ => _.RequiredByDate.HasValue ? _.RequiredByDate : _.Order.DispatchEst);
				var earliestEDD = earliestEDDs.Min();

				if (!r.IsBusinessCard)
				{
					string earliestEDDStr = (jd.DespatchByDate = earliestEDDs.Min().Value.ToString(STR_DateFormat) ?? "");
					jd.DespatchByDate = earliestEDDStr;
				}

				var x = GetHoursRemainingToDispatch2Run(earliestEDD);
				jd.HD = x == null ? null : (long?)x.Value.TotalSeconds;

				jd.RunId = rId;
				jd.RunIsBC = true;
				jd.OrderId = 0;
				jd.Facility = r.Facility;

				// summarize customer name
				var cnames = r.Jobs.Select(rj => rj.Order.Customer.Name).Distinct();
				if (cnames.Count() == 1)
				{
					jd.CustomerName = cnames.First();
				}
				else
				{
					jd.CustomerName = "*(" + cnames.Count().ToString() + ")";
				}

				// summarize job names
				jd.JobName = "*(" + r.Jobs.Count().ToString() + ")";
				var jobNames = r.Jobs.Select(rj => rj.Name).Distinct();
				if (jobNames.Count() == 1)
				{
					jd.JobName = jobNames.First();
				}
				else
				{
					jd.JobName = "*(" + jobNames.Count().ToString() + ")";
				}

				var jobnumbers = r.Jobs.Select(rj => rj.Id.ToString()).Distinct().Aggregate((m, n) => m + ' ' + n);
				var ordnumbers = r.Jobs.Select(rj => rj.Order.Id.ToString()).Distinct().Aggregate((m, n) => m + ' ' + n);
				jd.ExtraData = jobnumbers + " " + ordnumbers;

				jd.Template = "Business Cards Run";
				if (r.Jobs.Any(rj => rj.IsNDD()))
				{
					jd.Template = "NDD Business Cards Run";
				}
				jd.Stock = r.Stock.StockString();
				jd.Cello = r.Celloglaze.ToDescription().Replace("None", "");
				jd.Folding = "";
				jd.Quantity = 0;
				jd.PressSheets = r.NumOfPressSheets ?? 0;

				jd.Health = health;
				jd.UpddateAt = DateTime.Now;

				//jd.BoardsToAppear = JobBoardSelectionHelper.OffsetJob_GetBoardsToAppearIn(nextMinStatusOfJobUnderThisRun);
				jd.BoardsToAppear = r.Jobs.SelectMany(j => JobBoardSelectionHelper.GetBoardsToAppearIn(j)).Distinct().ToArray<JobBoardTypes>();
				jd.RowType = "0R";

				jd.Version = DateTime.Now.Ticks;
			}
			catch (Exception ex)
			{
				Log.Error(ex.StackTrace, ex);
			}

			return jd;
		}

		public async Task CreateJobBoardAsync()
		{

			try
			{
				MySchedules = ScheduleApplication.GetAllStandardSchedules();
				//JobBoard GlobalJobBoard  =  (JobBoard)WebApplicationContext.Current.GetObject("GlobalJobBoard");

				#region hide

				//ICriteria jobC  = base.Session.CreateCriteria(typeof(lep.job.IJob),"j")
				//    .CreateAlias("j.Template","Template")
				//    .CreateAlias("j.Stock","Stock")
				//    .CreateAlias("j.Order","Order")
				//    .CreateAlias("Order.Customer","Customer")
				//    .Add(Expression.Conjunction()
				//            .Add(Expression.Not(Expression.Eq("j.Status",JobStatusOptions.Open)))
				//            .Add(Expression.Eq("j.Enable",true))
				//            .Add(Expression.Eq("j.SupplyArtworkApproval",JobApprovalOptions.NotNeeded))
				//            .Add(Expression.Eq("j.ReadyArtworkApproval",JobApprovalOptions.NotNeeded))
				//            .Add(Expression.Eq("j.ProofStatus",JobProofStatus.None))
				//            .Add(Expression.Eq("j.QuoteNeedApprove",false))
				//            .Add(Expression.IsNotNull("j.NextStatus"))
				//            .Add(Expression.Not(Expression.Disjunction()
				//                    .Add(Expression.Eq("j.NextStatus",JobStatusOptions.Open))
				//                    .Add(Expression.Eq("j.NextStatus",JobStatusOptions.Complete))
				//                        ))
				//        )
				//    .SetFetchMode("Template",NHibernate.FetchMode.Eager)
				//    .SetFetchMode("Stock",NHibernate.FetchMode.Eager)
				//    .SetFetchMode("Runs",NHibernate.FetchMode.Eager)
				//    .SetFetchMode("Order",NHibernate.FetchMode.Eager)
				//    .SetFetchMode("Customer",NHibernate.FetchMode.Eager);

				/*
				 * If LEP wants based on Job Status  instead of NextStatus
				 * need to update next status column for all the rows return by the following	*/

				#endregion hide

				//Console.WriteLine("creating job board " + GlobalJobBoard.GetHashCode());

				//var jobC = Session.CreateCriteria(typeof(IJob), "j")
				//			.Add(Restrictions.Conjunction()
				//			.Add(Restrictions.Not(Restrictions.In("j.Status", new object[] { JobStatusOptions.Open, JobStatusOptions.Dispatched, JobStatusOptions.Complete, JobStatusOptions.UnableToMeetPrice, JobStatusOptions.RejectedVariation })))
				//			.Add(Restrictions.Eq("j.QuoteNeedApprove", false))
				//			.Add(Restrictions.Eq("j.SupplyArtworkApproval", JobApprovalOptions.NotNeeded))
				//			.Add(Restrictions.Eq("j.ReadyArtworkApproval", JobApprovalOptions.NotNeeded))
				//			.Add(Restrictions.Gt("j.ReceivedDate", DateTime.Now.Date.AddMonths(-6)))
				//	);


				var jobC = Session.CreateCriteria(typeof(IJob), "j")
					.Fetch(SelectMode.Fetch, "Template")
					.Fetch(SelectMode.Fetch, "Stock")
					.Fetch(SelectMode.Fetch, "Runs")
					.Fetch(SelectMode.Fetch, "Order")
					.Fetch(SelectMode.Fetch, "Customer")
					.Add(NHibernate.Criterion.Expression.Sql(
						@"{alias}.Status not in ('Open', 'Dispatched', 'Complete', 'UnableToMeetPrice', 'RejectedVariation')
						and {alias}.QuoteNeedApprove  = 'N'
						and {alias}.LepApproval       = 'NotNeeded'
						and {alias}.ArtworkApproval   = 'NotNeeded'
						and {alias}.ReceivedDate >= Cast( DateAdd(m,-6, GetDate()) as date) "))
					.AddOrder(Order.Asc("j.Facility"))
					.SetCacheable(false);

				//jobC.SetProjection(
				//	Projections.ProjectionList()
				//		.Add(Projections.Property("j.Id"), "Id")
				//		.Add(Projections.Property("j.Status"), "Status")
				//		.Add(Projections.Property("j.NextStatus"), "NextStatus")
				//		.Add(Projections.Property("j.ReceivedDate"), "ReceivedDate")
				//		.Add(Projections.Property("j.Facility"), "Facility")
				//		.Add(Projections.Property("j.ReceivedDate"), "ReceivedDate")
				//		// add  RequiredByDate OrderId Template  Stock Cello Folding Quantity
				//		.Add(Projections.Property("j.RequiredByDate"), "RequiredByDate")
				//	)





				//.SetFetchMode("Template", FetchMode.Eager)
				//.SetFetchMode("Stock", FetchMode.Eager)
				//.SetFetchMode("Runs", FetchMode.Eager)
				//.SetFetchMode("Order", FetchMode.Eager)
				//.SetFetchMode("Customer", FetchMode.Eager)

				//var jobC = Session.CreateCriteria(typeof(IJob), "j")
				//		.Add(Restrictions.InG("j.Id", new List<int> {
				//			1590219,
				//			1590220,
				//			1590307,
				//			1590308,
				//			1590309,
				//			1590310,
				//			1590494,
				//			1590495,
				//			1590496,
				//			1590497,
				//			1590498,
				//			1591163,
				//			1591320



				//	}));
				var jobs = jobC.List<IJob>();
				//var jobs2 = jobs.ToList<IJob>();

				//jobs2.Sort(delegate (IJob j1, IJob j2){
				//	if(j1.Runs.Any() && j2.Runs.Any())
				//	{
				//		return (j1.Runs.First().Id.CompareTo(j1.Runs.First().Id));
				//	}
				//	else
				//	return (j1.Id.CompareTo(j1.Id));
				//	}
				//);

				//jobs = jobs2;




				var tempList = jobs.Select(givenJob => Create(givenJob)).ToList<JobDetailsDTO>();
				// tempList now has all the JobBoardDTOs that need to be on the board...

				// Load timing states for all jobs in batch
				await PopulateTimingStatesAsync(tempList);

				//// find all the BC run ids  that need to be grouped
				//var bcRunIds = tempList.Where(givenJob => givenJob.RunIsBC).Select(a => a.RunId).Distinct().ToList();
				////var bcRunIds = jobs.Where(givenJob => givenJob.Runs.Any()).Select(a => a.Runs[0].Id).Distinct().ToList();
				//foreach (var rId in bcRunIds)
				//{
				//	// deduce runs health
				//	var run_priority = 3;
				//	if (tempList.Count(j => j.RunId == rId && j.Health == 1) > 0)
				//	{
				//		run_priority = 1;
				//	}
				//	else if (tempList.Count(j => j.RunId == rId && j.Health == 2) > 0)
				//	{
				//		run_priority = 2;
				//	}
				//	// find the first job of the run
				//	var i = tempList.FindIndex(jd => jd.RunId == rId);

				//	// insert the run row just before the first job
				//	if (i > 0)
				//	{
				//		tempList.Insert(i - 1, CreateFromRun(rId, run_priority));
				//	}
				//	else
				//	{
				//		tempList.Add(CreateFromRun(rId, run_priority));
				//	}
				//}
				/**/
				////lock (GlobalJobBoard.SyncRoot)
				//// {
				//GlobalJobBoard.Entries = new ConcurrentBag<JobDetailsDTO>(tempList);// tempList;
				//GlobalJobBoard.Version = DateTime.Now.Ticks;
				//// }

				Log.Information("Create Job Board job board dto helper");
				Log.Information("Job Count : " + GlobalJobBoard.Entries.Count());

				//var runIdsInBoard = tempList.Where(x => x.Id == 0 && x.RunId != 0).Select(x => x.RunId).Distinct();

				//foreach (var runId in runIdsInBoard)
				//{
				//	var count = tempList.Count(x => x.RunId == runId);
				//	if (count == 1)
				//	{
				//		tempList.RemoveAll(x => x.Id == 0 && x.RunId == runId);
				//	}
				//}

				////tempList = tempList.OrderBy(x => x.RunId).ToList();
				//tempList.Sort((x, y) => x.RunId.CompareTo(y.RunId));

				lock (GlobalJobBoard.SyncRoot)
				{
					GlobalJobBoard.Entries.Clear();
					GlobalJobBoard.Entries.AddRange(tempList);
				}

				GlobalJobBoard.UpdateVersion();
			}
			catch (Exception ex)
			{
				Log.Error(ex.StackTrace, ex);
			}
		}

		// 1 = red , 2 = orange , 3 = okay for now
		public int DetermineJobHealth(IJob j)
		{
			if (j.NextStatus > JobStatusOptions.Filling)
			{
				return 0;
			}

			var folding = IsJobFoldingDepandant(j);

			List<ISchedule> nextSchedulesForThisJob2;

			nextSchedulesForThisJob2 = MySchedules.Where(s =>
						   s.Template.Id == j.Template.Id &&
						   s.Folding == folding &&
						   s.Status == j.NextStatus &&
						   s.Facility == j.Facility.Value).ToList();

			ISchedule nextSchedule;
			if (nextSchedulesForThisJob2.Count == 0)
			{
				nextSchedule = new Schedule() { Id = -1 };
			}
			else
			{
				nextSchedule = nextSchedulesForThisJob2[0];
			}

			var age = JobAging.GetStandardAge(j);
			// if a next scheduling point is defined in the system
			if (nextSchedule.Id != -1)
			{
				if ((decimal)age.TotalHours > nextSchedule.Red)
					return 1;
				if ((decimal)age.TotalHours > nextSchedule.Amber)
					return 2;
			}
			return 3;
		}

		public string GetJobHealthCSS(int jobId)
		{
			string result = NORMAL;
			if (GlobalJobBoard == null || GlobalJobBoard.Entries == null || GlobalJobBoard.Entries.Count == 0)
				return result;

			try
			{
				// Write the health of a Job
				if (GlobalJobBoard.Entries.Count(m => m.Id == jobId && m.Health == 1) > 0)
					result = RED_ALERT;
				else if (GlobalJobBoard.Entries.Count(m => m.Id == jobId && m.Health == 2) > 0)
					result = AMBER_ALERT;
				else
					result = NORMAL;
			}
			catch (Exception) { }

			return result;
		}

		public string GetOrderHealthCSS(int orderId)
		{
			string result = NORMAL;
			if (GlobalJobBoard == null || GlobalJobBoard.Entries == null || GlobalJobBoard.Entries.Count == 0)
				return result;
			lock (GlobalJobBoard.SyncRoot)
			{
				try
				{
					// Write the health of a Job
					if (GlobalJobBoard.Entries.Count(sj => sj.OrderId == orderId && sj.Health == 1) > 0)
						result = RED_ALERT;
					else if (GlobalJobBoard.Entries.Count(sj => sj.OrderId == orderId && sj.Health == 2) > 0)
						result = AMBER_ALERT;
					else
						result = NORMAL;
				}
				catch (Exception) { }
			}
			return result;
		}

		public string GetRunHealthCSS(int runId)
		{
			string result = NORMAL;
			if (GlobalJobBoard == null || GlobalJobBoard.Entries == null || GlobalJobBoard.Entries.Count == 0)
				return result;
			lock (GlobalJobBoard.SyncRoot)
			{
				try
				{
					// Write the health of a Job
					if (GlobalJobBoard.Entries.Count(m => m.RunId == runId && m.Health == 1) > 0)
						result = RED_ALERT;
					else if (GlobalJobBoard.Entries.Count(m => m.RunId == runId && m.Health == 2) > 0)
						result = AMBER_ALERT;
					else
						result = NORMAL;
				}
				catch (Exception) { }
			}

			return result;
		}

		public void Update(ref JobDetailsDTO jd, IJob x, bool forStaff)
		{
			jd.CurrentStatus = x.Render(forStaff);
			jd.NextStatus = x.NextStatus.ToString();
			jd.Health = DetermineJobHealth(x);
			jd.Age = JobAging.GetStandardAge(x);
			jd.UpddateAt = DateTime.Now;
			jd.Version = DateTime.Now.Ticks;
			jd.PressSheets = x.GetNumberOfPressSheets();
			jd.BoardsToAppear = JobBoardSelectionHelper.GetBoardsToAppearIn(x);
		}

		public async Task PopulateTimingStatesAsync(List<JobDetailsDTO> jobList)
		{
			try
			{
				// Get all active sessions for all users (bypass cache to ensure fresh data)
				var allActiveSessions = await Session.CreateCriteria<lep.timing.entities.JobStepActiveSession>()
					.Add(Restrictions.Eq("IsActive", true))
					.SetCacheable(false)  // Bypass NHibernate cache to get fresh data
					.ListAsync<lep.timing.entities.JobStepActiveSession>();

				// Create a lookup dictionary for quick access
				var sessionLookup = allActiveSessions
					.GroupBy(s => $"{s.JobId}-{s.Status}")
					.ToDictionary(g => g.Key, g => g.First());

				// Populate timing state for each job
				foreach (var job in jobList)
				{
					var key = $"{job.Id}-{job.CurrentStatus}";
					if (sessionLookup.TryGetValue(key, out var session))
					{
						// Job has an active timing session
						job.TimingSessionId = session.SessionId;
						job.TimingState = ((int)session.CurrentState).ToString(); // Convert enum to integer string
						job.TimingDuration = (int)session.CurrentWorkingDuration.TotalSeconds;

						// Set button states based on session state
						switch (session.CurrentState)
						{
							case lep.timing.enums.TimingSessionState.PLAYING:
								job.CanPlay = false;
								job.CanPause = true;
								job.CanResume = false;
								job.CanFinish = true;
								break;
							case lep.timing.enums.TimingSessionState.PAUSED:
								job.CanPlay = false;
								job.CanPause = false;
								job.CanResume = true;
								job.CanFinish = true;
								break;
							default:
								job.CanPlay = true;
								job.CanPause = false;
								job.CanResume = false;
								job.CanFinish = false;
								break;
						}
					}
					else
					{
						// No active session - show play button
						job.TimingSessionId = null;
						job.TimingState = "0"; // READY state as integer string
						job.TimingDuration = null;
						job.CanPlay = true;
						job.CanPause = false;
						job.CanResume = false;
						job.CanFinish = false;
					}
				}

				Log.Information("Populated timing states for {JobCount} jobs, found {SessionCount} active sessions",
					jobList.Count, allActiveSessions.Count);
			}
			catch (Exception ex)
			{
				Log.Error(ex, "Error populating timing states for job board");
				// Don't fail the entire job board load if timing fails
				// Just set default states for all jobs
				foreach (var job in jobList)
				{
					job.TimingSessionId = null;
					job.TimingState = "READY";
					job.TimingDuration = null;
					job.CanPlay = true;
					job.CanPause = false;
					job.CanResume = false;
					job.CanFinish = false;
				}
			}
		}

		#endregion Public Methods

		#region Private Methods

		//private void LoadJobBoardLayout()
		//{
		//	return;
		//	JobBoardLayouts = new Dictionary<string, Dictionary<string, JobBoardDisplayColumnSetting>>();
		//	if (JobBoardLayoutDirectory == null || !JobBoardLayoutDirectory.Exists)
		//	{
		//		return;
		//	}

		//	foreach (var l in from l in JobBoardLayoutDirectory.GetFiles()
		//					  where l.Extension.ToLower() == ".layout"
		//					  select l)
		//	{
		//		try
		//		{
		//			var parser = new JobBoardLayoutParser();
		//			parser.HandleQuotes = true;
		//			using (var r = l.OpenText())
		//			{
		//				parser.Parse(r);
		//			}
		//			JobBoardLayouts.Add(Path.GetFileNameWithoutExtension(l.Name), parser.Data);
		//		}
		//		catch (Exception ex)
		//		{
		//			Log.Error(ex.Message);
		//		}
		//	}
		//}

		private object ColorizeRuns(JobDetailsDTO x)
		{
			x.HealthOfRun = GlobalJobBoard.Entries.Where(e1 => e1.RunId == x.RunId).Min(e2 => e2.Health);
			return x;
		}

		private static bool IsJobFoldingDepandant(IJob job)
		{
			var folding = false;

			if (job.IsMagazine())
				return true;

			if (job.IsBrochure())
			{
				folding = job.FoldedSize == null ? false : true;
			}
			else
			{
				folding = false;
			}
			return folding;
		}

		#endregion Private Methods
	}
}
