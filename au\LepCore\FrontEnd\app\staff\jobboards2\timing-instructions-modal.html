<div class="timing-instructions-modal">
    <div class="modal-header">
        <h4 class="modal-title">
            <i class="glyphicon glyphicon-play"></i>
            Start Timing - {{ngDialogData.status}}
        </h4>
    </div>
    
    <div class="modal-body">
        <!-- Job Information -->
        <div class="job-info-panel">
            <h5>Job Information</h5>
            <div class="row">
                <div class="col-md-6">
                    <dl class="dl-horizontal">
                        <dt>Job ID:</dt>
                        <dd>{{ngDialogData.job.id}}</dd>
                        <dt>Job Name:</dt>
                        <dd>{{ngDialogData.job.jobName}}</dd>
                        <dt>Customer:</dt>
                        <dd>{{ngDialogData.job.customerName}}</dd>
                    </dl>
                </div>
                <div class="col-md-6">
                    <dl class="dl-horizontal">
                        <dt>Order ID:</dt>
                        <dd>{{ngDialogData.job.orderId}}</dd>
                        <dt>Status:</dt>
                        <dd><strong>{{ngDialogData.status}}</strong></dd>
                        <dt>Quantity:</dt>
                        <dd>{{ngDialogData.job.quantity | number}}</dd>
                    </dl>
                </div>
            </div>
        </div>

        <!-- Production Instructions -->
        <div class="instructions-panel" ng-if="ngDialogData.instructions">
            <h5>
                <i class="glyphicon glyphicon-list-alt"></i>
                Production Instructions
            </h5>
            <div class="instructions-content" ng-bind-html="ngDialogData.instructions.content"></div>
            
            <!-- Quality Checks -->
            <div class="quality-checks" ng-if="ngDialogData.instructions.qualityChecks">
                <h6>Quality Checks:</h6>
                <div class="checkbox" ng-repeat="check in ngDialogData.instructions.qualityChecks">
                    <label>
                        <input type="checkbox" ng-model="check.completed">
                        {{check.description}}
                    </label>
                </div>
            </div>
        </div>

        <!-- No Instructions Available -->
        <div class="no-instructions" ng-if="!ngDialogData.instructions">
            <div class="alert alert-info">
                <i class="glyphicon glyphicon-info-sign"></i>
                No specific production instructions available for this status.
            </div>
        </div>

        <!-- Confirmation -->
        <div class="confirmation-panel">
            <div class="checkbox">
                <label>
                    <input type="checkbox" ng-model="instructionsRead" ng-if="ngDialogData.instructions">
                    <span ng-if="ngDialogData.instructions">I have read and understood the production instructions</span>
                    <input type="checkbox" ng-model="instructionsRead" ng-if="!ngDialogData.instructions" checked>
                    <span ng-if="!ngDialogData.instructions">Ready to start timing</span>
                </label>
            </div>
        </div>
    </div>
    
    <div class="modal-footer">
        <button type="button" 
                class="btn btn-success" 
                ng-click="startTiming()"
                ng-disabled="ngDialogData.instructions && !instructionsRead">
            <i class="glyphicon glyphicon-play"></i>
            Start Timing
        </button>
        <button type="button" class="btn btn-default" ng-click="closeThisDialog()">
            <i class="glyphicon glyphicon-remove"></i>
            Cancel
        </button>
    </div>
</div>

<style>
.timing-instructions-modal {
    max-width: 700px;
    margin: 0 auto;
}

.timing-instructions-modal .modal-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    padding: 15px 20px;
    border-radius: 6px 6px 0 0;
}

.timing-instructions-modal .modal-title {
    margin: 0;
    color: #333;
    font-size: 18px;
}

.timing-instructions-modal .modal-body {
    padding: 20px;
    max-height: 500px;
    overflow-y: auto;
}

.timing-instructions-modal .modal-footer {
    background-color: #f8f9fa;
    border-top: 1px solid #dee2e6;
    padding: 15px 20px;
    text-align: right;
    border-radius: 0 0 6px 6px;
}

.job-info-panel {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 20px;
}

.job-info-panel h5 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #495057;
    font-weight: bold;
}

.job-info-panel .dl-horizontal dt {
    width: 80px;
    text-align: left;
    font-weight: normal;
    color: #6c757d;
}

.job-info-panel .dl-horizontal dd {
    margin-left: 90px;
    font-weight: bold;
}

.instructions-panel {
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 20px;
}

.instructions-panel h5 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #495057;
    font-weight: bold;
}

.instructions-content {
    background-color: #fff;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 15px;
    line-height: 1.6;
}

.instructions-content h1,
.instructions-content h2,
.instructions-content h3,
.instructions-content h4,
.instructions-content h5,
.instructions-content h6 {
    margin-top: 0;
    margin-bottom: 10px;
    color: #495057;
}

.instructions-content ul,
.instructions-content ol {
    margin-bottom: 10px;
    padding-left: 20px;
}

.instructions-content li {
    margin-bottom: 5px;
}

.quality-checks {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 4px;
    padding: 15px;
}

.quality-checks h6 {
    margin-top: 0;
    margin-bottom: 10px;
    color: #856404;
    font-weight: bold;
}

.quality-checks .checkbox {
    margin-bottom: 8px;
}

.quality-checks .checkbox:last-child {
    margin-bottom: 0;
}

.no-instructions {
    margin-bottom: 20px;
}

.confirmation-panel {
    background-color: #e7f3ff;
    border: 1px solid #b3d7ff;
    border-radius: 4px;
    padding: 15px;
}

.confirmation-panel .checkbox {
    margin-bottom: 0;
}

.confirmation-panel .checkbox label {
    font-weight: bold;
    color: #0056b3;
}

.btn {
    margin-left: 10px;
}

.btn:first-child {
    margin-left: 0;
}
</style>
