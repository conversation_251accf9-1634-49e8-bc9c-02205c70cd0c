namespace lep.timing
{
    /// <summary>
    /// Configuration constants for the timing system
    /// </summary>
    public static class TimingConfiguration
    {
        /// <summary>
        /// Maximum hours for a timing session before auto-timeout
        /// </summary>
        public const int MAX_SESSION_HOURS = 8;
        
        /// <summary>
        /// Auto-pause session after X minutes of inactivity
        /// </summary>
        public const int AUTO_PAUSE_MINUTES = 30;
        
        /// <summary>
        /// Require users to view instructions before starting timing
        /// </summary>
        public const bool REQUIRE_INSTRUCTIONS = false;
        
        /// <summary>
        /// Require quality check confirmation on finish
        /// </summary>
        public const bool REQUIRE_QUALITY_CHECKS = false;
        
        /// <summary>
        /// Enable real-time updates (for future SignalR integration)
        /// </summary>
        public const bool ENABLE_REALTIME_UPDATES = true;
        
        /// <summary>
        /// Default estimated duration for production steps (in minutes)
        /// </summary>
        public const int DEFAULT_ESTIMATED_DURATION = 30;
        
        /// <summary>
        /// Maximum number of active sessions per user
        /// </summary>
        public const int MAX_ACTIVE_SESSIONS_PER_USER = 10;
        
        /// <summary>
        /// Interval for live timer updates (in milliseconds)
        /// </summary>
        public const int TIMER_UPDATE_INTERVAL = 1000;
        
        /// <summary>
        /// Maximum length for timing notes
        /// </summary>
        public const int MAX_NOTES_LENGTH = 500;
        
        /// <summary>
        /// Maximum length for completion notes
        /// </summary>
        public const int MAX_COMPLETION_NOTES_LENGTH = 1000;
        
        /// <summary>
        /// Default workstation ID prefix
        /// </summary>
        public const string DEFAULT_WORKSTATION_PREFIX = "WS";
        
        /// <summary>
        /// System user identifier for automated actions
        /// </summary>
        public const string SYSTEM_USER = "SYSTEM";
    }
}
