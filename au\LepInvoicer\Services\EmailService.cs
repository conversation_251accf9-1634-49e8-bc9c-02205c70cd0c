using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Net.Mail;

namespace LepInvoicer.Services;

/// <summary>
/// Email service implementation
/// </summary>
public class EmailService : IEmailService
{
    private readonly ILogger<EmailService> _logger;
    private readonly InvoicerConfiguration _config;

    public EmailService(ILogger<EmailService> logger, IOptions<InvoicerConfiguration> config)
    {
        _logger = logger;
        _config = config.Value;
    }

    public Task<bool> SendInvoiceEmailAsync(string toAddress, string invoiceNumber, string pdfFilePath)
    {
        if (string.IsNullOrEmpty(toAddress))
        {
            _logger.LogWarning("Cannot send email - no recipient address provided");
            return Task.FromResult(false);
        }

        if (!File.Exists(pdfFilePath))
        {
            _logger.LogWarning("Cannot send email - PDF file not found: {PdfPath}", pdfFilePath);
            return Task.FromResult(false);
        }

        try
        {
            _logger.LogInformation("Sending invoice email to {Email} for invoice {InvoiceNumber}", toAddress, invoiceNumber);
            
            // TODO: Implement actual email sending
            // For now, just log the action
            _logger.LogInformation("Email would be sent to {Email} with attachment {PdfPath}", toAddress, pdfFilePath);
            
            return Task.FromResult(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send email to {Email} for invoice {InvoiceNumber}", toAddress, invoiceNumber);
            return Task.FromResult(false);
        }
    }

    public MailMessage CreateEmail()
    {
        var mail = new MailMessage
        {
            From = new MailAddress(_config.Smtp.FromAddress, _config.Smtp.FromName),
            IsBodyHtml = true
        };

        return mail;
    }
}
