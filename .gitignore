*.bak
*.suo
*.lock
Ankh.NoLoad
packages/
PrecompiledWeb/
release/
debug/
SalekTests
config*.xsl
config-dev.txt
config-dev.txt0
database.txt
lep.5.1.ReSharper.user
lep.sln.cache
release
results
code/cron/bin
code/cron/obj
code/cron/app.config
code/cron/*.csproj.user
code/main/obj
code/main/bin
code/main/*.csproj.user
code/main/[Bb]in
code/main/[Dd]ebug
code/main/[Rr]elease
code/main/*.user
code/main/*.aps
code/main/*.eto
code/main/Properties/AssemblyVersionInfo.cs
code/scanner-engine/obj
code/scanner-engine/bin
code/scanner-engine/*.csproj.user
code/scanner-interface/obj
code/scanner-interface/bin
code/scanner-interface/*.csproj.user
code/scanner-service/obj
code/scanner-service/bin
code/scanner-service/*.csproj.user
code/scanner-test/obj
code/scanner-test/bin
code/scanner-test/*.csproj.user
code/test/obj
code/test/bin
code/test/*.csproj.user
code/test/src/app.config
code/test/src/App.config
code/testdata/testdata.csproj.user
code/testdata/obj
code/testdata/bin
code/testdata/src/app.config
code/wwwtest/obj
code/wwwtest/bin
code/wwwtest/*.csproj.user
code/wwwtest/src/app.config
code/wwwtest/src/App.config
lumen/[Bb]in
lumen/obj
lumen/[Dd]ebug
lumen/[Rr]elease
lumen/*.user
lumen/*.aps
lumen/*.eto
lumen/resource
lumen/HtmlAgilityPack/[Bb]in
lumen/HtmlAgilityPack/obj
lumen/HtmlAgilityPack/[Dd]ebug
lumen/HtmlAgilityPack/[Rr]elease
lumen/HtmlAgilityPack/*.user
lumen/HtmlAgilityPack/*.aps
lumen/HtmlAgilityPack/*.eto
lumen/Properties/AssemblyVersionInfo.cs
nant/version.txt
au/www/Web.config
au/www/bin
au/www/data
au/www/logs
au/www/web.config
au/www/app_data/tmp
www/app_themes/lep/images/Thumbs.db
www/app_themes/lep/images/bg/Thumbs.db
www/app_themes/lep/images/btn/Thumbs.db
www/app_themes/lep/images/nav/Thumbs.db
www/bin/*.dll
www/bin/*.pdb
www/bin/*.xml
www/cache/*.js
/au/lepcore/FontsUnused
/au/lumen/bin
/au/lumen/obj
/au/lumen/resource
/au/www/bin
/au/www/data
/au/www/logs
/au/www/cache
/au/www/customerlogos
/au/_ReSharper.lep
/au/www/testdata
/au/www/wwwtest
/au/www/bin
/au/.idea/
www/web.config
wwwroot/lib/*
project.fragment.lock.json
bower_components
node_modules
obj
bin
release
.user
/au/.vs
/au/www2/bin
au/LepCore/wwwroot/
au/LepCore/dist.dev/
au/LepCore/dist.prod/
au/healthy-gulp-angular-master2/
au/healthy-gulp-angular-master/
/au/LepCore/bin
/au/LepCore/.idea
/au/LepCore/script
/au/LepCore/logs
/au/LepCore/wwwroot.dev
/au/LepCore/wwwroot.dist
/au/LepCore.Tests/project.lock.json
/au/LepCore/project.lock.json
.DS_Store
__MACOSX
/au/LepCore.Tests/LepCore.Tests.xproj.user
/au/LepCore/LepCore.xproj.user
job/*.csv
.vs
.vscode
/SEQ
/LEPDB
/upd
Untitled-1.txt
LEP.myox
LORD-1195.txt
Untitled.txt
dockerDBMSSQL.txt
PressSheetAllowances - LORD/
au/Libs/win32-x64-64_binding.node
.ionide
au/LepCore - Copy
au/LepCore/app - Copy
3cx
au/_PriceFiles
au/ii2
/au/MigrationBackup
au/LepCore/Properties/PublishProfiles/FS.pubxml.user
au/WestpacCSVtoMyob/packages
.aider*
au/WebApplication1
au/LepCore/LepCore.csproj.user
au/LepCore/Properties/PublishProfiles/FolderProfile.pubxml.user
au/LepInvoicer/logs
*.log
