using System;
using lep.timing.enums;
using Newtonsoft.Json;

namespace lep.timing.dto
{
    public class TimingButtonState
    {
        public bool CanPlay { get; set; }
        public bool CanPause { get; set; }
        public bool CanResume { get; set; }
        public bool CanFinish { get; set; }

        [JsonProperty("CurrentState")]
        public int? CurrentStateValue
        {
            get => CurrentState.HasValue ? (int)CurrentState.Value : (int?)null;
            set => CurrentState = value.HasValue ? (TimingSessionState)value.Value : (TimingSessionState?)null;
        }

        [JsonIgnore]
        public TimingSessionState? CurrentState { get; set; }

        public TimeSpan? CurrentDuration { get; set; }
        public DateTime? LastEventTime { get; set; }
        public Guid? SessionId { get; set; }
        public string StatusMessage { get; set; }

        public static TimingButtonState Initial()
        {
            return new TimingButtonState
            {
                CanPlay = true,
                CanPause = false,
                CanResume = false,
                CanFinish = false,
                CurrentState = null,
                CurrentDuration = null,
                StatusMessage = "Ready to start"
            };
        }

        public static TimingButtonState Playing(TimeSpan duration, Guid sessionId)
        {
            return new TimingButtonState
            {
                CanPlay = false,
                CanPause = true,
                CanResume = false,
                CanFinish = true,
                CurrentState = TimingSessionState.PLAYING,
                CurrentDuration = duration,
                SessionId = sessionId,
                StatusMessage = "In progress"
            };
        }

        public static TimingButtonState Paused(TimeSpan duration, Guid sessionId)
        {
            return new TimingButtonState
            {
                CanPlay = false,
                CanPause = false,
                CanResume = true,
                CanFinish = true,
                CurrentState = TimingSessionState.PAUSED,
                CurrentDuration = duration,
                SessionId = sessionId,
                StatusMessage = "Paused"
            };
        }

        public static TimingButtonState Finished(TimeSpan duration)
        {
            return new TimingButtonState
            {
                CanPlay = true,
                CanPause = false,
                CanResume = false,
                CanFinish = false,
                CurrentState = TimingSessionState.FINISHED,
                CurrentDuration = duration,
                StatusMessage = "Completed"
            };
        }
    }
}
