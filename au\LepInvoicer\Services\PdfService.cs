using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace LepInvoicer.Services;

/// <summary>
/// PDF generation service implementation
/// </summary>
public class PdfService : IPdfService
{
    private readonly ILogger<PdfService> _logger;
    private readonly InvoicerConfiguration _config;

    public PdfService(ILogger<PdfService> logger, IOptions<InvoicerConfiguration> config)
    {
        _logger = logger;
        _config = config.Value;
    }

    public Task<string> GenerateOrderInvoicePdfAsync(IOrder order, string outputPath)
    {
        try
        {
            _logger.LogInformation("Generating PDF invoice for order {OrderId} at {OutputPath}", order.Id, outputPath);

            // Ensure directory exists
            InvoicerUtilities.EnsureDirectoryExists(Path.GetDirectoryName(outputPath));

            // TODO: Implement actual PDF generation using FastReport
            // For now, create a placeholder file with order details
            var content = $@"Invoice PDF for Order {order.Id}
Customer: {order.Customer.Name}
Total: {order.PriceOfJobs:C}
Finish Date: {order.FinishDate}
Generated at: {DateTime.Now}";

            File.WriteAllText(outputPath, content);

            _logger.LogInformation("Successfully generated PDF for order {OrderId}", order.Id);
            return Task.FromResult(outputPath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to generate PDF for order {OrderId}", order.Id);
            throw;
        }
    }

    public Task<string> GenerateCreditInvoicePdfAsync(OrderCredit orderCredit, string outputPath)
    {
        try
        {
            _logger.LogInformation("Generating PDF credit invoice for credit {CreditId} at {OutputPath}", orderCredit.Id, outputPath);

            // Ensure directory exists
            InvoicerUtilities.EnsureDirectoryExists(Path.GetDirectoryName(outputPath));

            // TODO: Implement actual PDF generation using FastReport
            // For now, create a placeholder file with credit details
            var content = $@"Credit Invoice PDF for Credit {orderCredit.Id}
Amount: {orderCredit.Amount:C}
Generated at: {DateTime.Now}";

            File.WriteAllText(outputPath, content);

            _logger.LogInformation("Successfully generated PDF for credit {CreditId}", orderCredit.Id);
            return Task.FromResult(outputPath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to generate PDF for credit {CreditId}", orderCredit.Id);
            throw;
        }
    }
}
