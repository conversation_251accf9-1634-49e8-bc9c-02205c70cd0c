using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace LepCore.Services
{
    /// <summary>
    /// Wrapper service that delays the startup of another hosted service
    /// </summary>
    public class DelayedHostedService : IHostedService
    {
        private readonly IHostedService _innerService;
        private readonly TimeSpan _delay;
        private readonly ILogger<DelayedHostedService> _logger;

        public DelayedHostedService(IHostedService innerService, TimeSpan delay)
        {
            _innerService = innerService;
            _delay = delay;
            _logger = Microsoft.Extensions.Logging.Abstractions.NullLogger<DelayedHostedService>.Instance;
        }

        public async Task StartAsync(CancellationToken cancellationToken)
        {
            _logger?.LogInformation("Delaying start of {ServiceType} for {DelaySeconds} seconds", 
                _innerService.GetType().Name, _delay.TotalSeconds);

            // Wait for the delay period before starting the inner service
            await Task.Delay(_delay, cancellationToken);

            if (!cancellationToken.IsCancellationRequested)
            {
                _logger?.LogInformation("Starting delayed service {ServiceType}", _innerService.GetType().Name);
                await _innerService.StartAsync(cancellationToken);
            }
        }

        public async Task StopAsync(CancellationToken cancellationToken)
        {
            _logger?.LogInformation("Stopping delayed service {ServiceType}", _innerService.GetType().Name);
            await _innerService.StopAsync(cancellationToken);
        }
    }
}
