using System;

namespace lep.timing.exceptions
{
    public class TimingValidationException : Exception
    {
        public string ErrorCode { get; }
        public int? JobId { get; }
        public int? UserId { get; }
        public string Status { get; }
        
        public TimingValidationException(string message, string errorCode = null) 
            : base(message)
        {
            ErrorCode = errorCode;
        }
        
        public TimingValidationException(string message, string errorCode, int jobId, int userId, string status) 
            : base(message)
        {
            ErrorCode = errorCode;
            JobId = jobId;
            UserId = userId;
            Status = status;
        }
        
        public TimingValidationException(string message, Exception innerException, string errorCode = null) 
            : base(message, innerException)
        {
            ErrorCode = errorCode;
        }
        
        // Common validation error factory methods
        public static TimingValidationException JobNotFound(int jobId)
        {
            return new TimingValidationException(
                $"Job with ID {jobId} not found or not accessible",
                "JOB_NOT_FOUND");
        }
        
        public static TimingValidationException InvalidJobStatus(int jobId, string currentStatus, string requiredStatus)
        {
            return new TimingValidationException(
                $"Job {jobId} is in status '{currentStatus}' but requires '{requiredStatus}' for timing operations",
                "INVALID_JOB_STATUS");
        }
        
        public static TimingValidationException SessionNotFound(int jobId, int userId, string status)
        {
            return new TimingValidationException(
                $"No active timing session found for Job {jobId}, User {userId}, Status '{status}'",
                "SESSION_NOT_FOUND",
                jobId, userId, status);
        }
        
        public static TimingValidationException InvalidStateTransition(string fromState, string toState)
        {
            return new TimingValidationException(
                $"Invalid state transition from '{fromState}' to '{toState}'",
                "INVALID_STATE_TRANSITION");
        }
        
        public static TimingValidationException SessionAlreadyExists(int jobId, int userId, string status)
        {
            return new TimingValidationException(
                $"Active timing session already exists for Job {jobId}, User {userId}, Status '{status}'",
                "SESSION_ALREADY_EXISTS",
                jobId, userId, status);
        }
        
        public static TimingValidationException UserNotAuthorized(int userId, int jobId)
        {
            return new TimingValidationException(
                $"User {userId} is not authorized to perform timing operations on Job {jobId}",
                "USER_NOT_AUTHORIZED");
        }
        
        public static TimingValidationException SessionExpired(Guid sessionId)
        {
            return new TimingValidationException(
                $"Timing session {sessionId} has expired",
                "SESSION_EXPIRED");
        }
        
        public static TimingValidationException MaxSessionTimeExceeded(int maxHours)
        {
            return new TimingValidationException(
                $"Timing session has exceeded maximum allowed time of {maxHours} hours",
                "MAX_SESSION_TIME_EXCEEDED");
        }
    }
}
