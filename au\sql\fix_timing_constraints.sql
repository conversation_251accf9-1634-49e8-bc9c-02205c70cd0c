-- Fix timing table constraints to accept enum integer values
-- This script updates the CHECK constraints to work with NHibernate enum mappings

SET QUOTED_IDENTIFIER ON
SET ANSI_NULLS ON

-- Drop existing constraints
IF EXISTS (SELECT * FROM sys.check_constraints WHERE name = 'CK_JobStepActiveSession_State')
    ALTER TABLE [dbo].[JobStepActiveSession] DROP CONSTRAINT [CK_JobStepActiveSession_State]

IF EXISTS (SELECT * FROM sys.check_constraints WHERE name = 'CK_JobStepTimingEvent_EventType')
    ALTER TABLE [dbo].[JobStepTimingEvent] DROP CONSTRAINT [CK_JobStepTimingEvent_EventType]

-- Update CurrentState column to accept integers
ALTER TABLE [dbo].[JobStepActiveSession]
ALTER COLUMN [CurrentState] [int] NOT NULL

-- Update EventType column to accept integers
ALTER TABLE [dbo].[JobStepTimingEvent]
ALTER COLUMN [EventType] [int] NOT NULL

-- Add new constraints for integer enum values
-- TimingSessionState: PLAYING = 1, PAUSED = 2, FINISHED = 3
ALTER TABLE [dbo].[JobStepActiveSession]
ADD CONSTRAINT [CK_JobStepActiveSession_State] CHECK ([CurrentState] IN (1, 2, 3))

-- TimingEventType: PLAY = 1, PAUSE = 2, RESUME = 3, FINISH = 4
ALTER TABLE [dbo].[JobStepTimingEvent]
ADD CONSTRAINT [CK_JobStepTimingEvent_EventType] CHECK ([EventType] IN (1, 2, 3, 4))

PRINT 'Timing table constraints updated successfully!'
PRINT 'CurrentState values: 1=PLAYING, 2=PAUSED, 3=FINISHED'
PRINT 'EventType values: 1=PLAY, 2=PAUSE, 3=RESUME, 4=FINISH'
