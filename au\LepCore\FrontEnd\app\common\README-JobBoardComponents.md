# Job Board Components - Readonly Job Details Integration

This document explains the new components created for displaying readonly job details in job board popups with timing functionality.

## Components Created

### 1. Job Details Components

#### `lep-job-details-readonly` 
- **File**: `job-details-readonly.component.html/coffee`
- **Purpose**: Full readonly view of job details
- **Usage**: Complete job information display
- **Features**: 
  - Comprehensive job information layout
  - Size, material, printing, and finishing details
  - Special instructions and options
  - Date and user information

#### `lep-job-details-compact`
- **File**: `job-details-compact.component.html/coffee` 
- **Purpose**: Compact readonly view for popups
- **Usage**: Essential job info in limited space
- **Features**:
  - Key job information in grid layout
  - Special options as tags
  - Urgency indicators
  - Responsive design for popups

### 2. Job Board Popup System

#### `job-board-popup.html`
- **Purpose**: Main popup template for timing + job details
- **Features**:
  - Tabbed interface (Checkpoints, Job Details, Instructions)
  - Timing controls (Start, Pause, Resume, Finish)
  - Production instructions integration
  - Timer display

#### `JobBoardPopupController`
- **File**: `job-board-popup.controller.coffee`
- **Purpose**: Controls popup behavior and timing
- **Features**:
  - Loads job data from OrdersController API
  - Integrates with production instructions API
  - Manages timing state and events
  - Sends timing data to backend

#### `JobBoardPopupService`
- **File**: `job-board-popup.service.coffee`
- **Purpose**: Service to open popups from anywhere
- **Methods**:
  - `openJobBoardPopup(jobId, status, options)`
  - `openJobDetailsPopup(jobId, options)`

## Integration with Existing System

### Data Source
- Uses existing `OrdersController.GetJob(id)` endpoint
- Returns `JobViewCustDto` with complete job information
- No changes needed to existing backend API

### Production Instructions
- Integrates with new production instructions system
- Shows boss's checkpoints first (quick reference)
- Links to detailed instructions when available
- Uses `/api/production-instructions/{status}/both` endpoint

### Timing Integration
- Connects to timing system backend APIs
- Sends timing events (Start, Pause, Resume, Finish)
- Tracks elapsed time with pause support
- Stores timing data for reporting

## Usage Examples

### Basic Job Board Integration
```coffeescript
# In your job board controller
$scope.openJobTiming = (job, status) ->
    JobBoardPopupService.openJobBoardPopup(job.Id, status, {
        size: 'lg'
        onSuccess: (result) ->
            # Refresh job board after timing
            $scope.refreshJobBoard()
    })
```

### HTML Template Integration
```html
<!-- Add to job board template -->
<button type="button" 
        class="btn btn-primary btn-sm" 
        ng-click="openJobTiming(job, job.Status)">
    <i class="glyphicon glyphicon-play"></i>
    Start {{job.Status}}
</button>
```

### Readonly Job Details
```coffeescript
# Quick view without timing
$scope.viewJobDetails = (job) ->
    JobBoardPopupService.openJobDetailsPopup(job.Id)
```

## Component Properties

### lep-job-details-compact
```html
<lep-job-details-compact 
    job="jobData" 
    show-order-info="true">
</lep-job-details-compact>
```

### lep-job-details-readonly  
```html
<lep-job-details-readonly 
    job="jobData" 
    show-title="true" 
    compact="false">
</lep-job-details-readonly>
```

## Styling

### Responsive Design
- Components adapt to different screen sizes
- Popup optimized for tablet/desktop use
- Compact layout for smaller spaces

### Bootstrap Integration
- Uses existing Bootstrap classes
- Consistent with LEPCore styling
- Custom CSS for timing controls

### Status Indicators
- Color-coded status labels
- Urgency indicators for due dates
- Special instruction highlighting

## File Structure

```
au/LepCore/FrontEnd/app/common/
├── directives/
│   ├── job-details-readonly.component.html
│   ├── job-details-readonly.component.coffee
│   ├── job-details-compact.component.html
│   └── job-details-compact.component.coffee
├── templates/
│   └── job-board-popup.html
├── controllers/
│   └── job-board-popup.controller.coffee
├── services/
│   └── job-board-popup.service.coffee
└── examples/
    └── job-board-integration-example.coffee
```

## Next Steps

1. **Include in Build**: Add new files to your build process
2. **Test Components**: Test with real job data
3. **Integrate**: Add popup buttons to existing job boards
4. **Customize**: Adjust styling and layout as needed
5. **Train Users**: Show staff the new timing workflow

## Benefits

- **Unified Experience**: Job details + timing in one popup
- **Quick Reference**: Boss's checkpoints readily available
- **Detailed Guidance**: Full instructions when needed
- **Time Tracking**: Accurate production timing
- **No Navigation**: Stay on job board while working
- **Responsive**: Works on different devices
