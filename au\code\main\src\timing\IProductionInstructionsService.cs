using System.Collections.Generic;
using System.Threading.Tasks;
using lep.timing.dto;

namespace lep.timing
{
    public interface IProductionInstructionsService
    {
        Task<ProductionInstructionsDto> GetInstructionsForJobStep(int jobId, string status);
        Task<List<ProductionInstructionsDto>> GetInstructionsByStatus(string status);
        Task<ProductionInstructionsDto> CreateInstructions(ProductionInstructionsDto dto, int userId);
        Task<ProductionInstructionsDto> UpdateInstructions(ProductionInstructionsDto dto, int userId);
        Task DeleteInstructions(int id, int userId);
        Task<List<string>> GetAllProductionStatuses();
        Task<List<ProductionInstructionsDto>> GetInstructionsByJobType(int jobTypeId);
        Task<List<ProductionInstructionsDto>> GetAllInstructions();
        Task<ProductionInstructionsDto> GetInstructions(string status);
        Task<(ProductionInstructionsDto checkpoints, ProductionInstructionsDto detailed)> GetBothInstructionTypes(string status);
        void ClearCache();
    }
}
