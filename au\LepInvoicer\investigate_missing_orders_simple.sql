-- Simple investigation of missing orders
USE PRD_AU;
GO

-- Create temp table with order IDs
CREATE TABLE #OrderIds (OrderId INT);

INSERT INTO #OrderIds VALUES 
(1416721), (1416747), (1417065), (1420095), (1420096), (1420098), (1420092), (1419029), (1420117), (1419424), 
(1419774), (1419985), (1419914), (1419907), (1419194), (1420034), (1405289), (1419942), (1419807), (1419815), 
(1419504), (1419541), (1419543), (1419544), (1404589), (1411056), (1419863), (1419859), (1418591), (1418647), 
(1420062), (1419021), (1420094), (1419420), (1419830), (1420093), (1419989), (1419986), (1418227), (1418873), 
(1420031), (1420009), (1419028), (1418692), (1418689), (1419173), (1420006), (1419961), (1419801), (1415818), 
(1419838), (1419975);

-- Summary by reason
SELECT 
    CASE 
        WHEN c.Name IN ('LEP Colour Printers Pty Ltd', 'LEP Marketing', 'LEP TEST J', 'LEP TEST T', 'lepdemo') THEN 'IGNORED_CUSTOMER'
        WHEN o.Invoiced2 IN ('Y', 'F', 'C') THEN 'ALREADY_INVOICED'
        WHEN o.FinishDate IS NULL THEN 'NO_FINISH_DATE'
        WHEN YEAR(o.FinishDate) = 1 THEN 'INVALID_FINISH_DATE'
        WHEN o.FinishDate < '2024-02-01' THEN 'BEFORE_MIN_DATE'
        WHEN o.Price IS NULL OR o.Price = 0 THEN 'ZERO_PRICE'
        ELSE 'SHOULD_BE_SELECTED'
    END as Reason,
    COUNT(*) as OrderCount
FROM [Order] o
    INNER JOIN Customer c ON o.userId = c.Id
    INNER JOIN #OrderIds oi ON o.Id = oi.OrderId
GROUP BY 
    CASE 
        WHEN c.Name IN ('LEP Colour Printers Pty Ltd', 'LEP Marketing', 'LEP TEST J', 'LEP TEST T', 'lepdemo') THEN 'IGNORED_CUSTOMER'
        WHEN o.Invoiced2 IN ('Y', 'F', 'C') THEN 'ALREADY_INVOICED'
        WHEN o.FinishDate IS NULL THEN 'NO_FINISH_DATE'
        WHEN YEAR(o.FinishDate) = 1 THEN 'INVALID_FINISH_DATE'
        WHEN o.FinishDate < '2024-02-01' THEN 'BEFORE_MIN_DATE'
        WHEN o.Price IS NULL OR o.Price = 0 THEN 'ZERO_PRICE'
        ELSE 'SHOULD_BE_SELECTED'
    END
ORDER BY OrderCount DESC;

-- Show some examples of each category
PRINT 'Examples of ZERO_PRICE orders:';
SELECT TOP 5 o.Id, o.Price, c.Name, 
    (SELECT SUM(ISNULL(TRY_CAST(j.Price as decimal(18,2)), 0)) FROM Job j WHERE j.OrderId = o.Id) as JobsTotal
FROM [Order] o
    INNER JOIN Customer c ON o.userId = c.Id
    INNER JOIN #OrderIds oi ON o.Id = oi.OrderId
WHERE (o.Price IS NULL OR o.Price = 0)
    AND c.Name NOT IN ('LEP Colour Printers Pty Ltd', 'LEP Marketing', 'LEP TEST J', 'LEP TEST T', 'lepdemo')
    AND o.Invoiced2 NOT IN ('Y', 'F', 'C')
    AND o.FinishDate IS NOT NULL
    AND YEAR(o.FinishDate) != 1
    AND o.FinishDate >= '2024-02-01';

PRINT 'Examples of ALREADY_INVOICED orders:';
SELECT TOP 5 o.Id, o.Invoiced2, c.Name, 
    (SELECT COUNT(*) FROM Invoicer2Log il WHERE il.OrderId = o.Id) as LogEntries
FROM [Order] o
    INNER JOIN Customer c ON o.userId = c.Id
    INNER JOIN #OrderIds oi ON o.Id = oi.OrderId
WHERE o.Invoiced2 IN ('Y', 'F', 'C');

DROP TABLE #OrderIds;
