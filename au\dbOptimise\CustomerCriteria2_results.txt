Changed database context to 'PRD_AU'.
=== CREATING INDEXES FOR OPTIMIZED CUSTOMERCRITERIA2 ===
Database: PRD_AU
Target: CustomerCriteria2 query optimization
Date: 2025-06-08 00:02:38
 
1. CREATING PRIMARY CUSTOMER SEARCH INDEXES...
Creating Customer name/username prefix search index...
ERROR: Column name '<PERSON><PERSON><PERSON>' does not exist in the target table or view.
Creating Customer filtering index...
ERROR: Column name 'IsEnabled' does not exist in the target table or view.
Creating Customer postcode index...
SUCCESS: IX_Customer_BillingPostcode created
  - Optimizes: Like("cust.BillingAddress.Postcode", PostalPostCode, MatchMode.Start)
 
2. CREATING SUPPORTING TABLE INDEXES...
Creating SalesRegion LEP_Region index...
SUCCESS: IX_SalesRegion_LEP_Region created
  - Optimizes: RegionLep subquery filtering
Creating CustomerNote search index...
ERROR: Cannot find the object "dbo.CustomerNote" because it does not exist or you do not have permissions.
Creating Order Customer relationship index...
ERROR: Column name 'CustomerId' does not exist in the target table or view.
Creating Job Order relationship index...
ERROR: CREATE INDEX failed because the following SET options have incorrect settings: 'QUOTED_IDENTIFIER'. Verify that SET options are correct for use with indexed views and/or indexes on computed columns and/or filtered indexes and/or query notifications and/or XML data type methods and/or spatial index operations.
 
3. FULL-TEXT SEARCH RECOMMENDATIONS...
For ContactsJsonStr searches, consider implementing:
- Full-Text Search on ContactsJsonStr column
- JSON indexing (SQL Server 2016+)
- Separate contact search table for better performance
 
4. UPDATING STATISTICS...
Customer statistics updated
SalesRegion statistics updated
Msg 2706, Level 16, State 6, Server SRV03, Line 188
Table 'CustomerNote' does not exist.
