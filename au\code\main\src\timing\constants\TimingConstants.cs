namespace lep.timing.constants
{
    /// <summary>
    /// Constants for timing system to ensure consistency across database and application
    /// </summary>
    public static class TimingConstants
    {
        /// <summary>
        /// Timing session states stored in database
        /// </summary>
        public static class SessionStates
        {
            public const string READY = "READY";
            public const string PLAYING = "PLAYING";
            public const string PAUSED = "PAUSED";
            public const string FINISHED = "FINISHED";
        }

        /// <summary>
        /// Timing event types stored in database
        /// </summary>
        public static class EventTypes
        {
            public const string START = "START";
            public const string PAUSE = "PAUSE";
            public const string RESUME = "RESUME";
            public const string FINISH = "FINISH";
        }

        /// <summary>
        /// Frontend timing state constants (for compatibility with existing frontend code)
        /// Maps to numeric values used in frontend
        /// </summary>
        public static class FrontendStates
        {
            public const int READY = 0;
            public const int PLAYING = 1;
            public const int PAUSED = 2;
            public const int FINISHED = 3;
        }

        /// <summary>
        /// Convert database string state to frontend numeric state
        /// </summary>
        public static int ToFrontendState(string dbState)
        {
            return dbState switch
            {
                SessionStates.READY => FrontendStates.READY,
                SessionStates.PLAYING => FrontendStates.PLAYING,
                SessionStates.PAUSED => FrontendStates.PAUSED,
                SessionStates.FINISHED => FrontendStates.FINISHED,
                _ => FrontendStates.READY
            };
        }

        /// <summary>
        /// Convert frontend numeric state to database string state
        /// </summary>
        public static string ToDbState(int frontendState)
        {
            return frontendState switch
            {
                FrontendStates.READY => SessionStates.READY,
                FrontendStates.PLAYING => SessionStates.PLAYING,
                FrontendStates.PAUSED => SessionStates.PAUSED,
                FrontendStates.FINISHED => SessionStates.FINISHED,
                _ => SessionStates.READY
            };
        }
    }
}
