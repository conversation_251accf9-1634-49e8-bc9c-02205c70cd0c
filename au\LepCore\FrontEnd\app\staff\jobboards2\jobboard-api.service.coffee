appStaff = angular.module('app.staff')

appStaff.service 'JobBoardApiService', [
    '$http', '$q', '$rootScope', 'lepApi2'
    ($http, $q, $rootScope, lepApi2) ->

        @getJobs = (facilityStr, board) ->
            params =
                facilityStr: facilityStr || 'FG'
                board: board || 'All'

            $http.get('/api/jobboard/GetJson', { params: params })
                .then (response) ->
                    # Emit event for service-agnostic handling
                    $rootScope.$broadcast('service:getJobsResult', response.data)
                .catch (error) ->
                    $rootScope.$broadcast('service:getJobsError', error)
                    throw error

        @getVersion = () ->
            $http.get('/api/jobboard/Version')
                .then (response) ->
                    # Emit event for service-agnostic handling
                    $rootScope.$broadcast('service:getVersionResult', response.data)

        # Timing API methods (HTTP only) - emit events for service-agnostic handling
        @startTiming = (jobId, status, notes, instructionsViewed, qualityChecksPassed) ->
            request =
                JobId: jobId
                Status: status
                Notes: notes || null
                InstructionsViewed: instructionsViewed || false
                QualityChecksPassed: qualityChecksPassed || false
                WorkstationId: @getWorkstationId()

            console.log("🌐 API SERVICE: START TIMING REQUEST:", request)

            $http.post('/api/jobtiming/play', request)
                .then (response) ->
                    console.log("🌐 API SERVICE: START TIMING RESPONSE:", response.data)
                    # Emit event for service-agnostic handling (same as SignalR service)
                    $rootScope.$broadcast('timing:actionResult', response.data)
                .catch (error) ->
                    console.error("🌐 API SERVICE: START TIMING ERROR:", error)
                    # Emit error event for service-agnostic handling
                    $rootScope.$broadcast('timing:actionError', error.data || error)
                    throw error

        @pauseTiming = (jobId, status, notes) ->
            request =
                JobId: jobId
                Status: status
                Notes: notes || null
                WorkstationId: @getWorkstationId()

            $http.post('/api/jobtiming/pause', request)
                .then (response) ->
                    # Emit event for service-agnostic handling
                    $rootScope.$broadcast('timing:actionResult', response.data)
                .catch (error) ->
                    $rootScope.$broadcast('timing:actionError', error.data || error)
                    throw error

        @resumeTiming = (jobId, status, notes) ->
            request =
                JobId: jobId
                Status: status
                Notes: notes || null
                WorkstationId: @getWorkstationId()

            $http.post('/api/jobtiming/resume', request)
                .then (response) ->
                    # Emit event for service-agnostic handling
                    $rootScope.$broadcast('timing:actionResult', response.data)
                .catch (error) ->
                    $rootScope.$broadcast('timing:actionError', error.data || error)
                    throw error

        @finishTiming = (jobId, status, notes) ->
            request =
                JobId: jobId
                Status: status
                Notes: notes || null
                WorkstationId: @getWorkstationId()

            $http.post('/api/jobtiming/finish', request)
                .then (response) ->
                    # Emit event for service-agnostic handling
                    $rootScope.$broadcast('timing:actionResult', response.data)
                .catch (error) ->
                    $rootScope.$broadcast('timing:actionError', error.data || error)
                    throw error

        @getTimingState = (jobId, status) ->
            console.log("🌐 API SERVICE: GET TIMING STATE REQUEST - JobId: #{jobId}, Status: #{status}")

            $http.get("/api/jobtiming/state/#{jobId}/#{encodeURIComponent(status)}")
                .then (response) ->
                    console.log("🌐 API SERVICE: GET TIMING STATE RESPONSE:", response.data)
                    # Emit event for service-agnostic handling
                    $rootScope.$broadcast('service:timingStateResult', response.data)
                    # Still return data for error handling usage
                    response.data
                .catch (error) ->
                    console.error("🌐 API SERVICE: GET TIMING STATE ERROR:", error)
                    throw error

        @stopAllSessions = () ->
            $http.post('/api/jobtiming/stop-all')
                .then (response) ->
                    # Emit event if needed (currently no event listener for this)
                    console.log("🌐 API SERVICE: STOP ALL SESSIONS RESPONSE:", response.data)

        @getProductionInstructions = (status) ->
            $http.get("/api/production-instructions/#{encodeURIComponent(status)}")
                .then (response) ->
                    # Emit event for service-agnostic handling
                    $rootScope.$broadcast('service:productionInstructionsResult', response.data)
                    # Still return data for dialog usage
                    response.data
                .catch (error) ->
                    console.warn('Production instructions not available:', error)
                    null

        @getWorkstationId = () ->
            workstationId = localStorage.getItem('workstationId')
            if !workstationId
                workstationId = "WS-#{Date.now()}-#{Math.random().toString(36).substr(2, 9)}"
                localStorage.setItem('workstationId', workstationId)
            workstationId

        @
]
