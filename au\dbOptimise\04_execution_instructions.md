# Database Optimization Execution Instructions

## Overview
This document provides step-by-step instructions for executing the database optimization scripts for the PRD_AU database on SRV03.

## Prerequisites
- SQL Server Management Studio (SSMS) or sqlcmd access
- Appropriate permissions on the target database (db_ddl<PERSON>min, db_datawriter, db_datareader minimum)
- Backup of the database (HIGHLY RECOMMENDED before any optimization)
- Non-production environment for initial testing

## Connection Details
- **Server**: SRV03
- **Database**: PRD_AU
- **Connection String**: `Data Source=srv03; user id=sa; password=*************; Initial Catalog=PRD_AU;MultipleActiveResultSets=true;Max Pool Size=200;App=LepCore`

## Execution Order

### Phase 1: Analysis (Read-Only)
Execute this script first to understand current database performance issues.

**Script**: `01_database_analysis.sql`
**Purpose**: Analyze database performance and identify optimization opportunities
**Risk Level**: LOW (Read-only operations)

#### Using SSMS:
1. Open SQL Server Management Studio
2. Connect to SRV03
3. Open `01_database_analysis.sql`
4. Ensure you're connected to the PRD_AU database
5. Execute the script (F5)
6. Review the results to understand what optimizations will be performed

#### Using sqlcmd:
```cmd
sqlcmd -S srv03 -U sa -P "*************" -d PRD_AU -i "C:\LepSF\au\dbOptimise\01_database_analysis.sql" -o "C:\LepSF\au\dbOptimise\analysis_results.txt"
```

### Phase 2: Optimization Implementation
Execute this script to implement the actual optimizations.

**Script**: `02_index_optimization.sql`
**Purpose**: Create missing indexes, rebuild fragmented indexes, update statistics
**Risk Level**: MEDIUM (DDL operations, can impact performance during execution)

#### Pre-execution Checklist:
- [ ] Database backup completed
- [ ] Analysis results reviewed
- [ ] Maintenance window scheduled (recommended)
- [ ] Rollback plan understood

#### Using SSMS:
1. Open `02_index_optimization.sql`
2. Review the script contents
3. Execute during a maintenance window
4. Monitor execution progress
5. Review success/error messages

#### Using sqlcmd:
```cmd
sqlcmd -S srv03 -U sa -P "*************" -d PRD_AU -i "C:\LepSF\au\dbOptimise\02_index_optimization.sql" -o "C:\LepSF\au\dbOptimise\optimization_results.txt"
```

### Phase 3: Rollback (If Needed)
Execute this script only if you need to rollback the optimizations.

**Script**: `03_rollback_optimization.sql`
**Purpose**: Remove optimization indexes and restore original state
**Risk Level**: MEDIUM (DDL operations)

#### Using SSMS:
1. Open `03_rollback_optimization.sql`
2. Uncomment the rollback sections you want to execute
3. Execute carefully, section by section
4. Verify rollback completion

#### Using sqlcmd:
```cmd
sqlcmd -S srv03 -U sa -P "*************" -d PRD_AU -i "C:\LepSF\au\dbOptimise\03_rollback_optimization.sql" -o "C:\LepSF\au\dbOptimise\rollback_results.txt"
```

## Automated Execution

### Batch File Execution
Use the provided batch file for automated execution:

```cmd
cd C:\LepSF\au\dbOptimise
execute_optimization.bat
```

### PowerShell Execution
```powershell
# Set execution policy if needed
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser

# Execute optimization
.\execute_optimization.ps1
```

## Monitoring and Verification

### During Execution
- Monitor SQL Server Activity Monitor
- Watch for blocking processes
- Check error logs
- Monitor disk space usage

### Post-Execution Verification
1. **Check for errors**: Review output files for any error messages
2. **Verify index creation**: 
   ```sql
   SELECT name, create_date FROM sys.indexes 
   WHERE create_date >= DATEADD(hour, -1, GETDATE())
   ORDER BY create_date DESC;
   ```
3. **Monitor query performance**: Compare execution plans before/after
4. **Check fragmentation levels**:
   ```sql
   SELECT 
       OBJECT_NAME(object_id) AS table_name,
       index_id,
       avg_fragmentation_in_percent
   FROM sys.dm_db_index_physical_stats(DB_ID(), NULL, NULL, NULL, 'LIMITED')
   WHERE avg_fragmentation_in_percent > 10;
   ```

## Expected Results

### Performance Improvements
- **Query Response Time**: 20-50% improvement for queries with missing indexes
- **I/O Reduction**: 30-70% reduction in logical reads for optimized queries
- **CPU Usage**: 10-30% reduction in CPU time for complex queries

### Index Statistics
- **Missing Indexes**: Creation of 5-15 high-impact indexes
- **Fragmentation**: Reduction from >30% to <5% for rebuilt indexes
- **Statistics**: Updated statistics for all optimized tables

## Troubleshooting

### Common Issues
1. **Insufficient Permissions**: Ensure account has db_ddladmin rights
2. **Lock Timeouts**: Execute during low-activity periods
3. **Disk Space**: Ensure adequate space for index operations (2x table size)
4. **Memory Issues**: Monitor tempdb usage during operations

### Error Resolution
- **Index Already Exists**: Script includes existence checks, but manual cleanup may be needed
- **Online Operations Not Supported**: Some operations may need to run offline
- **Statistics Update Failures**: Usually safe to ignore, can be run separately

## Best Practices

### Before Execution
1. **Full database backup**
2. **Test in development environment**
3. **Schedule during maintenance window**
4. **Notify application teams**

### During Execution
1. **Monitor system resources**
2. **Check for blocking**
3. **Watch error logs**
4. **Be prepared to cancel if needed**

### After Execution
1. **Verify all operations completed successfully**
2. **Monitor application performance**
3. **Update documentation**
4. **Schedule regular maintenance**

## Support and Escalation

### If Issues Occur
1. **Stop execution** if errors are encountered
2. **Review error messages** in output files
3. **Check SQL Server error logs**
4. **Consider rollback** if performance degrades
5. **Contact DBA team** for complex issues

### Performance Monitoring
- Monitor for 24-48 hours after optimization
- Compare key performance metrics
- Adjust or rollback if necessary
- Document lessons learned

## File Locations
- **Scripts**: `C:\LepSF\au\dbOptimise\`
- **Output Logs**: `C:\LepSF\au\dbOptimise\logs\`
- **Backup Location**: Ensure backups are stored safely before execution
