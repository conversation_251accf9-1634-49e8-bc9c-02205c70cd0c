<div class="job-details-compact">
    <!-- Job Header -->
    <div class="job-header clearfix">
        <div class="pull-left">
            <h4 class="job-title">
                <i class="glyphicon glyphicon-file"></i>
                {{job.Name}}
                <small class="text-muted">#{{job.Id}}</small>
            </h4>
            <span class="label label-{{job.StatusCss || 'default'}}">{{job.StatusC || job.Status}}</span>
        </div>
        <div class="pull-right">
            <span class="job-quantity">{{job.Quantity | number}} units</span>
            <br>
            <small class="text-muted">{{job.Template.Name}}</small>
        </div>
    </div>

    <!-- Key Information Grid -->
    <div class="job-info-grid">
        <div class="row">
            <!-- Left Column -->
            <div class="col-xs-6">
                <div class="info-item" ng-if="job.FinishedSize">
                    <label>Size:</label>
                    <span>{{formatSize(job.FinishedSize)}}</span>
                </div>
                
                <div class="info-item" ng-if="job.Stock">
                    <label>Stock:</label>
                    <span>{{job.Stock.Name}}</span>
                    <span ng-if="job.StockOverride && job.StockOverride.Name" class="text-warning">
                        (Override: {{job.StockOverride.Name}})
                    </span>
                </div>
                
                <div class="info-item" ng-if="job.PrintType">
                    <label>Print:</label>
                    <span>{{getPrintTypeDisplay(job.PrintType)}}</span>
                </div>
                
                <div class="info-item" ng-if="job.FrontCelloglaze || job.BackCelloglaze">
                    <label>Cello:</label>
                    <span>{{getCelloDisplay(job.FrontCelloglaze, job.BackCelloglaze)}}</span>
                </div>
            </div>
            
            <!-- Right Column -->
            <div class="col-xs-6">
                <div class="info-item" ng-if="job.FoldedSize && job.FoldedSize.PaperSize.Id">
                    <label>Folded:</label>
                    <span>{{formatSize(job.FoldedSize)}}</span>
                </div>
                
                <div class="info-item" ng-if="job.BindingOption && job.BindingOption.Name">
                    <label>Binding:</label>
                    <span>{{job.BindingOption.Name}}</span>
                </div>
                
                <div class="info-item" ng-if="job.Pages > 1">
                    <label>Pages:</label>
                    <span>{{job.Pages}}</span>
                </div>
                
                <div class="info-item" ng-if="job.PrintByDate">
                    <label>Due:</label>
                    <span class="text-warning">{{job.PrintByDate | date:'MMM d, h:mm a'}}</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Special Options (if any) -->
    <div class="special-options" ng-if="hasSpecialOptions()">
        <label class="options-label">Options:</label>
        <div class="options-list">
            <span ng-if="job.RoundOption" class="option-tag">
                <i class="glyphicon glyphicon-certificate"></i>
                {{$root.enums.ValueDesc.RoundOption[job.RoundOption]}}
            </span>
            <span ng-if="job.HoleDrilling" class="option-tag">
                <i class="glyphicon glyphicon-record"></i>
                {{job.NumberOfHoles}} holes
            </span>
            <span ng-if="job.Perforating" class="option-tag">
                <i class="glyphicon glyphicon-minus"></i>
                Perforating
            </span>
            <span ng-if="job.Scoring" class="option-tag">
                <i class="glyphicon glyphicon-resize-horizontal"></i>
                Scoring
            </span>
            <span ng-if="job.NumberOfMagnets" class="option-tag">
                <i class="glyphicon glyphicon-magnet"></i>
                {{job.NumberOfMagnets}} magnets
            </span>
            <span ng-if="job.FoilColour" class="option-tag">
                <i class="glyphicon glyphicon-tint"></i>
                {{job.FoilColour}} foil
            </span>
        </div>
    </div>

    <!-- Special Instructions (if any) -->
    <div class="special-instructions" ng-if="job.SpecialInstructions">
        <label class="instructions-label">
            <i class="glyphicon glyphicon-comment"></i>
            Special Instructions:
        </label>
        <div class="instructions-text">{{job.SpecialInstructions}}</div>
    </div>

    <!-- Order Information -->
    <div class="order-info" ng-if="job.OrderId">
        <small class="text-muted">
            Order #{{job.OrderId}}
            <span ng-if="job.DateCreated"> • Created {{job.DateCreated | date:'MMM d, yyyy'}}</span>
            <span ng-if="job.CreatedBy"> • by {{job.CreatedBy.Name}}</span>
        </small>
    </div>
</div>

<style>
.job-details-compact {
    font-size: 13px;
    line-height: 1.4;
}

.job-header {
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.job-title {
    margin: 0 0 5px 0;
    font-size: 16px;
    font-weight: 600;
}

.job-quantity {
    font-weight: 600;
    color: #337ab7;
}

.job-info-grid {
    margin-bottom: 15px;
}

.info-item {
    margin-bottom: 8px;
    display: flex;
    align-items: flex-start;
}

.info-item label {
    font-weight: 600;
    min-width: 50px;
    margin-right: 8px;
    margin-bottom: 0;
    color: #666;
    font-size: 12px;
}

.info-item span {
    flex: 1;
    word-break: break-word;
}

.special-options {
    margin-bottom: 15px;
    padding: 10px;
    background-color: #f8f9fa;
    border-radius: 4px;
}

.options-label {
    font-weight: 600;
    color: #666;
    font-size: 12px;
    display: block;
    margin-bottom: 5px;
}

.options-list {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
}

.option-tag {
    display: inline-block;
    padding: 2px 6px;
    background-color: #e9ecef;
    border-radius: 3px;
    font-size: 11px;
    color: #495057;
    white-space: nowrap;
}

.option-tag i {
    margin-right: 3px;
    font-size: 10px;
}

.special-instructions {
    margin-bottom: 15px;
    padding: 10px;
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 4px;
}

.instructions-label {
    font-weight: 600;
    color: #856404;
    font-size: 12px;
    display: block;
    margin-bottom: 5px;
}

.instructions-text {
    color: #856404;
    font-size: 12px;
    white-space: pre-wrap;
    word-break: break-word;
}

.order-info {
    padding-top: 10px;
    border-top: 1px solid #eee;
}
</style>
