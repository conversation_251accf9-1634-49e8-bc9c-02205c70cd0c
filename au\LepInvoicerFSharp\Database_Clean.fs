namespace LepInvoicerFSharp

open System
open LepInvoicerFSharp

// ============================================================================
// DATABASE MODULE - Functional database operations (Mock implementation)
// ============================================================================

module Database =
    
    /// Mock database operations for development
    /// In production, these would use F# Type Providers or other database access
    
    /// Get orders to invoice with functional error handling
    let getOrdersToInvoice (config: InvoicerConfig) (batchSize: int) : AsyncResult<(int * string) list, string> =
        async {
            try
                // Mock data - in real implementation would query database
                let mockOrders = [
                    (1, "customer1")
                    (2, "customer2")
                    (3, "customer3")
                ]
                
                // Filter and take only what we need
                let validOrders = 
                    mockOrders
                    |> List.filter (fun (_, username) -> not (List.contains username config.IgnoreCustomers))
                    |> List.take (min batchSize (List.length mockOrders))
                
                return Ok validOrders
            with
            | ex -> return Error $"Failed to get orders to invoice: {ex.Message}"
        }
    
    /// Get credits to invoice with functional error handling
    let getCreditsToInvoice (config: InvoicerConfig) (batchSize: int) : AsyncResult<OrderCredit list, string> =
        async {
            try
                // Mock data - in real implementation would query database
                let mockCredits = [
                    {
                        Id = 1
                        OrderId = 1
                        CustomerId = 1
                        CreditType = Credit
                        Amount = 50.0m
                        Description = "Test credit"
                        IsInvoiced = false
                        DateCreated = DateTime.Now
                    }
                ]
                
                let credits = 
                    mockCredits
                    |> List.filter (CreditLogic.isReadyForInvoicing config)
                    |> List.take (min batchSize (List.length mockCredits))
                
                return Ok credits
            with
            | ex -> return Error $"Failed to get credits to invoice: {ex.Message}"
        }
    
    /// Get refunds to invoice with functional error handling
    let getRefundsToInvoice (config: InvoicerConfig) (batchSize: int) : AsyncResult<OrderCredit list, string> =
        async {
            try
                // Mock data - in real implementation would query database
                let mockRefunds = [
                    {
                        Id = 2
                        OrderId = 2
                        CustomerId = 2
                        CreditType = Refund
                        Amount = 25.0m
                        Description = "Test refund"
                        IsInvoiced = false
                        DateCreated = DateTime.Now
                    }
                ]
                
                let refunds = 
                    mockRefunds
                    |> List.filter (CreditLogic.isReadyForInvoicing config)
                    |> List.take (min batchSize (List.length mockRefunds))
                
                return Ok refunds
            with
            | ex -> return Error $"Failed to get refunds to invoice: {ex.Message}"
        }
    
    /// Mark order as invoiced
    let markOrderInvoiced (orderId: int) : AsyncResult<unit, string> =
        async {
            try
                // Mock operation - in real implementation would update database
                printfn $"Marking order {orderId} as invoiced"
                do! Async.Sleep(10) // Simulate database operation
                return Ok ()
            with
            | ex -> return Error $"Failed to mark order {orderId} as invoiced: {ex.Message}"
        }
    
    /// Mark order as failed
    let markOrderFailed (orderId: int) (errorMessage: string) : AsyncResult<unit, string> =
        async {
            try
                // Mock operation - in real implementation would update database
                printfn $"Marking order {orderId} as failed: {errorMessage}"
                do! Async.Sleep(10) // Simulate database operation
                return Ok ()
            with
            | ex -> return Error $"Failed to mark order {orderId} as failed: {ex.Message}"
        }
    
    /// Mark credit as invoiced
    let markCreditInvoiced (creditId: int) : AsyncResult<unit, string> =
        async {
            try
                // Mock operation - in real implementation would update database
                printfn $"Marking credit {creditId} as invoiced"
                do! Async.Sleep(10) // Simulate database operation
                return Ok ()
            with
            | ex -> return Error $"Failed to mark credit {creditId} as invoiced: {ex.Message}"
        }
    
    /// Check for pending work (functional composition)
    let checkForPendingWork (config: InvoicerConfig) : AsyncResult<bool * string, string> =
        async {
            let! ordersResult = getOrdersToInvoice config config.InvoiceBatchSize
            let! creditsResult = getCreditsToInvoice config config.RefundBatchSize  
            let! refundsResult = getRefundsToInvoice config config.RefundBatchSize
            
            match ordersResult, creditsResult, refundsResult with
            | Ok orders, Ok credits, Ok refunds ->
                let totalWork = List.length orders + List.length credits + List.length refunds
                let hasPendingWork = totalWork > 0
                
                let workDescription = 
                    [
                        if not (List.isEmpty orders) then $"{List.length orders} orders"
                        if not (List.isEmpty credits) then $"{List.length credits} credits"  
                        if not (List.isEmpty refunds) then $"{List.length refunds} refunds"
                    ]
                    |> String.concat ", "
                    |> fun desc -> if String.IsNullOrEmpty(desc) then "No work" else desc
                
                return Ok (hasPendingWork, workDescription)
            | Error error, _, _ -> return Error error
            | _, Error error, _ -> return Error error
            | _, _, Error error -> return Error error
        }
