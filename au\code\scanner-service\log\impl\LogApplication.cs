namespace lep.log.impl
{
	using Common.Logging;
	using lep.config;
	using System;
	using Serilog;
	public class LogApplication : ILoggerFactoryAdapter, ILog
	{
		private IConfigApplication configApp;

		private bool isInfoEnabled;
		private bool isEmailEnabled;
		private string emailHost;
		private string emailTo;

		private log4net.ILog log;
		private log4net.ILog email;
		private log4net.Appender.SmtpAppender emailAppender;

		public void Start()
		{
			configApp.OnChange += new ConfigHandler(OnConfigurationChange);
			if (configApp != null)
			{
				OnConfigurationChange();
			}

			LogManager.Adapter = this;

			// configure log4net via app.config
			log4net.Config.XmlConfigurator.Configure();

			log = log4net.LogManager.GetLogger("lep.file");
			email = log4net.LogManager.GetLogger("lep.email");
			emailAppender = (email.Logger as log4net.Core.IAppenderAttachable).GetAppender("SmtpAppender") as log4net.Appender.SmtpAppender;
		}

		public void OnConfigurationChange()
		{
			string logLevel = configApp.GetLogLevel();
			isInfoEnabled = logLevel == "full";

			emailTo = configApp.GetEmailTo();
			emailHost = configApp.GetEmailHost();

			isEmailEnabled = (emailAppender != null) && !(string.IsNullOrEmpty(emailTo) || string.IsNullOrEmpty(emailHost));
			if (isEmailEnabled)
			{
				emailAppender.To = emailTo;
				emailAppender.From = emailTo;
				emailAppender.SmtpHost = emailHost;
			}
		}

		#region ILoggerFactoryAdapter magic

		public ILog GetLogger(Type type)
		{
			return this;
		}

		public ILog GetLogger(string name)
		{
			return this;
		}

		#endregion ILoggerFactoryAdapter magic

		#region ILog magic

		public void Debug(object message)
		{
		}

		public void Debug(object message, Exception exception)
		{
		}

		public void Trace(object message)
		{
		}

		public void Trace(object message, Exception exception)
		{
		}

		public void Error(object message)
		{
			Log.Error(message.ToString());
			if (isEmailEnabled)
			{
				email.Error(message);
			}
		}

		public void Error(object message, Exception exception)
		{
			Log.Error(message.ToString(), exception);
			if (isEmailEnabled)
			{
				email.Error(message, exception);
			}
		}

		public void Fatal(object message)
		{
			log.Fatal(message);
			if (isEmailEnabled)
			{
				email.Fatal(message);
			}
		}

		public void Fatal(object message, Exception exception)
		{
			log.Fatal(message, exception);
			if (isEmailEnabled)
			{
				email.Fatal(message, exception);
			}
		}

		public void Info(object message)
		{
			if (isInfoEnabled)
			{
				Log.Information(message.ToString());
			}
		}

		public void Info(object message, Exception exception)
		{
			if (isInfoEnabled)
			{
				Log.Information(message.ToString(), exception);
			}
		}

		public void Warn(object message)
		{
			if (isInfoEnabled)
			{
				log.Warn(message);
				if (isEmailEnabled)
				{
					email.Warn(message);
				}
			}
		}

		public void Warn(object message, Exception exception)
		{
			if (isInfoEnabled)
			{
				log.Warn(message, exception);
				if (isEmailEnabled)
				{
					email.Warn(message, exception);
				}
			}
		}

		public bool IsDebugEnabled
		{
			get { return false; }
		}

		public bool IsTraceEnabled
		{
			get { return false; }
		}

		public bool IsErrorEnabled
		{
			get { return true; }
		}

		public bool IsFatalEnabled
		{
			get { return true; }
		}

		public bool IsInfoEnabled
		{
			get { return isInfoEnabled; }
		}

		public bool IsWarnEnabled
		{
			get { return isInfoEnabled; }
		}

		#endregion ILog magic

		public IConfigApplication ConfigApplication
		{
			set { configApp = value; }
		}
	}
}
