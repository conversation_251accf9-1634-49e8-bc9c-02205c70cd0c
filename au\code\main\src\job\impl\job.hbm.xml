<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2"
					 namespace="lep.job"
					 assembly="lep"
					 auto-import="true"
					 default-cascade="all">

    <class name="IStock" table="Stock" discriminator-value="null">
        <cache usage="read-write" />
        <id name="Id" type="Int32" unsaved-value="0">
            <generator class="identity" />
        </id>
        <discriminator column="Id" type="Int32" insert="false" />
        <timestamp name="DateModified" column="DateModified" />
        <property name="Name" length="50" not-null="true" />
        <property name="SType" column="Type" length="255" not-null="false" />
        <property name="DateCreated" column="DateCreated" type="lumen.hibernate.type.DateTimeType, lumen" not-null="false" update="false" insert="false" />
        <property name="GSM" column="GSM" type="Int32" />
        <property name="FG_Production" type="YesNo" not-null="true" />
        <property name="PM_Production" type="YesNo" not-null="true" />
        <property name="Thickness" type="Decimal" not-null="false" />
        <property name="IsCover" column="Cover" type="YesNo" not-null="true" />
        <subclass name="lep.job.impl.Stock, lep" proxy="IStock" discriminator-value="not null" />
    </class>

    <class name="IBindingOption" table="BindingOption" discriminator-value="null" lazy="true">
        <cache usage="read-write" />
        <id name="Id" type="Int32" unsaved-value="0">
            <generator class="identity" />
        </id>
        <discriminator column="Id" type="Int32" insert="false" />
        <property name="Name" length="50" not-null="true" />
        <property name="NumberFiles" type="Int32" not-null="false" />
        <property name="FixedValue" type="Int32" not-null="false" />
        <property name="PerThousandValue" type="Int32" not-null="false" />
        <property name="PerSectionValue" type="Int32" not-null="false" />
        <subclass name="lep.job.impl.BindingOption, lep" proxy="IBindingOption" discriminator-value="not null" />
    </class>

    <class name="IBindingOptionLookup" table="BindingOptionLookup" discriminator-value="null">
        <cache usage="read-write" />
        <id name="Id" type="Int32" unsaved-value="0">
            <generator class="identity" />
        </id>
        <discriminator column="Id" type="Int32" insert="false" />
        <property name="JobOptionId" type="Int32" not-null="false" />
        <property name="StockId" type="Int32" not-null="false" />
        <property name="PaperSizeId" type="Int32" not-null="false" />
        <property name="MinTextPP" type="Int32" not-null="false" />
        <property name="MaxTextPP" type="Int32" not-null="false" />
        <property name="PrintType" type="lep.GenericEnum`1[lep.job.PrintType], lep" not-null="false" />
        <many-to-one name="BindingOptionId" class="IBindingOption" not-null="true" cascade="none" />

        <subclass name="lep.job.impl.BindingOptionLookup, lep" proxy="IBindingOptionLookup" discriminator-value="not null" />
    </class>

    <class name="IPostCode" table="PostCode" discriminator-value="null">
        <cache usage="read-write" />
        <id name="Id" type="Int32" unsaved-value="0">
            <generator class="identity" />
        </id>
        <discriminator column="Id" type="Int32" insert="false" />
        <property name="Code" length="4" not-null="true" />
        <property name="Facility" column="Facility" type="lep.GenericEnum`1[lep.job.Facility], lep" />
        <subclass name="lep.job.impl.PostCode, lep" proxy="IPostCode" discriminator-value="not null" />
    </class>

    <class name="IPaperSize" table="PaperSize" discriminator-value="null">
        <cache usage="read-write" />
        <id name="Id" type="Int32" unsaved-value="0">
            <generator class="identity" />
        </id>
        <discriminator column="Id" type="Int32" insert="false" />
        <timestamp name="DateModified" column="DateModified" />
        <property name="Name" length="50" not-null="true" />
        <component name="Size" class="lep.job.impl.Size, lep">
            <!-- NB: PaperSize is not mapped -->
            <property name="Width" not-null="true" />
            <property name="Height" not-null="true" />
        </component>

        <!--
        <many-to-one name="FreightPaperSize" class="lep.job.IPaperSize, lep" column="FreightPaperSizeId" cascade="none" />
		<property name="FreightPaperSizeQty" type="Int32" not-null="false" />
        -->

        <property name="DateCreated" column="DateCreated" type="lumen.hibernate.type.DateTimeType, lumen" not-null="false" update="false" insert="false" />
        <subclass name="lep.job.impl.PaperSize, lep" proxy="IPaperSize" discriminator-value="not null" />
    </class>

    <class name="IJob" table="job" discriminator-value="null">
        <cache usage="read-write" />
        <id name="Id" type="Int32" unsaved-value="0">
            <generator class="identity" />
        </id>
        <discriminator column="Id" type="Int32" insert="false" />
        <timestamp name="DateModified" column="DateModified" />

        <!--<property name="Customer"
				formula="
				(SELECT xc.Name FROM Job AS xj  INNER JOIN [Order] AS xo ON xj.OrderId = xo.Id INNER JOIN  [LepUser] lu on xo.userId = lu.Id INNER JOIN  Customer AS xc ON xc.CustomerId = lu.Id WHERE (xj.Id = Id))
				"  type="string"   />-->

        <!--<property name="PressDetailPaperSizes"
				  formula="(Select distinct pd0.size + ' ' from PressDetail pd0 where pd0.jobId = Id FOR XML PATH('')) " />
		<property name="PressDetailPressSheets"
		          formula="
					   case
						   When JobOptionId in (8,14,22) then (select  Sum(coalesce(pd1.Sect,0) *  coalesce(pd1.Qty, 0) ) from PressDetail pd1 where pd1.JobId = Id)
						   When JobOptionId in (1,2,3,26,27,11) then (SELECT  top (1)  Run.NumOfPressSheets FROM Run INNER JOIN RunJob rjx ON Run.Id = rjx.RunId WHERE (rjx.JobId = Id) order by rjx.RunId desc)
						   When JobOptionId not in (8,14,22,1,2,3,26,27,11) then (select  Sum( coalesce(pd3.Qty, 0) ) from PressDetail pd3 where pd3.JobId = Id)
						   else 0
					   end
	   " />-->

        <property name="Name" length="80" not-null="true" />
        <many-to-one name="Template" column="JobOptionId" class="IJobTemplate" not-null="true" cascade="none" />
        <many-to-one name="Order" column="OrderId" class="lep.order.IOrder, lep" not-null="true" cascade="none" />
        <!--<many-to-one name="Quote" column="QuoteId" class="lep.quote.IQuote, lep" not-null="false" cascade="none" />-->
        <property name="Quantity" not-null="true" type="Int32" />
        <property name="ThicknessOfSingleJob" not-null="false" />
        <property name="PrintType" type="lep.GenericEnum`1[lep.job.PrintType], lep" not-null="false" />
        <property name="ForcedPrintType" type="lep.GenericEnum`1[lep.job.PrintType], lep" not-null="false" />
        <property name="CustomSlot" not-null="true" type="Int32" />
        <property name="PriceBase" column="PriceBase" length="10" not-null="false" />
        <property name="PriceMargin" type="Decimal" />
        <property name="PriceMarginValue" column="PriceMarginValue" length="10" not-null="false" />
        <property name="ProductPriceCode" column="ProductPriceCode" length="5" not-null="false" />
        <property name="Price" column="Price" length="10" not-null="false" />
        <property name="CustomerRequestedPrice" column="CustomerRequestedPrice" not-null="false" />
        <property name="PriceDate" type="lumen.hibernate.type.DateTimeType, lumen" />
        <property name="IsQuotePrice" type="YesNo" not-null="true" />
        <!--<property name="MYOB" length="20" />-->
        <property name="ArtworkStatus" column="ArtworkStatus" type="lep.GenericEnum`1[lep.job.ArtworkStatusOption], lep" not-null="true" />
        <bag name="Artworks" table="JobArtwork" cascade="all-delete-orphan">
            <key column="JobId" />
            <one-to-many class="IArtwork" />
        </bag>
        <property name="SpecialInstructions" type="StringClob" />
        <property name="ProductionInstructions" type="StringClob" />
        <many-to-one name="Stock" class="IStock" not-null="true" cascade="none" />
        <many-to-one name="StockOverride" class="IStock" not-null="false" cascade="none" />
        <many-to-one name="BindingOption" class="IBindingOption" not-null="false" cascade="none" />
        <property name="FrontPrinting" column="FrontOuterPrinting" type="lep.GenericEnum`1[lep.job.JobPrintOptions], lep" not-null="true" />
        <property name="BackPrinting" column="BackInnerPrinting" type="lep.GenericEnum`1[lep.job.JobPrintOptions], lep" not-null="true" />
        
        <property name="FrontCelloglaze" column="FrontOuterCelloglaze" type="lep.GenericEnum`1[lep.job.JobCelloglazeOptions], lep" not-null="true" />
        <property name="BackCelloglaze" column="BackInnerCelloglaze" type="lep.GenericEnum`1[lep.job.JobCelloglazeOptions], lep" not-null="true" />
     
        <property name="FrontCelloglazeOverride" column="FrontOuterCelloglazeOverride" type="lep.GenericEnum`1[lep.job.JobCelloglazeOptions], lep" not-null="false" />
        <property name="BackCelloglazeOverride" column="BackInnerCelloglazeOverride" type="lep.GenericEnum`1[lep.job.JobCelloglazeOptions], lep" not-null="false" />

        <property name="FoilColour" column="FoilColour" not-null="false" />
        <property name="Envelope" column="Envelope" not-null="false" />
        <property name="EnvelopeType" column="EnvelopeType" not-null="false" />

        <property name="Rotation" column="Rotation" type="lep.GenericEnum`1[lep.job.RotationOption], lep" not-null="true" />
        <property name="BoundEdge" type="lep.GenericEnum`1[lep.job.JobBoundEdgeOptions], lep" not-null="true" />

        <component name="FinishedSize" class="lep.job.impl.Size, lep">
            <many-to-one name="PaperSize" column="FinishedPaperSizeId" class="IPaperSize" not-null="true" cascade="none" />
            <property name="Width" column="FinishedWidth" not-null="true" />
            <property name="Height" column="FinishedHeight" not-null="true" />
        </component>
        <component name="FoldedSize" class="lep.job.impl.Size, lep">
            <many-to-one name="PaperSize" column="FoldedPaperSizeId" class="IPaperSize" not-null="false" cascade="none" />
            <property name="Width" column="FoldedWidth" not-null="false" />
            <property name="Height" column="FoldedHeight" not-null="false" />
        </component>
        <property name="RoundOption" type="lep.GenericEnum`1[lep.job.RoundOption], lep" not-null="true" />
        <property name="RoundDetailOption" type="lep.GenericEnum`1[lep.job.RoundDetailOption], lep" not-null="true" />
        <property name="CustomDieCut" length="255" />
        <property name="SendSamples" type="YesNo" not-null="true" />
        <property name="ReOrderSourceJobId" type="Int32" not-null="true" />
        <property name="TRround" type="YesNo" not-null="true" />
        <property name="TLround" type="YesNo" not-null="true" />
        <property name="BRround" type="YesNo" not-null="true" />
        <property name="BLround" type="YesNo" not-null="true" />
        <property name="NumberOfHoles" type="Int32" not-null="false" />
        <property name="HoleDrilling" type="lep.GenericEnum`1[lep.job.HoleDrilling], lep" not-null="true" />
        <property name="PadDirection" type="lep.GenericEnum`1[lep.job.PadDirection], lep" not-null="true" />
        <!--<property name="Magnet" type="YesNo" not-null="true" />-->
        <property name="NumberOfMagnets" not-null="true" />
        <property name="Pages" not-null="true" />
        <property name="SelfCovered" type="YesNo" not-null="true" />
        <many-to-one name="StockForCover" class="IStock" cascade="none" />
        <many-to-one name="StockForCoverOverride" class="IStock" cascade="none" not-null="false"/>
        <property name="ArtSuppliedVia" column="ArtVia" type="StringClob" />
        <component name="Proofs" class="lep.job.impl.Proofs, lep">
            <property name="ProofsRequired" column="SendProof" type="YesNo" not-null="true" />
            <property name="NumProofsSentA1" column="NumProofsSentA1" not-null="true" />
            <property name="NumProofsSentA2" column="NumProofsSentA2" not-null="true" />
            <property name="NumProofsSentA3" column="NumProofsSentA3" not-null="true" />
        </component>
        
        
        <property name="NCRNumbered" type="YesNo" not-null="true" />
        <property name="NCRStartingNumber" not-null="false" />
        <property name="NCRInfo" column="NCRInfo"  not-null="false"
                  type="lep.JsonType`1[lep.job.NCRInfo], lep" />

        <property name="Status" type="lep.GenericEnum`1[lep.job.JobStatusOptions], lep" not-null="true" />
        <property name="StatusDate" type="lumen.hibernate.type.DateTimeType, lumen" not-null="true" />
        <property name="NextStatus" type="lep.GenericEnum`1[lep.job.JobStatusOptions], lep" not-null="false" />
        <property name="ProofStatus" type="lep.GenericEnum`1[lep.job.JobProofStatus], lep" not-null="true" />
        <!--<property name="Enable" column="IsEnable" type="YesNo" not-null="true" />-->
        <property name="QuoteNeedApprove" type="YesNo" not-null="true" />
        <property name="HasRejectedBefore" type="YesNo" not-null="true" />
        <property name="SupplyArtworkApproval" column="LepApproval" type="lep.GenericEnum`1[lep.job.JobApprovalOptions], lep" not-null="true" />
        <property name="ReadyArtworkApproval" column="ArtworkApproval" type="lep.GenericEnum`1[lep.job.JobApprovalOptions], lep" not-null="true" />
        <property name="ReceivedDate" type="lumen.hibernate.type.DateTimeType, lumen" not-null="false" />
        <property name="TrackProgress" type="YesNo" not-null="true" />
        <property name="Urgent" type="YesNo" not-null="true" />
        <component name="Prepress" class="lep.job.impl.Prepress, lep" lazy="true">
            <many-to-one name="PrepressBy" class="lep.user.impl.Staff, lep" cascade="none" />
            <property name="PrepressDate" type="lumen.hibernate.type.DateTimeType, lumen" />
            <component name="OneSided" class="lep.job.impl.PrepressWork, lep">
                <property name="Sheets" column="PrepressOneSidedSheets" not-null="false" />
                <property name="A1" column="PrepressOneSidedA1" not-null="false" />
                <property name="A2" column="PrepressOneSidedA2" not-null="false" />
            </component>
            <component name="WorkAndTurn" class="lep.job.impl.PrepressWork, lep">
                <property name="Sheets" column="PrepressWorkAndTurnSheets" not-null="false" />
                <property name="A1" column="PrepressWorkAndTurnA1" not-null="false" />
                <property name="A2" column="PrepressWorkAndTurnA2" not-null="false" />
            </component>
            <component name="WorkAndTumble" class="lep.job.impl.PrepressWork, lep">
                <property name="Sheets" column="PrepressWorkAndTumbleSheets" not-null="false" />
                <property name="A1" column="PrepressWorkAndTumbleA1" not-null="false" />
                <property name="A2" column="PrepressWorkAndTumbleA2" not-null="false" />
            </component>
            <component name="SheetWork" class="lep.job.impl.PrepressWork, lep">
                <property name="Sheets" column="PrepressSheetWorkSheets" not-null="false" />
                <property name="A1" column="PrepressSheetWorkA1" not-null="false" />
                <property name="A2" column="PrepressSheetWorkA2" not-null="false" />
            </component>
            <component name="Cover" class="lep.job.impl.PrepressWork, lep">
                <property name="Sheets" column="PrepressCoverSheets" not-null="false" />
                <property name="A1" column="PrepressCoverA1" not-null="false" />
                <property name="A2" column="PrepressCoverA2" not-null="false" />
            </component>
        </component>
        <many-to-one name="PrepressCheckedBy" class="lep.user.impl.Staff, lep" cascade="none" />
        <many-to-one name="PrintedBy" class="lep.user.impl.Staff, lep" cascade="none" />
        <property name="PrintedDate" type="lumen.hibernate.type.DateTimeType, lumen" />
        <property name="NumPressSheets" />
        <property name="LepSpecialInstructions" type="StringClob" />
        <property name="Folding" type="StringClob" />
        <property name="Scoring" type="YesNo" not-null="true" />
        <property name="ScoringInstructions" type="StringClob" />
        <property name="Perforating" type="YesNo" not-null="true" />
        <property name="PerforatingInstructions" type="StringClob" />
        <property name="DieCutType" type="lep.GenericEnum`1[lep.job.CutOptions], lep" />
        <property name="DieCutting" type="StringClob" />
        <property name="RequestedPackaging" type="StringClob" />
        <property name="DispatchRequirements" type="StringClob" />
        <!--<property name="ActualPackaging" type="StringClob" />-->
        <many-to-one name="FinishedBy" class="lep.user.impl.Staff, lep" cascade="none" />
        <property name="FinishedDate" type="lumen.hibernate.type.DateTimeType, lumen" />
        <property name="Preview" length="255" />
        <property name="DispatchDate" />
        <property name="IsCustomFacility" type="YesNo" not-null="true" />
        <property name="Facility" column="Facility" type="lep.GenericEnum`1[lep.job.Facility], lep" not-null="false" />
        <bag name="Runs" table="runjob" inverse="true" lazy="true">
            <key column="jobid" />
            <many-to-many column="runid" class="lep.run.IRun, lep" not-found="ignore" />
        </bag>

        <bag name="Comments" table="comment" order-by="DateCreated"  lazy="true">
            <key column="jobid" />
            <composite-element class="lep.job.impl.Comment, lep">
                <property name="CommentText" type="StringClob" />
                <many-to-one name="Author" class="lep.user.IUser, lep" cascade="none" />
                <property name="CreationDate" column="DateCreated" type="lumen.hibernate.type.DateTimeType, lumen" not-null="false" update="false" insert="false" />
                <property name="LepOnly" type="YesNo" not-null="true" />
            </composite-element>
        </bag>
        
        <!--
        <list name="StatusChangeEvents" table="JobStatusChangeEvent" cascade="all-delete-orphan" lazy="false">
			<key column="jobid" />
			<index  column="sortorder" />
			<composite-element class="lep.job.impl.StatusChangeEvent, lep">
				<property name="Status" type="NHibernate.Type.EnumStringType`1[[lep.job.JobStatusOptions, lep]]" not-null="true" />
				<property name="Status" type="NHibernate.Type.EnumStringType`1[[radins.order.ShoppingCartStatus, radins]]" />
            	<property     name="Status" type="lep.GenericEnum`1[lep.job.JobStatusOptions], lep" not-null="true" />
				<property     name="StatusReachedAt" type= "lumen.hibernate.type.DateTimeType, lumen" not-null="false" />
				<many-to-one  name="Author" class="lep.user.IUser, lep" cascade="none" />
			</composite-element>
		</list>
        -->
        
        <bag name="PressDetails" table="pressdetail" order-by="Id">
            <key column="jobid" />
            <composite-element class="lep.job.impl.PressDetail, lep">
                <property name="Stock" length="100" />
                <property name="Sect" type="Int32" not-null="true" />
                <property name="Size" length="100" />
                <property name="Method" length="100" />
                <property name="Qty" type="Int32" not-null="true" />
            </composite-element>
        </bag>
        <many-to-one name="CreatedBy" column="CreatedBy" class="lep.user.IUser, lep"   not-null="false" cascade="none" />
        <property name="DateCreated" column="DateCreated" not-null="true" update="false" insert="false" type="DateTime"  generated="always"/>
        <property name="Printed" type="YesNo" not-null="true" />
        <property name="MailedPrePayment" type="YesNo" not-null="true" />
        <property name="MailedGonePlate" type="YesNo" not-null="true" />
        <property name="MailedGoneFinish" type="YesNo" not-null="true" />
        <property name="MailedComplete" type="YesNo" not-null="true" />
        <property name="MailedCompletePayment" type="YesNo" not-null="true" />
        <property name="RequiredByDate" />
        <property name="PrintByDate" />
        <property name="IsReprint" type="YesNo" not-null="true" />
        <property name="ReprintReason" type="StringClob" />
        <property name="ReprintResult" type="StringClob" />

        <!-- //cr21 spring mappings for new columns -->
        <property name="ReprintFromPreviousJobNo" type="Int32" />
        <property name="ReprintFromPreviousRunNo" type="Int32" />

        <property name="NCRNo" type="String" />
        <property name="ReprintCost" length="10" not-null="false" />

        <property name="IsReprintJobDispatch" type="YesNo" not-null="true" />
        <property name="ScanCount" type="Int32" not-null="true" />

        <component name="BrochureDistPackInfo" class="lep.job.BrochureDistPackInfo, lep">
            <many-to-one name="MailHouse" column="MailHouse" class="MailHouse" not-null="false" cascade="none" />
            <property not-null="false"  name="PackingInstruction" column="PackingInstruction" />
            <property not-null="false"  name="BundlesOf" column="BundlesOf" />
            <!--Fuji Xerox-->
            <property not-null="false"  name="QtyPerBox"       column="BrochureMailHouse_QtyPerBox" />
            <property not-null="false"  name="DatePacked"      column="BrochureMailHouse_DatePacked" />
            <!--Salmat-->
            <property not-null="false"  name="BookingIdNumber" column="BrochureMailHouse_BookingIdNumber" />
            <property not-null="false"  name="CampaignName"    column="BrochureMailHouse_CampaignName" />
        </component>

        <!--<property name="IsWithdrawn" type="YesNo" not-null="true" />-->
        <many-to-one name="ReprintBy" class="lep.user.IUser, lep" cascade="none" />

        <!--
        <component name="Freight" class="lep.freight.impl.Freight, lep">
          <bag name="Cartons" table="JobCartons" cascade="all-delete-orphan" lazy="true">
            <key column="jobId" />
            <composite-element class="lep.job.JobCarton, lep">
              <many-to-one name="Carton" class="lep.freight.ICarton, lep" column="CartonId" not-null="true" cascade="none" />
              <property name="CartonQty" type="Int32" not-null="true" />
              <property name="JobQtyPerCarton" type="Int32" not-null="true" />
              <property name="WeightPerCarton" type="Decimal" not-null="true" />
            </composite-element>
          </bag>
          <property name="IsCustom" column="IsFreightCustom" type="YesNo" />
        </component>
        <property name="AACNotPerformed" column="AACNotPerformed" type="YesNo" not-null="false" />
        -->
        <property name="AACNotPerformedOld" column="AACNotPerformed" type="YesNo" not-null="false" />
        <component name="Freight" class="lep.freight.impl.Freight, lep">
            <property name="Packages" column="Packages" type="lep.JsonType`1[lep.freight.ListOfPackages], lep" />
            <property name="IsCustom" column="IsFreightCustom" type="YesNo" />
        </component>

        <property name="InvolvesOutwork" type="YesNo" not-null="true" />


        <property name="IsWhiteLabel" type="YesNo" not-null="true" />
        <property name="PriceWL" column="PriceWL"  not-null="false" />

        <property name="QuoteEstimator"     not-null="false" />
        <property name="QuoteCOGS"          not-null="false" />
        <property name="QuoteOutworkCost"   not-null="false" />
        <property name="QuoteComments"      not-null="false" />
        <property name="QuotePrimary"       not-null="false" />

        <property name="QuoteOutcome"      not-null="false" />
        <property name="QuoteLostReason"   not-null="false" />
        <property name="QuoteLostComments" not-null="false" />
        <property name="QuoteFollowUpNotes"       not-null="false" />

        <property name="Splits"    type="lep.JsonType`1[lep.job.JobSplits], lep"    not-null="false" />
        <property name="WiroInfo" type="lep.JsonType`1[lep.job.WiroMagazineInfo], lep" not-null="false" />

        <subclass name="lep.job.impl.Job, lep" proxy="IJob" discriminator-value="not null" />
    </class>

    <class name="IArtwork" table="JobArtwork" discriminator-value="null">
        <cache usage="read-write" />
        <id name="Id" type="Int32" unsaved-value="0">
            <generator class="identity" />
        </id>
        <discriminator column="Id" type="Int32" insert="false" />
        <many-to-one name="Job" column="JobId" class="IJob" not-null="true" cascade="none" />
        <property name="Type" column="ArtType" type="lep.GenericEnum`1[lep.job.ArtworkTypeOptions], lep" not-null="true" />
        <property name="Supplied" column="ArtSupplied" length="80" not-null="true" />
        <property name="Ready" column="ArtReady" length="80" />
        <property name="Preview" column="ArtPreview" length="80" />
        <property name="Position" column="ArtworkPosition" length="50" />
        <property name="SuppliedCheckSum" length="100" />
        <property name="PreviewdCheckSum" length="100" />
        <property name="ReadyCheckSum" length="100" />
        <property name="AACPerformed" type="YesNo"  not-null="false" />
        <subclass name="lep.job.impl.Artwork, lep" proxy="IArtwork" discriminator-value="not null" />
    </class>

    <class name="IJobTemplate" table="joboption" discriminator-value="null">
        <cache usage="read-write" />
        <id name="Id" type="Int32" unsaved-value="0">
            <generator class="identity" />
        </id>
        <discriminator column="Id" type="Int32" insert="false" />
        <timestamp name="DateModified" column="DateModified" />
        <property name="Name" length="50" not-null="true" />
        <property name="FG_Production" type="YesNo" not-null="true" />
        <property name="PM_Production" type="YesNo" not-null="true" />
        <property name="Category" length="50" not-null="false" />
        <!--<bag name="SizeOptions" cascade="all-delete-orphan" lazy="true">
            <key column="joboptionid" />
            <one-to-many class="IJobOptionSpecSize" />
        </bag>-->
        <property name="DateCreated" column="DateCreated" type="lumen.hibernate.type.DateTimeType, lumen" not-null="false" update="false" insert="false" />
        <subclass name="lep.job.impl.JobTemplate, lep" proxy="IJobTemplate" discriminator-value="not null" />
    </class>

    <class name="IJobOptionSpecSize" table="JobOptionSpecSize" discriminator-value="null">
        <cache usage="read-write" />
        <id name="Id" type="Int32" unsaved-value="0">
            <generator class="identity" />
        </id>
        <discriminator column="Id" type="Int32" insert="false" />
        <many-to-one name="JobTemplate" column="JobOptionId" class="IJobTemplate" not-null="true" cascade="none" />
        <!--<many-to-one name="JobTemplate"  column="JobOptionId" class="lep.job.impl.JobTemplate, lep" not-null="true" cascade="none" />-->
        <many-to-one name="PaperSize" column="PaperSizeId" class="IPaperSize" not-null="true" cascade="none" />
        <bag name="StockOptions" cascade="all-delete-orphan" lazy="true" inverse="true">
            <key column="JobOptionSpecSizeId" />
            <one-to-many class="IJobOptionSpecStock" />
        </bag>
        <subclass name="lep.job.impl.JobOptionSpecSize, lep" proxy="IJobOptionSpecSize" discriminator-value="not null" />
    </class>

    <class name="IJobOptionSpecStock" table="JobOptionSpecStock" discriminator-value="null">
        <cache usage="read-write" />
        <id name="Id" type="Int32" unsaved-value="0">
            <generator class="identity" />
        </id>
        <discriminator column="Id" type="Int32" insert="false" />
        <many-to-one name="JobOptionSpecSize" column="JobOptionSpecSizeId" class="IJobOptionSpecSize" not-null="true" cascade="none" />
        <many-to-one name="Stock" column="StockId" class="IStock" not-null="true" cascade="none" />
        <!--<property name="Magnet" type="YesNo" not-null="true" />-->
        <property name="MinMagnet" not-null="true" />
        <property name="PrintType" type="lep.GenericEnum`1[lep.job.PrintType], lep" not-null="false" />

        <bag name="FrontPrintOptions" table="JobOptionSpecFrontPrint" cascade="all-delete-orphan">
            <key column="JobOptionSpecStockId" />
            <element column="FrontPrint" type="lep.GenericEnum`1[lep.job.JobPrintOptions], lep" />
        </bag>
        <bag name="BackPrintOptions" table="JobOptionSpecBackPrint" cascade="all-delete-orphan">
            <key column="JobOptionSpecStockId" />
            <element column="BackPrint" type="lep.GenericEnum`1[lep.job.JobPrintOptions], lep" />
        </bag>
        <bag name="CelloOptions" table="JobOptionSpecCello" cascade="all-delete-orphan" inverse="true">
            <key column="JobOptionSpecStockId" />
            <one-to-many class="ICelloOption" />
        </bag>
        <bag name="FoldOptions" table="JobOptionSpecFold" cascade="none">
            <key column="JobOptionSpecStockId" />
            <many-to-many class="IPaperSize" column="PaperSizeId" />
        </bag>
        <component name="QuantityOption" class="lep.job.impl.JobOptionSpecQuantity, lep">
            <property name="Minium" type="Int32" />
            <property name="Step1" type="Int32" />
            <property name="Change" type="Int32" />
            <property name="Step2" type="Int32" />
            <property name="Change2" type="Int32" />
        </component>
        <subclass name="lep.job.impl.JobOptionSpecStock, lep" proxy="IJobOptionSpecStock" discriminator-value="not null" />
    </class>

    <class name="ICelloOption" table="JobOptionSpecCello" discriminator-value="null">
        <cache usage="read-write" />
        <id name="Id" type="Int32" unsaved-value="0">
            <generator class="identity" />
        </id>
        <discriminator column="Id" type="Int32" insert="false" />
        <many-to-one name="JobOptionSpecStock" column="JobOptionSpecStockId" class="IJobOptionSpecStock" not-null="true" cascade="none" />
        <property name="CelloFront" column="CelloFront" type="lep.GenericEnum`1[lep.job.JobCelloglazeOptions], lep" not-null="true" />
        <property name="CelloBack" column="CelloBack"  type="lep.GenericEnum`1[lep.job.JobCelloglazeOptions], lep" not-null="true" />
        <subclass name="lep.job.impl.CelloOption, lep" proxy="ICelloOption" discriminator-value="not null" />
    </class>

    <!--<class name="JobDespatchUpdate" table="JobDespatchUpdate">
		<cache usage="read-write" />
		<id name="JobId" type="Int32" unsaved-value="0">
			<generator class="identity" />
		</id>
		<property name="Response" length="50" />
	</class>-->

    <class name="MailHouse" table="BrochureMailHouses" discriminator-value="null">
        <cache usage="read-write" />
        <id name="Id" type="Int32" unsaved-value="0">
            <generator class="identity" />
        </id>
        <discriminator column="Id" type="Int32" insert="false" />
        <property name="Name" type="string" length="50"  not-null="true" />
        <property name="Instructions" type="StringClob" not-null="false" />


         <property name="InBundlesOf"   not-null="true" />
        <property name="MaxInBundle"  not-null="true" />
        <property name="MaxWeightPerCarton"   not-null="true" /> 
    </class>
</hibernate-mapping>
