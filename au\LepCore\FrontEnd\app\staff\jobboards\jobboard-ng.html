<div class="jobboard-ng-container">
    <!-- Header -->
    <div class="jobboard-header">
        <h3>Job Board - {{vm.getFacilityLabel()}}</h3>
        <div class="alert-counters">
            <span class="alert-box red-alert-box">Red <span>{{vm.alertCounts.red}}</span></span>
            <span class="alert-box amber-alert-box">Amber <span>{{vm.alertCounts.amber}}</span></span>
        </div>
    </div>

    <!-- Filters -->
    <div class="jobboard-filters">
        <div class="filter-group">
            <label for="facilitySelect">Facility:</label>
            <select id="facilitySelect"
                    class="form-control"
                    ng-model="vm.selectedFacility"
                    ng-change="vm.onFilterChange()"
                    ng-options="facility.value as facility.label for facility in vm.facilities">
            </select>
        </div>

        <div class="filter-group">
            <label for="boardSelect">Board:</label>
            <select id="boardSelect"
                    class="form-control"
                    ng-model="vm.selectedBoard"
                    ng-change="vm.onFilterChange()"
                    ng-options="board.value as board.label for board in vm.boards">
            </select>
        </div>

        <!-- Reset Preferences Button -->
        <div class="filter-group">
            <label>&nbsp;</label> <!-- Empty label for alignment -->
            <button type="button"
                    class="btn btn-default btn-sm"
                    ng-click="vm.clearUserPreferences(); vm.onFilterChange()"
                    title="Reset to default facility and board selection">
                <i class="glyphicon glyphicon-refresh"></i>
                Reset Preferences
            </button>
        </div>
    </div>

    <!-- Loading indicator -->
    <div ng-if="vm.loading" class="loading-indicator">
        <i class="glyphicon glyphicon-refresh spinning"></i> Loading jobs...
    </div>

    <!-- Error message -->
    <div ng-if="vm.error" class="alert alert-danger">
        {{vm.error}}
    </div>

    <!-- Jobs table -->
    <div ng-if="!vm.loading && !vm.error" class="jobs-table-container">
        <table class="table table-striped table-hover jobs-table">
            <thead>
                <tr>
                    <th class="actions-header">Actions</th>
                    <th>Job ID</th>
                    <th>Order</th>
                    <th>Run</th>
                    <th>Customer</th>
                    <th>Job Name</th>
                    <th>Print By</th>
                    <th>Despatch By</th>
                    <th>Hours Remaining</th>
                    <th>Status</th>
                    <th>Job Type</th>
                    <th>Qty</th>
                    <th>Age</th>
                </tr>
            </thead>
            <tbody>
                <tr ng-repeat="job in vm.jobs track by job.id"
                    ng-class="job.healthClass">

                    <!-- Actions -->
                    <td class="actions-cell">
                        <!-- Timing Controls -->
                        <div class="timing-controls">
                            <div class="timing-display">{{vm.getTimingDisplay(job.id, job.currentStatus)}}</div>
                            <div class="timing-buttons">
                                <!-- Play Button -->
                                <button type="button"
                                        class="btn btn-xs btn-success timing-btn"
                                        ng-if="vm.getTimingButtonState(job.id, job.currentStatus).canPlay"
                                        ng-click="vm.startTiming(job.id, job.currentStatus)"
                                        title="Start timing for {{job.currentStatus}}">
                                    <i class="glyphicon glyphicon-play"></i>
                                </button>

                                <!-- Pause Button -->
                                <button type="button"
                                        class="btn btn-xs btn-warning timing-btn"
                                        ng-if="vm.getTimingButtonState(job.id, job.currentStatus).canPause"
                                        ng-click="vm.pauseTiming(job.id, job.currentStatus)"
                                        title="Pause timing">
                                    <i class="glyphicon glyphicon-pause"></i>
                                </button>

                                <!-- Resume Button -->
                                <button type="button"
                                        class="btn btn-xs btn-info timing-btn"
                                        ng-if="vm.getTimingButtonState(job.id, job.currentStatus).canResume"
                                        ng-click="vm.resumeTiming(job.id, job.currentStatus)"
                                        title="Resume timing">
                                    <i class="glyphicon glyphicon-play"></i>
                                </button>

                                <!-- Finish Button -->
                                <button type="button"
                                        class="btn btn-xs btn-danger timing-btn"
                                        ng-if="vm.getTimingButtonState(job.id, job.currentStatus).canFinish"
                                        ng-click="vm.finishTiming(job.id, job.currentStatus)"
                                        title="Finish timing">
                                    <i class="glyphicon glyphicon-stop"></i>
                                </button>
                            </div>

                            <!-- Job Details Button -->
                            <button type="button"
                                class="btn btn-xs btn-info details-btn"
                                ng-click="vm.openJobDetails(job.id)"
                                title="View job details">
                                <i class="glyphicon glyphicon-eye-open"></i>
                            </button>
                        </div>
                    </td>

                    <!-- Job ID -->
                    <td>
                        <a href="#" ng-click="vm.openJobDetails(job.id)" class="job-link">
                            {{job.id}}
                        </a>
                    </td>

                    <!-- Order ID -->
                    <td>
                        <a href="#" ng-click="vm.openOrderDetails(job.orderId)" class="order-link">
                            {{job.orderId}}
                        </a>
                    </td>

                    <!-- Run ID -->
                    <td>
                        <a href="#"
                           ng-if="job.runId && job.runId !== -1"
                           ng-click="vm.openRunDetails(job.runId)"
                           class="run-link">
                            {{job.runId}}
                        </a>
                        <span ng-if="!job.runId || job.runId === -1">-</span>
                    </td>

                    <!-- Customer -->
                    <td>{{job.customerName}}</td>

                    <!-- Job Name -->
                    <td class="job-name">{{job.jobName}}</td>

                    <!-- Print By -->
                    <td>
                        <span ng-if="job.submittedDate">
                            {{job.submittedDate | date:'dd/MM/yy HH:mm'}}
                        </span>
                    </td>

                    <!-- Despatch By -->
                    <td>
                        <span ng-if="job.orderDispatchEst">
                            {{job.orderDispatchEst | date:'dd/MM/yy HH:mm'}}
                        </span>
                    </td>

                    <!-- Hours Remaining -->
                    <td>
                        <span ng-if="job.hd">{{vm.formatDuration(job.hd)}}</span>
                    </td>

                    <!-- Status -->
                    <td class="status-cell">{{job.currentStatus}}</td>

                    <!-- Job Type -->
                    <td>{{job.template}}</td>

                    <!-- Quantity -->
                    <td class="text-right">{{job.quantity | number}}</td>

                    <!-- Age -->
                    <td>{{vm.formatAge(job.age)}}</td>
                </tr>
            </tbody>
        </table>

        <!-- No jobs message -->
        <div ng-if="vm.jobs.length === 0" class="no-jobs-message">
            <p>No jobs found for the selected filters.</p>
        </div>
    </div>
</div>

<style>
.jobboard-ng-container {
    padding: 15px;
    height: 100vh;
    display: flex;
    flex-direction: column;
}

.jobboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #ddd;
}

.jobboard-header h3 {
    margin: 0;
    color: #333;
}

.alert-counters {
    display: flex;
    gap: 15px;
}

.alert-box {
    padding: 5px 12px;
    border-radius: 4px;
    font-weight: bold;
    font-size: 14px;
}

.red-alert-box {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.amber-alert-box {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.jobboard-filters {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 4px;
}

.filter-group {
    display: flex;
    flex-direction: column;
    min-width: 200px;
}

.filter-group label {
    font-weight: bold;
    margin-bottom: 5px;
    color: #555;
}

.filter-group select {
    padding: 8px;
    border: 1px solid #ccc;
    border-radius: 4px;
}

.loading-indicator {
    text-align: center;
    padding: 40px;
    font-size: 16px;
    color: #666;
}

.spinning {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.jobs-table-container {
    flex: 1;
    overflow: auto;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.jobs-table {
    margin: 0;
    font-size: 13px;
}

.jobs-table th {
    background-color: #f8f9fa;
    font-weight: bold;
    border-bottom: 2px solid #dee2e6;
    position: sticky;
    top: 0;
    z-index: 10;
}

.jobs-table td, .jobs-table th {
    padding: 8px 10px;
    vertical-align: middle;
    white-space: nowrap;
}

.job-name {
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
}

.status-cell {
    font-weight: bold;
}

.actions-header {
    width: 160px;
    min-width: 160px;
    text-align: center;
}

.actions-cell {
    text-align: center;
    width: 160px;
    min-width: 160px;
    padding: 8px 12px !important;
}

.timing-controls {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 6px;
    margin-bottom: 6px;
}

.timing-display {
    font-family: 'Courier New', monospace;
    font-size: 12px;
    font-weight: bold;
    color: #333;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 3px 8px;
    min-width: 60px;
    text-align: center;
}

.timing-buttons {
    display: flex;
    gap: 3px;
    justify-content: center;
    flex-wrap: wrap;
}

.timing-btn, .details-btn {
    margin: 0;
    padding: 5px 8px !important;
    font-size: 11px !important;
    line-height: 1.2;
    border-radius: 4px;
}

.timing-btn {
    min-width: 28px;
}

.details-btn {
    margin-top: 6px;
    width: 100%;
    max-width: 120px;
}

.job-link, .order-link, .run-link {
    font-weight: bold;
    text-decoration: none;
}

.job-link:hover, .order-link:hover, .run-link:hover {
    text-decoration: underline;
}

.red-alert {
    background-color: #f8d7da !important;
}

.amber-alert {
    background-color: #fff3cd !important;
}

.no-jobs-message {
    text-align: center;
    padding: 40px;
    color: #666;
    font-style: italic;
}

.no-jobs-message p {
    margin: 0;
    font-size: 16px;
}
</style>
