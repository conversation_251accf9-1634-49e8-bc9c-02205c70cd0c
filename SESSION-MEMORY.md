# LEP Timing System Development Session Memory
*Generated: 2024-12-19*

## 🎯 **Current Session Context**

### **Project**: LEP Online Timing System Enhancement
### **User**: Experienced developer working on LEPCore project
### **Location**: C:\LepSF workspace
### **Database**: <PERSON> (PRD_AU) - SQL Server with sa/11_Fore5tGl5n credentials

## ✅ **Completed Work This Session**

### 1. **SQL Timing Reports (COMPLETED)**
- Created 8 comprehensive timing reports in `timing/reports/` folder
- **All reports tested and working** against <PERSON> database
- Fixed table relationships: Order.UserId → LepUser.Id (not Customer table)
- Corrected enum values: EventType (PLAY=1, PAUSE=2, RESUME=3, FINISH=4)
- Reports include: active sessions, performance analysis, worker productivity, bottlenecks, trends

### 2. **Play Button State Issue (FIXED)**
- **Problem**: Play button not updating to pause/stop state after clicking
- **Root Cause**: Frontend looking for `response.CurrentState` instead of `response.ButtonState.CurrentState`
- **Solution**: Fixed response handling in `jobboard-ng.controller.coffee`
- **Status**: ✅ RESOLVED - Button state updates now work correctly

### 3. **Debug System (IMPLEMENTED)**
- Added comprehensive debug logging throughout timing system
- Frontend: Emoji-prefixed console logs (🎬, 🔄, 🌐, 🔍, ❌, ⚠️)
- Backend: Structured logging in controllers and application services
- Documentation: `timing/DEBUG-TIMING-SYSTEM.md` for troubleshooting

## 🏗️ **System Architecture Understanding**

### **Database Schema**
```
JobStepActiveSession (active timing sessions)
├── SessionId (Guid, PK)
├── JobId → Job.Id
├── UserId → LepUser.Id
├── Status (string)
├── CurrentState (1=PLAYING, 2=PAUSED, 3=FINISHED)
├── StartTime, LastEventTime
└── TotalPausedDuration

JobStepTimingEvent (historical events)
├── JobId → Job.Id
├── UserId → LepUser.Id
├── EventType (1=PLAY, 2=PAUSE, 3=RESUME, 4=FINISH)
├── EventTime
└── SessionId → JobStepActiveSession.SessionId

Job → Order → LepUser (Customer relationship)
```

### **Key Relationships**
- **Order.UserId** links to **LepUser.Id** (NOT Customer table)
- **EventType**: 1=PLAY, 2=PAUSE, 3=RESUME, 4=FINISH
- **CurrentState**: 1=PLAYING, 2=PAUSED, 3=FINISHED

### **Technology Stack**
- **Backend**: .NET 8, NHibernate, Serilog
- **Frontend**: AngularJS, CoffeeScript
- **Database**: SQL Server 2014
- **API**: RESTful endpoints under `/api/jobtiming/`

## 🔧 **Key Files Modified**

### **Backend**
- `au\LepCore\BackEnd\Controllers\JobTimingController.cs` - Added debug logging
- `au\code\main\src\timing\impl\TimingApplication.cs` - Enhanced logging
- `au\code\main\src\timing\dto\TimingButtonState.cs` - JSON serialization fix

### **Frontend**
- `au\LepCore\FrontEnd\app\staff\jobboards\jobboard-ng.controller.coffee` - Fixed response handling
- `au\LepCore\FrontEnd\app\staff\jobboards\jobboard-ng.service.coffee` - Added debug logging

### **Reports**
- `timing/reports/01-current-active-sessions.sql` through `08-performance-trends-over-time.sql`
- `timing/reports/README.md` - Documentation

## 🎯 **User Preferences & Patterns**

### **Code Style**
- Prefers LINQ over foreach loops
- Descriptive variable names and concise code
- Direct column references in DataTables vs numeric indices
- Succinct HTML with CSS flexbox/grid over Bootstrap nesting
- AutoMapper for object conversion
- Constants instead of hardcoded values
- No Async suffix on method names (Task<T> indicates async)

### **UI/UX Preferences**
- Prominent styling for order/job/run ID links
- Inline Special Instructions beside ProductionInstruction/DPC/IMP links
- glyphicon-comment for Special Instructions
- DataTables full width/height, no pagination, sticky headers
- 4mm margins on print layouts
- Action columns positioned first and wider
- Facility/board selections saved in localStorage

### **Development Environment**
- HTTP port 5000 (not HTTPS 5001) for local development
- Machine: app07, IIS website: C:\FS\LEPCoreBuilds
- dotnet publish instead of MSBuild
- PublishReadyToRun=false for .NET 6+
- Prefers manual gulp watch dev and dotnet watch run

## 🚀 **Next Development Opportunities**

### **Immediate Enhancements**
1. **Production Instructions Integration** - 404 error on `/api/production-instructions/ApprovedForPlating`
2. **Timing State Persistence** - Ensure states survive page refreshes
3. **Auto-pause Inactive Sessions** - Implement background cleanup
4. **Timing Analytics Dashboard** - Use the SQL reports for insights

### **Feature Requests from Memory**
1. **Job Board Improvements** - AngularJS-based boards with timing integration
2. **SendToImp Functionality** - JSON file creation in hotfolders
3. **Customer Search Enhancement** - Include notes searching
4. **Order Credit Form** - Date/calendar input modifications

### **Job Board Business Logic**
- **HD Column**: "Hours Till Dispatch" - primary sorting field for priority
- **Run Grouping**: Jobs with same runID are printed together in batches
- **Default Sort**: Usually by HD (Hours Till Dispatch) for priority ordering
- **Workflow**: Jobs move through statuses, grouped by runs for efficiency

### **Technical Debt**
1. **Remove Debug Logging** - Clean up console.log statements for production
2. **Error Handling** - Enhance error messages and recovery
3. **Performance Optimization** - Job board loading with timing state
4. **Unit Tests** - Add comprehensive test coverage

## 🔍 **Debugging Tools Available**

### **SQL Reports** (All Working)
```bash
sqlcmd -S newman -d PRD_AU -U sa -P "11_Fore5tGl5n" -i "timing/reports/01-current-active-sessions.sql"
```

### **Debug Logging Patterns**
- 🎬 Timing operations
- 🔄 State updates
- 🌐 Service calls
- 🔍 State queries
- ❌ Errors
- ⚠️ Warnings

### **Browser Console**
- F12 → Console tab
- Look for emoji-prefixed messages
- Follow debug flow sequence in `timing/DEBUG-TIMING-SYSTEM.md`

## 📚 **Important Constants**

### **Database Connection**
```
Server: newman
Database: PRD_AU
User: sa
Password: 11_Fore5tGl5n
```

### **Timing States**
```javascript
TIMING_STATES = {
    READY: 0,
    PLAYING: 1,
    PAUSED: 2,
    FINISHED: 3
}
```

### **API Endpoints**
```
POST /api/jobtiming/play
POST /api/jobtiming/pause
POST /api/jobtiming/resume
POST /api/jobtiming/finish
GET  /api/jobtiming/state/{jobId}/{status}
```

## 🎯 **Session Success Metrics**
- ✅ 8/8 SQL reports working
- ✅ Play button state issue resolved
- ✅ Comprehensive debug system implemented
- ✅ Database schema understanding documented
- ✅ Development patterns captured

---
*This memory file enables seamless session resumption and feature development continuation.*
