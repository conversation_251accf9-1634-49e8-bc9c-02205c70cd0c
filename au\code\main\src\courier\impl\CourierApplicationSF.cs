using System;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Reflection;
using System.ServiceModel;
using Serilog;
using lep.configuration;
using lep.freight;
using lep.job;
using lep.order;
using lep.security;
using Newtonsoft.Json;
using NHibernate;
using System.Threading.Tasks;
using lep.configuration.impl;
using lep.freight.impl;
using System.Xml.Linq;
using System.Xml.XPath;
using lep.src.freight.impl;
using NHibernate.Util;
using lep.address;
using lep.extensionmethods;
using lep.order.impl;
using lep.user;
using NHibernate.Criterion;
using System.Runtime.ConstrainedExecution;

namespace lep.courier.impl
{
	public static class StringExtensions
	{
		public static string Left(this string value, int maxLength)
		{
			if (string.IsNullOrEmpty(value)) return value;
			maxLength = Math.Abs(maxLength);

			return (value.Length <= maxLength
				   ? value
				   : value.Substring(0, maxLength)
				   );
		}
	}
	// Wrapper around Comp data webservice
	public sealed class CourierApplicationSF : BaseApplication, ICourierApplication
	{
		public const string LEP = "LEP";
		private const string SF_QLD_KEY = "RqB0fJOapwP79AVDdzF5Pgg7fESwF4GxHQ";
		private const string SF_VIC_KEY = "JoIJ2bXndxCk0V3ZfkM6VxKuzv1qgEAiA0";
		private const string AUTHORITY_TO_LEAVE = "AUTHORITY TO LEAVE";

		//private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		public CourierApplicationSF(ISession sf,
			ISecurityApplication _securityApp,
			IJobApplication jobApp,
			lep.SmartFreight.SFOv1 iSmartFreight,
			lep.SmartFreight.ISmartFreightDeliveryOptions iSmartFreightDeliveryOptions,
			CartonFinder cartonFinder) : base(sf, _securityApp)
		{
			JobApplication = jobApp;
			_iSmartFreight = iSmartFreight;
			_iSmartFreightDeliveryOptions = iSmartFreightDeliveryOptions;
			_cartonFinder = cartonFinder;
		}
		private CartonFinder _cartonFinder;
		private lep.SmartFreight.SFOv1 _iSmartFreight;
		private lep.SmartFreight.ISmartFreightDeliveryOptions _iSmartFreightDeliveryOptions;
		private Func<string, object, XElement> __ = (n, c) => new XElement(n, c);
		//private string[] fastWayRoadServices = new[] { "RED", "BLACK", "BROWN", "LIME", "ORANGE", "GREEN", "WHITE", "GRAY" };



		public dynamic CreateConsignmentBase(
									   IOrder order,
									   Facility? facility,
									   CourierType courier,
									   IPhysicalAddress address,
									   IList<Package> packages,
									   string reference,
									   string deliveryInstructions,
									   string RecipientName,
									   string RecipientPhone,
									   string RecipientEmail,
									   string consignmentPrinterName = "")
		{

			try
			{


				var sender = LepGlobal.Instance.TestBox ? "TEST" : "LEP";
				sender = "LEP";
				sender += GetSendingFacilityState(facility);   // TESTQLD, TESTVIC, LEPQLD, LEPVIC

				string sfId, sfKey;
				GetSmartFreightIdKey(sender, out sfId, out sfKey);

				var req = new XElement("connote");

				req.Add(__("sendaccno", sender));
				req.Add(__("matchsendertoaccountno", "YES"));
				req.Add(__("condate", DateTime.Now.ToShortDateString()));
				req.Add(__("applymarkup", "Yes"));


				// receiver details
				// http://balmoral.ifsconnect.net/integration/smartfreight-consignment-message/receiver-details/
				// req.Add(__("recaccno", order.Customer.Id));

				req.Add(__("recname", RecipientName));
				req.Add(__("recph", RecipientPhone));

				//var recemail = order.WLContact?.Email ??
				//					  order.Contact?.Email ??
				//					  order.Customer.Email;
				//req.Add(__("recemail", recemail));
				string carriername = courier.CourierName;
				string service = courier.ServiceName;
				string accno = courier.CarrierAccount;


				var _service = service;

				//if (carriername == "FASTWAY")
				//{
				//	service = "MANUAL";
				//}

				req.Add(__("adhocrec", "Yes"));
				// set selected courier
				req.Add(__("carriername", carriername));
				req.Add(__("service", service));

				if (accno != "null")
					accno = "";

				req.Add(__("accno", accno));

				var spins = new XElement("spins");

				deliveryInstructions = (deliveryInstructions ?? "").Left(30);
				spins.Add(__("sp1", deliveryInstructions));

				switch (carriername)
				{
					//case "FASTWAY":
					//	spins.Add(__("sp2", AUTHORITY_TO_LEAVE));
					//	break;
					case "STARTRACK EXP":
						spins.Add(__("sp2", AUTHORITY_TO_LEAVE));
						spins.Add(__("sp3", "ONE DELIVERY ONLY HOLD @DEPOT"));
						break;
					case "TNT":
						spins.Add(__("sp2", AUTHORITY_TO_LEAVE));
						spins.Add(__("sp3", "DO NOT REDELIVER"));
						break;
					case "AUSTRALIA POST":
						spins.Add(__("sp2", AUTHORITY_TO_LEAVE));
						break;
					case "COURIERS PLS II":
						spins.Add(__("sp2", AUTHORITY_TO_LEAVE));
						break;
					case CourierType.TOLLNQX:
						spins.Add(__("sp2", AUTHORITY_TO_LEAVE));
						break;
				}


				req.Add(spins);
				//< spins >
				//< sp1 > ATTENTION: JOHN SMITH</ sp1 >
				//< sp2 > DELIVER TO REAR OF BUILDING</ sp2 >
				//</ spins >


				// recaddr Marks the start of the receiver address
				XElement recaddr = CreateSmartFreightReceiversAddressXmlNode(address);
				req.Add(recaddr);


				var refInclued = false;
				//http://www.abctransport.com.au/cubic-calculator
				foreach (var p in packages)
				{
					var cubicM3 = (double)((p.Width / 1000d) * (p.Height / 1000d) * (p.Depth / 1000d));
					var cubicWeight = cubicM3 * 250;

					var deadWeight = (double)(p.Weight);
					var weight = Math.Max(deadWeight, cubicWeight);

					var Cubic = cubicM3;
					var XCM = p.Width / 10d;
					var YCM = p.Height / 10d;
					var ZCM = p.Depth / 10d;

					var desc = "CARTON";
					if (p.Carton.Contains("Skid"))
						desc = "SKID";

					//if (carriername == "FASTWAY")
					//{
					//	if (!fastWayRoadServices.Contains(_service))
					//	{
					//		desc = _service;
					//	}
					//}

					var f = new XElement("freightlinedetails");
					f.Add(__("desc", desc));
					f.Add(__("amt", 1));
					f.Add(__("wgt", deadWeight));
					f.Add(__("cube", cubicM3));
					f.Add(__("len", XCM));
					f.Add(__("wdt", YCM));
					f.Add(__("hgt", ZCM));
					if (!refInclued)
					{
						f.Add(__("ref", reference));
						refInclued = true;
					}

					req.Add(f);
				}

				//consignmentPrinterName = "PDF_FG_TEST";
				if (consignmentPrinterName != "")
					req.Add(__("printer", consignmentPrinterName));

				var consignmentxml = req.ToString();

				Log.Information(string.Format("REQUEST: {0}", req.ToString()));


				var ccrStr = _iSmartFreight.Import(sfId, sfKey, "", consignmentxml);

				var ccr = XDocument.Parse(ccrStr);
				Log.Information(string.Format("RESPONSE: {0}", ccr.ToString()));

				bool hasErrors = ccr.Root.Attribute("HasErrors") != null;

				dynamic r;
				if (!hasErrors)
				{
					var connotes = ccr.XPathSelectElements("//connotenumber").Select(_ => _.Value.ToString()).ToList();
					var labels = ccr.XPathSelectElements("//labels/labelno_tracking").Select(_ => _.Value.ToString()).ToList();
					//connotes.Dump();
					//labels.Dump();
					//connotenumber
					r = new { Connote = connotes, Labels = labels };

					var cn = new lep.order.impl.OrderConNote()
					{
						CarrierName = carriername,
						CarrierService = service,
						ConNote = connotes.FirstOrDefault() ?? "-",
						TrackingLabels = labels,
						IsEmailGenerated = 0,
						Order = order,
					};
					order.ConNotes.Add(cn);
					base.Save(order);
				}
				else
				{
					//"Fail".Dump();
					var errors = ccr.XPathSelectElements("//*[@Error]").Select(_ => _.Name + " : " + _.Attribute("Error").Value).ToList();
					r = new { Errors = errors };

					Log.Error(string.Format("RESPONSE: {0}", ccr.ToString()));
					Log.Error(errors.Aggregate((m, n) => m + "\n" + n).ToString());
				}
				return r;
			}
			catch (Exception ex)
			{
				Log.Error(ex.Message, ex);
				return null;
			}

		}


		public dynamic CreateConsignment(IOrder order, Facility? facility, string consignmentPrinterName = "")
		{
			if (order == null)
				throw new ArgumentNullException(nameof(order));


			if (order.HasSplitDelivery)
			{
				var job = order.Jobs.FirstOrDefault();
				dynamic consignmentResults = new List<dynamic>();
				// for loop job.Splits
				for (int i = 0; i < job.Splits.Count; i++)
				{
					var split = job.Splits[i];
					var ref1 = order.Id.ToString() + "-" + (i + 1).ToString();

					var consignmentPrinterNameForSplit = "";
					if (consignmentPrinterName == "FG2" || consignmentPrinterName == "PM2")
					{
						var x = split.Courier.Split(new[] { '~' })[0].Replace(" ", "").ToUpper();
						consignmentPrinterNameForSplit = consignmentPrinterName + "_" + x;
					}

					var consignment = CreateConsignmentBase(
												order,
												job.Facility,
												split.Courier,
												split.Address,
												split.Packages,
												ref1,
												split.DeliveryInstructions,
												split.RecipientName,
												split.RecipientPhone, "",
												consignmentPrinterNameForSplit);
					consignmentResults.Add(consignment);
				}
				return consignmentResults;
			}

			try
			{
				// add freight items
				IList<Package> packages = order.PackDetail.GetPackages(facility);
				if (!packages.Any()) return null; // todo : return some error

				CourierType courier = CourierType.None;
				if (facility == Facility.FG)
				{
					courier = !order.PackDetail.FGCourier.IsNone ? order.PackDetail.FGCourier : order.Courier;
				}
				else if (facility == Facility.PM)
				{
					courier = !order.PackDetail.PMCourier.IsNone ? order.PackDetail.PMCourier : order.Courier;
				}

				string carriername = courier.CourierName;
				string service = courier.ServiceName;
				string accno = courier.CarrierAccount;

				var _service = service;

				//if (carriername == "FASTWAY")
				//{
				//	service = "MANUAL";
				//}


				var sender = LepGlobal.Instance.TestBox ? "TEST" : "LEP";
				sender = "LEP";
				sender += GetSendingFacilityState(facility);   // TESTQLD, TESTVIC, LEPQLD, LEPVIC

				string sfId, sfKey;
				GetSmartFreightIdKey(sender, out sfId, out sfKey);

				var req = new XElement("connote");

				req.Add(__("sendaccno", sender));
				req.Add(__("matchsendertoaccountno", "YES"));
				req.Add(__("condate", DateTime.Now.ToShortDateString()));
				req.Add(__("applymarkup", "Yes"));


				// receiver details
				// http://balmoral.ifsconnect.net/integration/smartfreight-consignment-message/receiver-details/
				req.Add(__("recaccno", order.Customer.Id));

				if (carriername == "ARAMEX")
				{
					var reccontact = "";

					if (string.IsNullOrEmpty(order.RecipientName)
						|| order.DeliveryAddress.Equals(order.Customer.PostalAddress))
					{
						reccontact = order.ReceiverName;
					}
					else
					{
						reccontact = order.RecipientName;
					}
					req.Add(__("reccontact", reccontact));
					req.Add(__("recname", order.ReceiverName));
				}
				else
				{
					req.Add(__("recname", order.ReceiverName));
				}



				req.Add(__("recph", order.ReceiverPhone));

				//var recemail = order.WLContact?.Email ??
				//					  order.Contact?.Email ??
				//					  order.Customer.Email;
				//req.Add(__("recemail", recemail));

				req.Add(__("adhocrec", "Yes"));
				// set selected courier
				req.Add(__("carriername", carriername));
				req.Add(__("service", service));

				if (accno != "null")
					accno = "";

				req.Add(__("accno", accno));

				var spins = new XElement("spins");

				var deliveryInstructions = (order.DeliveryInstructions ?? "").Left(30);
				spins.Add(__("sp1", deliveryInstructions));

				switch (carriername)
				{
					//case "FASTWAY":
					//	spins.Add(__("sp2", AUTHORITY_TO_LEAVE));
					//	break;
					case "STARTRACK EXP":
						spins.Add(__("sp2", AUTHORITY_TO_LEAVE));
						spins.Add(__("sp3", "ONE DELIVERY ONLY HOLD @DEPOT"));
						break;
					case "TNT":
						spins.Add(__("sp2", AUTHORITY_TO_LEAVE));
						spins.Add(__("sp3", "DO NOT REDELIVER"));
						break;
					case "AUSTRALIA POST":
						spins.Add(__("sp2", AUTHORITY_TO_LEAVE));
						break;
					case "COURIERS PLS II":
						spins.Add(__("sp2", AUTHORITY_TO_LEAVE));
						break;
					case CourierType.TOLLNQX:
						spins.Add(__("sp2", AUTHORITY_TO_LEAVE));
						break;
				}




				/*
				AUTHORITY TO LEAVE.
					<serviceoption>
						<option>AUTHORITYTOLEAVE</option>
						<value>YES</value>
					</serviceoption>
				var serviceoption = new XElement("serviceoption");
				serviceoption.Add(__("option", "AUTHORITYTOLEAVE"));
				serviceoption.Add(__("value", "YES"));
				req.Add(serviceoption);
				*/



				req.Add(spins);
				//< spins >
				//< sp1 > ATTENTION: JOHN SMITH</ sp1 >
				//< sp2 > DELIVER TO REAR OF BUILDING</ sp2 >
				//</ spins >


				// recaddr Marks the start of the receiver address
				XElement recaddr = CreateSmartFreightReceiversAddressXmlNode(order.DeliveryAddress);
				req.Add(recaddr);
				var refInclued = false;
				//http://www.abctransport.com.au/cubic-calculator
				foreach (var p in packages)
				{
					var cubicM3 = (double)((p.Width / 1000d) * (p.Height / 1000d) * (p.Depth / 1000d));
					var cubicWeight = cubicM3 * 250;

					var deadWeight = (double)(p.Weight);
					var weight = Math.Max(deadWeight, cubicWeight);

					var Cubic = cubicM3;
					var XCM = p.Width / 10d;
					var YCM = p.Height / 10d;
					var ZCM = p.Depth / 10d;

					var desc = "CARTON";
					if (p.Carton.Contains("Skid"))
						desc = "SKID";

					//if (carriername == "FASTWAY")
					//{
					//	if (!fastWayRoadServices.Contains(_service))
					//	{
					//		desc = _service;
					//	}
					//}

					var f = new XElement("freightlinedetails");
					f.Add(__("desc", desc));
					f.Add(__("amt", 1));
					f.Add(__("wgt", deadWeight));
					f.Add(__("cube", cubicM3));
					f.Add(__("len", XCM));
					f.Add(__("wdt", YCM));
					f.Add(__("hgt", ZCM));
					if (!refInclued)
					{
						f.Add(__("ref", order.Id.ToString()));
						refInclued = true;
					}

					req.Add(f);
				}

				//consignmentPrinterName = "PDF_FG_TEST";
				if (consignmentPrinterName != "")
					req.Add(__("printer", consignmentPrinterName));

				var consignmentxml = req.ToString();
				Log.Information(string.Format("REQUEST: {0}", order.Id.ToString()));
				Log.Debug(string.Format("REQUEST: {0}", req.ToString()));


				var ccrStr = _iSmartFreight.Import(sfId, sfKey, "", consignmentxml);

				var ccr = XDocument.Parse(ccrStr);



				Log.Debug(string.Format("RESPONSE: {0}", ccr.ToString()));


				bool hasErrors = ccr.Root.Attribute("HasErrors") != null;

				dynamic r;
				if (!hasErrors)
				{
					var connotes = ccr.XPathSelectElements("//connotenumber").Select(_ => _.Value.ToString()).ToList();
					var labels = ccr.XPathSelectElements("//labels/labelno_tracking").Select(_ => _.Value.ToString()).ToList();
					//connotes.Dump();
					//labels.Dump();
					//connotenumber
					r = new { Connote = connotes, Labels = labels };

					var cn = new lep.order.impl.OrderConNote()
					{
						CarrierName = carriername,
						CarrierService = service,
						ConNote = connotes.FirstOrDefault() ?? "-",
						TrackingLabels = labels,
						IsEmailGenerated = 0,
						Order = order,
					};
					order.ConNotes.Add(cn);
					base.Save(order);
				}
				else
				{
					//"Fail".Dump();
					var errors = ccr.XPathSelectElements("//*[@Error]").Select(_ => _.Name + " : " + _.Attribute("Error").Value).ToList();
					r = new { Errors = errors };

					Log.Error(string.Format("RESPONSE: {0}", ccr.ToString()));
					Log.Error(errors.Aggregate((m, n) => m + "\n" + n).ToString());
				}
				return r;
			}
			catch (Exception ex)
			{
				Log.Error(ex.Message, ex);
				return null;
			}
		}



		public dynamic SyncConnotes(IOrder order, Facility? facility)
		{
			try
			{
				// add freight items
				IList<Package> packages = order.PackDetail.GetPackages(facility);
				if (!packages.Any()) return null; // todo : return some error


				var sender = LepGlobal.Instance.TestBox ? "TEST" : "LEP";
				sender = "LEP";
				sender += GetSendingFacilityState(facility);   // TESTQLD, TESTVIC, LEPQLD, LEPVIC

				string sfId, sfKey;
				GetSmartFreightIdKey(sender, out sfId, out sfKey);

				var ccrStrA = _iSmartFreight.FindCon(sfId, sfKey, "ref", order.Id.ToString(), "all");
				if (!ccrStrA.Any())
				{
					return null;
				}

				var xx = (from cid in ccrStrA
						  let ccrStr = _iSmartFreight.Enquiry(sfId, sfKey, cid)
						  let ccr = XDocument.Parse(ccrStr)
						  from res in ccr.Descendants("Connote")
						  select new
						  {
							  guid = cid,
							  conno = (string)res.Element("conno"),
							  carriername = (string)res.Element("carriername"),
							  service = (string)res.Element("service"),
							  connotedeleted = ((string)res.Element("connotedeleted") ?? "n").ToUpper() == "Y",
							  //suburb = (string)ccr.XPathSelectElement("//recaddr/add3"),
							  labels = ccr.XPathSelectElements("//labels/labelno_tracking").Select(_ => _.Value.ToString()).ToList()
						  })
						.GroupBy(_ => _.conno).Select(_ => _.FirstOrDefault()).OrderBy(_ => _.carriername).ThenBy(_ => _.conno).ToList();


				foreach (var x in xx)
				{
					if (x.connotedeleted)
					{
						try
						{
							var deletedConnote = order.ConNotes.Where(_ => _.ConNote == x.conno && _.CarrierName == x.carriername && _.CarrierService == x.service).FirstOrDefault();

							if (deletedConnote != null)
							{
								order.ConNotes.Remove(deletedConnote);
							}
						}
						catch (Exception ex)
						{
							Log.Error("Deleteing deleted connote", ex.Message);
						}
					}
					else
					{
						bool connotesExist = order.ConNotes.Any(_ => _.ConNote == x.conno && _.CarrierName == x.carriername && _.CarrierService == x.service);
						if (!connotesExist)
						{
							var cn = new lep.order.impl.OrderConNote()
							{
								CarrierName = x.carriername,
								CarrierService = x.service,
								ConNote = x.conno ?? "-",
								TrackingLabels = x.labels,
								IsEmailGenerated = 0,
								Order = order,
							};
							order.ConNotes.Add(cn);
						}
					}
				}
				base.Save(order);
				return null;
			}
			catch (Exception ex)
			{
				Log.Error(ex.Message, ex);
				return null;
			}
		}

		private XElement CreateSmartFreightReceiversAddressXmlNode(IPhysicalAddress address)
		{
			/*
            <recaddr> N/A S Start of Receiver Address
                <add1> String[30] M Address Line 1. Normally used for Lvl, Suite, Flr.
                        Populate with Street details if no Lvl, Suite or Flr
                <add2> String[30] O Address Line 2. Street address
                <add3> String[50] M Town/Suburb (Refer to Address section of this  document). Length is Country dependant
                <add4> String[25] M State (Refer to Address section of this document). Length is Country dependant
                <add5> String[10] M Postcode (Refer to Address section of this    document). Length is Country dependant
                <add6> String[30] O Country (Refer to Address section of this document)
                <type> String[50] O R – Residential
                B – Business
                PO – PO box
                Default is Business
            </recaddr> N/A E End of Receiver Address
            */

			var recaddr = new XElement("recaddr");

			var country = "AUSTRALIA";
			if (address.Country.Contains("NZ") || address.Country.Contains("New"))
			{
				country = "NEW ZEALAND";
			}

			address.Address1 = address.Address1.NullIf() ?? "100 SOMTHING";

			recaddr.Add(__("add1", address.Address1.Left(30)));
			recaddr.Add(__("add2", address.Address2.Left(30)));

			recaddr.Add(__("add3", address.City.Left(50))); // <add3> String[50] M Town/Suburb
			recaddr.Add(__("add4", address.State.Left(25))); //  <add4> String[25] M State
			recaddr.Add(__("add5", address.Postcode.Left(10))); //  <add5> String[10] M Postcode
			recaddr.Add(__("add6", country.Left(30)));   //  <add6> String[30] O Country
			return recaddr;
		}



		public IList<Rate> GetAvailableRatesBase(Facility? senderFacility,
										   IPhysicalAddress deliveryAddress,
										   IList<Package> packagesToSend,
										   bool packWithoutPallets = false,
										   ICustomerUser customer = null)
		{           // add freight items

			//bool includePickup = true, bool includeOther = false

			if (packagesToSend == null)
				return null;

			var sender = LepGlobal.Instance.TestBox ? "TEST" : "LEP";
			sender = "LEP";
			sender += GetSendingFacilityState(senderFacility);   // TESTQLD, TESTVIC, LEPQLD, LEPVIC

			string sfId, sfKey;
			GetSmartFreightIdKey(sender, out sfId, out sfKey);

			var ratesResult = new List<Rate>();
			double totalWeight = 0.0;
			//var ifreight = new lep.CompData.IiFreightChargeEnquiryServiceClient();

			System.Net.ServicePointManager.Expect100Continue = false;

			#region construct request

			Func<string, object, XElement> __ = (n, c) => new XElement(n, c);

			var req = new XElement("connote");

			req.Add(__("sendaccno", sender));
			req.Add(__("matchsendertoaccountno", "YES"));
			req.Add(__("applymarkup", "Yes"));
			// Your account number for the receiver, this provides an
			// easy way for you to associate this receiver with a customer
			// or other application

			if (customer != null)
			{
				req.Add(__("recaccno", customer.Id));

				// <recname> String[50] M Receiver Company Name
				req.Add(__("recname", customer.Name));

				// adhocrec: Flag to save the receiver details into the Receiver section
				// of the SmartFreight database or not. This is useful to consider if there is a
				// requirement to generate consignments without using integration (ie. manually)
				// where it is easier to pull the receiver information from the database rather
				// than entering all details manually. Possible values:
				// Yes - This is an ad hoc receiver and should not be saved into the database
				req.Add(__("adhocrec", "Yes"));
			}

			// recaddr Marks the start of the receiver address
			var recaddr = CreateSmartFreightReceiversAddressXmlNode(deliveryAddress);
			req.Add(recaddr);



			bool hasSkid = false;
			//http://www.abctransport.com.au/cubic-calculator
			foreach (var p in packagesToSend)
			{
				var cubicM3 = (double)((p.Width / 1000d) * (p.Height / 1000d) * (p.Depth / 1000d));
				var cubicWeight = cubicM3 * 250;

				var deadWeight = (double)(p.Weight);
				var weight = Math.Max(deadWeight, cubicWeight);
				totalWeight += weight;

				var Cubic = cubicM3;
				var XCM = p.Width / 10d;
				var YCM = p.Height / 10d;
				var ZCM = p.Depth / 10d;

				var desc = "CARTON";
				if (p.Carton.Contains("Skid"))
				{
					hasSkid = true;
					desc = "SKID";
				}


				var f = new XElement("freightlinedetails");
				f.Add(__("desc", desc));
				f.Add(__("amt", 1));
				f.Add(__("wgt", deadWeight));
				f.Add(__("cube", cubicM3));
				f.Add(__("len", XCM));
				f.Add(__("wdt", YCM));
				f.Add(__("hgt", ZCM));
				req.Add(f);
			}

			var consignmentxml = req.ToString();




			#endregion construct request


			try
			{
				var responseStr = _iSmartFreightDeliveryOptions.GetDeliveryOptions(sfId, sfKey, consignmentxml.GetHashCode().ToString(), consignmentxml);
				var response = XDocument.Parse(responseStr);

				Log.Information(string.Format("0REQUEST: {0} \n--------------\n\n   RESPONSE: {1}", req.ToString(), response.ToString()));

				var deliveryOptions = (from oo in response.Descendants("otheroptions")
									   from res in oo.Descendants("DeliveryOption")
									   let carrierName = (string)res.Element("carriername")
									   where carrierName != "FASTWAY" && carrierName != "TOLL"
									   select new Rate
									   {
										   CarrierName = (string)res.Element("carriername"),
										   ServiceName = (string)res.Element("service"),
										   CustomerCharge = Double.Parse((string)res.Element("secondarypricing")),
										   CarrierAccount = (string)res.Element("accno") ?? "",
										   EstDelivery = (string)res.Element("estdelivery") ?? ""
									   }).OrderBy(_ => _.CustomerCharge);

				ratesResult.AddRange(deliveryOptions);

				var sfRequest = req.ToString(SaveOptions.None);
				var sfResponse = response.ToString(SaveOptions.None);


				//if marginRate is negative, apply freight discount to least expensive - then subtract to all other rates returned.
				//if marginRate is positive then add margin to all rates returned.
				//if marginRate is 0 don't change rates at all, but still change the description for FastWays hack.
				if (ratesResult.Count > 0)
				{
					if (hasSkid)
					{
						ratesResult = ratesResult.Where(_ => !_.CarrierName.Is("AUSTRALIA POST",  "COURIERS PLS II")).ToList(); //"FASTWAY",
						ratesResult = ratesResult.Where(_ => !(_.CarrierName.Is("STARTRACK", "STARTRACK EXP") && _.ServiceName.Is("FP PREMIUM", "PREMIUM"))).ToList(); // todo refine later
					}

					ApplyTNTRelatedLogic(ratesResult, totalWeight);

					// LORD-1119 Add $1 charge per carton if customer selects Pack without pallets
					if (packWithoutPallets)
					{
						ratesResult.RemoveAll(_ => _.CarrierName == CourierType.TOLLNQX);
						var countOfLooseCarton = packagesToSend.Count();
						var chargePerLooseCarton = 1.0;
						var looseCartonCharge = countOfLooseCarton * chargePerLooseCarton;
						ratesResult.ForEach(rate => rate.CustomerCharge = rate.CustomerCharge + looseCartonCharge);
					}
				}
			}
			catch (Exception ex)
			{
				Log.Error(ex.Message, ex);
				var m = ex.InnerException;
				//throw ex;
			}

			ratesResult = ratesResult.OrderBy(x => (x.CustomerCharge)).ToList();

			return ratesResult;

		}

		private static void ApplyTNTRelatedLogic(List<Rate> ratesResult, double totalWeight)
		{
			ratesResult.ForEach(rate =>
			{
				if (rate.CarrierName == "TNT")
				{
					if (totalWeight > 30.0 && totalWeight < 100.0)
					{
						rate.CustomerCharge += 55;
					}
					else if (totalWeight > 100.0)
					{
						rate.CustomerCharge += 175;
					}
				}
			});
		}

		public void MakeLeastPricedRateZeroBasedOnMargin(double marginRate, List<Rate> ratesResult)
		{
			if (marginRate != 0.000 && ratesResult != null && ratesResult.Any())
			{
				var leastExpensiveRate1 = ratesResult.Min(x => x.CustomerCharge);
				var adjustmentPrice = (leastExpensiveRate1 * Math.Abs(marginRate)) / 100;
				adjustmentPrice = adjustmentPrice * (marginRate < 0 ? -1 : 1);
				ratesResult.ForEach(rate =>
				{
					rate.CustomerCharge = (double)(rate.CustomerCharge + adjustmentPrice);
				});
			}

		}


		//TODO: make this smaller, try using GetRatesBase
		public IList<Rate> GetAvailableRates(IOrder order, Facility? senderFacility, bool includePickup = true, bool includeOther = false)
		{
			Log.Information(string.Format("Order # {0}, Facility: {1}", order.Id, senderFacility));

			var sender = LepGlobal.Instance.TestBox ? "TEST" : "LEP";
			sender = "LEP";
			sender += GetSendingFacilityState(senderFacility);   // TESTQLD, TESTVIC, LEPQLD, LEPVIC

			string sfId, sfKey;
			GetSmartFreightIdKey(sender, out sfId, out sfKey);

			var ratesResult = new List<Rate>();
			double totalWeight = 0.0;
			//var ifreight = new lep.CompData.IiFreightChargeEnquiryServiceClient();

			System.Net.ServicePointManager.Expect100Continue = false;

			#region construct request

			Func<string, object, XElement> __ = (n, c) => new XElement(n, c);

			var req = new XElement("connote");

			req.Add(__("sendaccno", sender));
			req.Add(__("matchsendertoaccountno", "YES"));
			req.Add(__("applymarkup", "Yes"));
			// Your account number for the receiver, this provides an
			// easy way for you to associate this receiver with a customer
			// or other application

			if (order.Customer != null)
			{
				req.Add(__("recaccno", order.Customer?.Id ?? 0));

				// <recname> String[50] M Receiver Company Name
				req.Add(__("recname", order.ReceiverName));

				// adhocrec: Flag to save the receiver details into the Receiver section
				// of the SmartFreight database or not. This is useful to consider if there is a
				// requirement to generate consignments without using integration (ie. manually)
				// where it is easier to pull the receiver information from the database rather
				// than entering all details manually. Possible values:
				// Yes - This is an ad hoc receiver and should not be saved into the database
				req.Add(__("adhocrec", "Yes"));
			}



			// recaddr Marks the start of the receiver address
			var recaddr = CreateSmartFreightReceiversAddressXmlNode(order.DeliveryAddress);
			req.Add(recaddr);

			// add freight items
			var packagesToSend = order.PackDetail.GetPackages(null);
			if (packagesToSend == null)
				return null;

			bool hasSkid = false;
			//http://www.abctransport.com.au/cubic-calculator
			foreach (var p in packagesToSend)
			{
				var cubicM3 = (double)((p.Width / 1000d) * (p.Height / 1000d) * (p.Depth / 1000d));
				var cubicWeight = cubicM3 * 250;

				var deadWeight = (double)(p.Weight);
				var weight = Math.Max(deadWeight, cubicWeight);
				totalWeight += weight;

				var Cubic = cubicM3;
				var XCM = p.Width / 10d;
				var YCM = p.Height / 10d;
				var ZCM = p.Depth / 10d;

				var desc = "CARTON";
				if (p.Carton.Contains("Skid"))
				{
					hasSkid = true;
					desc = "SKID";
				}


				var f = new XElement("freightlinedetails");
				f.Add(__("desc", desc));
				f.Add(__("amt", 1));
				f.Add(__("wgt", deadWeight));
				f.Add(__("cube", cubicM3));
				f.Add(__("len", XCM));
				f.Add(__("wdt", YCM));
				f.Add(__("hgt", ZCM));
				req.Add(f);
			}

			var consignmentxml = req.ToString();

			#endregion construct request


			try
			{
				var watch = Stopwatch.StartNew();
				Log.Information(consignmentxml);
				var responseStr = _iSmartFreightDeliveryOptions.GetDeliveryOptions(sfId, sfKey, order.Id.ToString(), consignmentxml);

				var response = XDocument.Parse(responseStr);

				var q = (from oo in response.Descendants("otheroptions")
						 from res in oo.Descendants("DeliveryOption")
							 //orderby Double.Parse((string)res.Element("primarypricing"))
						 select new Rate
						 {
							 CarrierName = (string)res.Element("carriername"),
							 ServiceName = (string)res.Element("service"),
							 CustomerCharge = Double.Parse((string)res.Element("secondarypricing")),
							 CarrierAccount = (string)res.Element("accno") ?? "",
							 EstDelivery = (string)res.Element("estdelivery") ?? ""
						 }).OrderBy(_ => _.CustomerCharge).ToList();
				var q2 = q.Where(_ => _.CarrierName != "FASTWAY").ToList();
				ratesResult.AddRange(q2);


				if (!string.IsNullOrEmpty(order.DeliveryAddress.Postcode))
				// or other reason for which  to bypass calling
				{
					//result = TryGetRatesFromMultipleICompDataEndpoints(fcr);
				}

				watch.Stop();

				var sfRequest = req.ToString(SaveOptions.None);
				var sfResponse = response.ToString(SaveOptions.None);
				var orderId = order.Id;

				Log.Debug("SmartFreight {orderId}  {@sfRequest} {@sfResponse}", orderId, sfRequest, sfResponse);


				//if marginRate is negative, apply freight discount to least expensive - then subtract to all other rates returned.
				//if marginRate is positive then add margin to all rates returned.
				//if marginRate is 0 don't change rates at all, but still change the description for FastWays hack.
				if (ratesResult.Count > 0)
				{
					if (hasSkid)
					{
						ratesResult = ratesResult.Where(_ => !_.CarrierName.Is("AUSTRALIA POST", "FASTWAY", "COURIERS PLS II")).ToList();
						ratesResult = ratesResult.Where(_ => !(_.CarrierName.Is("STARTRACK", "STARTRACK EXP") && _.ServiceName.Is("FP PREMIUM", "PREMIUM"))).ToList(); // todo refine later
					}


					ratesResult.ForEach(rate =>
					{
						if (rate.CarrierName == "TNT")
						{
							if (totalWeight > 30.0 && totalWeight < 100.0)
							{
								rate.CustomerCharge += 55;
							}
							else if (totalWeight > 100.0)
							{
								rate.CustomerCharge += 175;
							}
						}
					});

					// LORD-1119 Add $1 charge per carton if customer selects Pack without pallets
					if (order.PackWithoutPallets)
					{
						ratesResult.RemoveAll(_ => _.CarrierName == CourierType.TOLLNQX);


						var countOfLooseCarton = packagesToSend.Count();
						var chargePerLooseCarton = 1.0;
						var looseCartonCharge = countOfLooseCarton * chargePerLooseCarton;
						ratesResult.ForEach(rate => rate.CustomerCharge = rate.CustomerCharge + looseCartonCharge);
					}


					//if marginRate is negative, apply freight discount to least expensive - then subtract to all other rates returned.
					//if marginRate is positive then add margin to all rates returned.
					//if marginRate is 0 don't change rates at all
					if (order.Customer != null)
					{
						double marginRate = 0.0;
						try
						{
							marginRate = (double)GetCustomerFreightMarginFromCode(order.Customer.FreightPriceCode);
						}
						catch (Exception ex)
						{
							Log.Error(ex.Message, ex);
						}
						var leastExpensiveRate1 = ratesResult.Min(x => x.CustomerCharge);
						var adjustmentPrice = (leastExpensiveRate1 * Math.Abs(marginRate)) / 100;
						adjustmentPrice = adjustmentPrice * (marginRate < 0 ? -1 : 1);
						ratesResult.ForEach(rate =>
						{
							rate.CustomerCharge = (double)(rate.CustomerCharge + adjustmentPrice);
						});
					}
					//ratesResult.ForEach(rate =>
					//{
					//	rate.CustomerCharge = (double)Math.Ceiling(rate.CustomerCharge);
					//});

					if (includeOther)
					{
						ratesResult.Add(new Rate() { CarrierName = "Other", ServiceName = "Other" });
						ratesResult.Add(new Rate()
						{
							CarrierName = "None",
							ServiceName = "None",
							CustomerCharge = 0,
							CarrierAccount = ""
						});
					}

					if (senderFacility != null)
					{
						if (senderFacility == Facility.FG && !order.PackDetail.FGCourier.IsNone)
						{
							ratesResult.Add(new Rate()
							{
								CarrierName = order.PackDetail.FGCourier.CourierName,
								ServiceName = order.PackDetail.FGCourier.ServiceName,
								CustomerCharge = (double)order.PackDetail.Price.Value,
								CarrierAccount = ""
							});
						}
						if (senderFacility == Facility.PM && !order.PackDetail.PMCourier.IsNone)
						{
							ratesResult.Add(new Rate()
							{
								CarrierName = order.PackDetail.PMCourier.CourierName,
								ServiceName = order.PackDetail.PMCourier.ServiceName,
								CustomerCharge = (double)order.PackDetail.Price.Value,
								CarrierAccount = ""
							});
						}
					}


				}
			}
			catch (Exception ex)
			{
				Log.Error(ex.Message, ex);
				var m = ex.InnerException;
				//throw ex;
			}

			ratesResult = ratesResult.OrderBy(x => (x.CustomerCharge)).ToList();

			// PO BOX can only be delivered to to Australia Post
			var address = order.DeliveryAddress?.ToStringFormatted().ToUpper();
			if (address != null && (address.Contains("PO BOX") || address.Contains("POBOX")))
			{
				ratesResult.RemoveAll(x => !x.CarrierName.Equals("australia post", StringComparison.OrdinalIgnoreCase));
			}

			bool includePickupFG = order.Jobs.All(j => JobApplication.IsFacilityAvailable(Facility.FG, j));
			bool includePickupPM = order.Jobs.All(j => JobApplication.IsFacilityAvailable(Facility.PM, j));
			// !order.Jobs.Any(job => job.IsDigital()); //make sure there are no digital or Business cards if Port Melbourne - as can't pickup!

			#region add pickup rate TODO get rid of pickup

			//if not same postcode
			if (includePickup)
			{
				var pickupFGCt = new CourierType(CourierType.CustomerPickupFG);
				var pickupPMCt = new CourierType(CourierType.CustomerPickupPM);

				if (includePickupFG)
				{
					Rate pickup = new Rate()
					{
						CarrierName = pickupFGCt.CourierName,
						ServiceName = pickupFGCt.ServiceName,
						ServiceCode = "",
						CustomerCharge = 0
					};
					ratesResult.Add(pickup);
				}

				if (includePickupPM)
				{
					Rate pickupPM = new Rate()
					{
						CarrierName = pickupPMCt.CourierName,
						ServiceName = pickupPMCt.ServiceName,

						ServiceCode = "",
						CustomerCharge = 0
					};
					ratesResult.Add(pickupPM);
				}
			}

			#endregion add pickup rate TODO get rid of pickup

			return ratesResult;
		}

		private static void GetSmartFreightIdKey(string sender, out string sfId, out string sfKey)
		{
			bool isQLD = sender.Contains("QLD");
			sfId = isQLD ? "NWM" : "NGU";
			sfKey = isQLD ? SF_QLD_KEY : SF_VIC_KEY;
		}

		private string GetSendingFacilityState(Facility? facility)
		{
			string sender = "QLD";
			if (facility != null)
			{
				sender = facility == Facility.FG ? "QLD" : "VIC";
			}
			return sender;
		}

		public void SetCustomerPreferredCourier(IOrder order, CourierType courier, decimal price)
		{
		}

		/// <summary>
		/// TODO : PULL Carriers and services from Compdata Supply master
		/// </summary>
		/// <param name="location"></param>
		/// <returns></returns>
		public IList<CourierType> GetAllCouriers(SiteLocation location)
		{
			var result = new List<CourierType>();

			if (location != SiteLocation.NZ)
			{
				//result.Add(CourierType.FastWay);
				result.Add(CourierType.StarTrack);
				result.Add(CourierType.AusPost);
			}

			result.Add(CourierType.TNT);

			return result;
		}

		public Dictionary<CourierType, decimal> GetAvailableRates2(IOrder order, Facility? facility)
		{
			var result = GetAvailableRates(order, facility, true);
			var uniqueSet = new Dictionary<CourierType,
			 decimal>();

			foreach (var d in result)
			{
				var key = new CourierType()
				{
					CourierName = d.CarrierName,
					ServiceName = d.ServiceName
				};
				var total = (decimal)(d.CustomerCharge);

				if (uniqueSet.ContainsKey(key))
				{
					uniqueSet[key] = Math.Max(uniqueSet[key], total);
				}
				else
				{
					uniqueSet.Add(key, total);
				}
			}
			return uniqueSet;
		}

		//very temporary - should really reference freight.GetCustomerAdjustedFreightPrice.
		private decimal GetCustomerAdjustedFreightPrice(Int32 CustomerID, decimal Price)
		{
			string sql = @"Select dbo.GetFreightPriceForCustomer(:CustomerID,:Price)";
			IQuery query = Session.CreateSQLQuery(sql);
			query.SetInt32("CustomerID", CustomerID);
			query.SetDecimal("Price", Price);
			return query.UniqueResult<decimal>();
		}

		public decimal GetCustomerFreightMarginFromCode(String freightCode)
		{
			var sql = @"SELECT CASE
                             WHEN EXISTS(SELECT 1 from dbo.CustomerFreightPricing where Code = :freightCode)
                       THEN(SELECT ISNULL(Margin, 0) from dbo.CustomerFreightPricing where Code = :freightCode)
                             ELSE 0
                             END ";

			IQuery query = Session.CreateSQLQuery(sql).SetAnsiString("freightCode", freightCode);
			var margin = Convert.ToDecimal(query.UniqueResult());
			return margin;
			//return query.UniqueResult<float>(); // throws error: ?
		}

		public IConfigurationApplication ConfigurationApplication { set; get; }
		public IJobApplication JobApplication { set; get; }
	}
}

namespace lep.SmartFreight
{
	[ServiceContract]
	public interface ISmartFreightDeliveryOptions
	{
		[OperationContract]
		string GetDeliveryOptions(string id, string passwd, string reference, string consignment);
	}
}
