using MYOB.AccountRight.SDK;
using MYOB.AccountRight.SDK.Contracts;
using Newtonsoft.Json;
using Microsoft.Extensions.Logging;

namespace LepInvoicer.Implementations.Myob;

/// <summary>
/// OAuth key service for persisting MYOB OAuth tokens
/// </summary>
public class OAuthKeyService : MYOB.AccountRight.SDK.IOAuthKeyService, Interfaces.IOAuthKeyService
{
	private readonly string _tokensFile;
	private readonly ILogger<OAuthKeyService> _logger;
	private OAuthTokens _tokens;

	public OAuthKeyService(ILogger<OAuthKeyService> logger)
	{
		_logger = logger;

		// Set tokens file path relative to the application directory
		var appDirectory = Path.GetDirectoryName(System.Reflection.Assembly.GetExecutingAssembly().Location)
						  ?? Environment.CurrentDirectory;
		_tokensFile = Path.Combine(appDirectory, "Tokens.json");

		_logger.LogDebug("OAuth tokens file path: {TokensFile}", _tokensFile);
		ReadFromFile();
	}

	/// <summary>
	/// OAuth response property that holds the tokens
	/// </summary>
	public OAuthTokens OAuthResponse
	{
		get => _tokens;
		set
		{
			_tokens = value;
			SaveToFile();
		}
	}



	/// <summary>
	/// Read tokens from file
	/// </summary>
	private void ReadFromFile()
	{
		try
		{
			if (!File.Exists(_tokensFile))
			{
				_logger.LogInformation("OAuth tokens file not found, will create new one at: {TokensFile}", _tokensFile);
				_tokens = null;
				return;
			}

			var json = File.ReadAllText(_tokensFile);
			_tokens = JsonConvert.DeserializeObject<OAuthTokens>(json);
			_logger.LogInformation("OAuth tokens loaded from file: {TokensFile}", _tokensFile);
		}
		catch (FileNotFoundException)
		{
			_logger.LogInformation("OAuth tokens file not found, will create new one");
			_tokens = null;
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "Failed to read OAuth tokens from file");
			_tokens = null;
		}
	}

	/// <summary>
	/// Save tokens to file
	/// </summary>
	private void SaveToFile()
	{
		try
		{
			// Ensure directory exists
			var directory = Path.GetDirectoryName(_tokensFile);
			if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
			{
				Directory.CreateDirectory(directory);
				_logger.LogInformation("Created directory for tokens file: {Directory}", directory);
			}

			var json = JsonConvert.SerializeObject(_tokens, Formatting.Indented);
			File.WriteAllText(_tokensFile, json);
			_logger.LogInformation("OAuth tokens saved to file: {TokensFile}", _tokensFile);
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "Failed to save OAuth tokens to file");
		}
	}
}
