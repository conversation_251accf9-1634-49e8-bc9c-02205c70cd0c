-- Check the current state of the problematic refunds
SELECT 
    Id,
    Type,
    Amount,
    Invoiced,
    DateCreated,
    CustomerId
FROM [OrderCredit]
WHERE Id IN (1801, 1802, 1803, 1804, 1805, 1806, 1807, 1812, 1814, 1815, 1816, 1817, 1818, 1824, 1836, 1837)
    AND Type = 'S'
ORDER BY Id;

-- Check what the query is actually selecting
SELECT TOP 20
    Id,
    Type,
    Amount,
    Invoiced,
    DateCreated,
    CustomerId
FROM [OrderCredit]
WHERE Type = 'S'
    AND (Invoiced IS NULL OR Invoiced = 0 OR Invoiced = '')
ORDER BY DateCreated;
