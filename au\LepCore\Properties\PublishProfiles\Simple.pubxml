<?xml version="1.0" encoding="utf-8"?>
<!--
https://go.microsoft.com/fwlink/?LinkID=208121.
-->
<Project>
  <PropertyGroup>
    <DeleteExistingFiles>true</DeleteExistingFiles>
    <ExcludeApp_Data>false</ExcludeApp_Data>
    <LaunchSiteAfterPublish>true</LaunchSiteAfterPublish>
    <LastUsedBuildConfiguration>Release</LastUsedBuildConfiguration>
    <LastUsedPlatform>Any CPU</LastUsedPlatform>
    <PublishProvider>FileSystem</PublishProvider>
    <PublishUrl>C:\FS\LEPCoreBuilds</PublishUrl>
    <WebPublishMethod>FileSystem</WebPublishMethod>
    <_TargetId>Folder</_TargetId>
    <SiteUrlToLaunchAfterPublish />
    <TargetFramework>net8.0-windows7.0</TargetFramework>
    <ProjectGuid>cf4055c3-8ea1-45da-ab5e-f9e6f1745d57</ProjectGuid>
    <SelfContained>false</SelfContained>
    <PublishReadyToRun>false</PublishReadyToRun>
    <PublishSingleFile>false</PublishSingleFile>

    <!-- Exclude configuration files from publish -->
    <ExcludeConfigFilesOnPublish>true</ExcludeConfigFilesOnPublish>
  </PropertyGroup>

  <!-- Explicitly exclude specific files -->
  <ItemGroup>
    <Content Update="web.config" CopyToPublishDirectory="Never" />
    <Content Update="appsettings.*.json" CopyToPublishDirectory="Never" />
  </ItemGroup>

  <!-- Custom target to run gulp release before publish -->
  <Target Name="RunGulpRelease" BeforeTargets="BeforePublish">
    <Message Importance="high" Text="Running gulp release for frontend assets..." />
    <Exec WorkingDirectory="$(ProjectDir)FrontEnd" Command="gulp release" ConsoleToMSBuild="true" />
    <Message Importance="high" Text="Frontend assets built successfully." />
  </Target>
</Project>
