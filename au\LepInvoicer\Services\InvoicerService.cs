using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using LepInvoicer.Configuration;
using System.Diagnostics;

namespace LepInvoicer.Services;

/// <summary>
/// Main invoicer service implementation with improved error handling and structure
/// </summary>
public class InvoicerService : IInvoicerService
{
    private readonly ILogger<InvoicerService> _logger;
    private readonly InvoicerConfiguration _config;
    private readonly IMYOBService _myobService;
    private readonly IEmailService _emailService;
    private readonly IPdfService _pdfService;
    private readonly IDatabaseService _databaseService;

    public InvoicerService(
        ILogger<InvoicerService> logger,
        IOptions<InvoicerConfiguration> config,
        IMYOBService myobService,
        IEmailService emailService,
        IPdfService pdfService,
        IDatabaseService databaseService)
    {
        _logger = logger;
        _config = config.Value;
        _myobService = myobService;
        _emailService = emailService;
        _pdfService = pdfService;
        _databaseService = databaseService;
    }

    public async Task<int> RunInvoicerAsync()
    {
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            _logger.LogInformation("Starting LEP Invoicer at {StartTime}", DateTime.Now);

            // Initialize services
            await InitializeServicesAsync();

            // Process orders
            var orderResults = await ProcessOrderInvoicesAsync();
            
            // Process credits
            var creditResults = await ProcessCreditInvoicesAsync();
            
            // Process refunds
            var refundResults = await ProcessRefundInvoicesAsync();

            // Cleanup
            await _databaseService.CleanupInvoicerLogsAsync();

            stopwatch.Stop();
            
            _logger.LogInformation("LEP Invoicer completed successfully in {ElapsedTime}ms. Orders: {OrderCount}, Credits: {CreditCount}, Refunds: {RefundCount}",
                stopwatch.ElapsedMilliseconds, orderResults.ProcessedCount, creditResults.ProcessedCount, refundResults.ProcessedCount);

            return 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "LEP Invoicer failed after {ElapsedTime}ms", stopwatch.ElapsedMilliseconds);
            return 1;
        }
        finally
        {
            _databaseService?.Dispose();
        }
    }

    private async Task InitializeServicesAsync()
    {
        _logger.LogInformation("Initializing services...");
        
        await _databaseService.InitializeAsync();
        await _myobService.InitializeAsync();
        
        // Setup FastReport
        FastReport.Utils.Config.FontListFolder = _config.FontListFolder;
        
        _logger.LogInformation("Services initialized successfully");
    }

    private async Task<ProcessingResult> ProcessOrderInvoicesAsync()
    {
        // Early return if disabled
        if (!_config.CreateOrderInvoice)
        {
            _logger.LogInformation("Order invoice creation is disabled");
            return new ProcessingResult();
        }

        _logger.LogInformation("Processing order invoices...");

        var orders = await _databaseService.GetOrdersToInvoiceAsync(_config.InvoiceBatchSize);
        _logger.LogInformation("Found {OrderCount} orders to process", orders.Count);

        var result = new ProcessingResult();

        foreach (var orderInfo in orders)
        {
            try
            {
                var order = await _databaseService.GetOrderAsync(orderInfo.Key);

                var success = await ProcessSingleOrderAsync(order);
                if (success)
                {
                    result.SuccessCount++;
                    await _databaseService.MarkOrderAsInvoicedAsync(orderInfo.Key); // Use orderInfo.Key instead of order.Id
                }
                else
                {
                    result.FailureCount++;
                }

                result.ProcessedCount++;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to process order {OrderId}", orderInfo.Key);
                result.FailureCount++;
                result.ProcessedCount++;

                await _databaseService.MarkOrderAsFailedAsync(orderInfo.Key, ex.Message);
            }
        }

        _logger.LogInformation("Order processing completed. Processed: {ProcessedCount}, Success: {SuccessCount}, Failed: {FailureCount}",
            result.ProcessedCount, result.SuccessCount, result.FailureCount);

        return result;
    }

    private async Task<bool> ProcessSingleOrderAsync(IOrder order)
    {
        // TODO: Replace with actual order properties when real types are available
        _logger.LogInformation("Processing order {OrderId}", "TBD");

        try
        {
            // Early return if validation fails
            if (!ValidateOrder(order))
                return false;

            // Early return if MYOB invoice creation fails
            var myobSuccess = await _myobService.CreateOrderInvoiceAsync(order);
            if (!myobSuccess)
            {
                _logger.LogWarning("Failed to create MYOB invoice for order {OrderId}", "TBD");
                return false;
            }

            // Generate PDF if enabled
            if (_config.CreatePdfInvoice)
            {
                await GenerateAndProcessPdfAsync(order);
            }

            // Log success (TODO: Use actual order properties)
            await _databaseService.LogInvoicingResultAsync(
                1, // order.Id,
                1, // order.Jobs.Count,
                100m, // order.PriceOfJobs ?? 0,
                DateTime.Now, // order.FinishDate ?? DateTime.Now,
                true,
                null);

            _logger.LogInformation("Successfully processed order {OrderId}", "TBD");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing order {OrderId}", "TBD");

            await _databaseService.LogInvoicingResultAsync(
                1, // order.Id,
                1, // order.Jobs.Count,
                100m, // order.PriceOfJobs ?? 0,
                DateTime.Now, // order.FinishDate ?? DateTime.Now,
                false,
                ex.Message);

            return false;
        }
    }

    private bool ValidateOrder(IOrder order)
    {
        // TODO: Implement actual validation when real order type is available
        _logger.LogInformation("Validating order (placeholder implementation)");
        return true;
    }

    private async Task GenerateAndProcessPdfAsync(IOrder order)
    {
        try
        {
            // TODO: Use actual order properties when available
            var finishDate = DateTime.Now.ToString("yyyy/MMM/dd").Replace(".", "");
            var pdfDirectory = Path.Combine(_config.PdfFolder, finishDate);
            Directory.CreateDirectory(pdfDirectory);

            var pdfPath = Path.Combine(pdfDirectory, "O1.pdf"); // $"O{order.Id}.pdf"

            await _pdfService.GenerateOrderInvoicePdfAsync(order, pdfPath);

            // Copy to order folder
            await CopyPdfToOrderFolderAsync(order, pdfPath);

            // Send email if enabled
            if (_config.EmailPdfInvoice)
            {
                await SendInvoiceEmailAsync(order, pdfPath);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to generate/process PDF for order {OrderId}", "TBD");
            // Don't fail the entire order for PDF issues
        }
    }

    private async Task CopyPdfToOrderFolderAsync(IOrder order, string pdfPath)
    {
        try
        {
            // TODO: Use actual order properties when available
            var orderFolder = Path.Combine(_config.DataDirectoryFullName, "orders",
                DateTime.Now.ToString("yyyyMMdd"), "ORDER1", "Extrafiles");

            Directory.CreateDirectory(orderFolder);

            var destinationPath = Path.Combine(orderFolder, "O1.pdf");
            File.Copy(pdfPath, destinationPath, true);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to copy PDF to order folder for order {OrderId}", "TBD");
        }
    }

    private async Task SendInvoiceEmailAsync(IOrder order, string pdfPath)
    {
        try
        {
            // TODO: Use actual customer properties when available
            var emailAddress = _config.EmailPdfAddress ?? "<EMAIL>";

            // Early return if no email address
            if (string.IsNullOrEmpty(emailAddress))
            {
                _logger.LogWarning("No email address found for order {OrderId}", "TBD");
                return;
            }

            var success = await _emailService.SendInvoiceEmailAsync(emailAddress, "O1", pdfPath);

            // Log result based on success
            if (success)
            {
                _logger.LogInformation("Email sent successfully for order {OrderId} to {Email}", "TBD", emailAddress);
            }
            else
            {
                _logger.LogWarning("Failed to send email for order {OrderId} to {Email}", "TBD", emailAddress);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending email for order {OrderId}", "TBD");
        }
    }

    private async Task<ProcessingResult> ProcessCreditInvoicesAsync()
    {
        // Early return if disabled
        if (!_config.CreateRefundInvoice)
            return new ProcessingResult();

        _logger.LogInformation("Processing credit invoices...");

        var credits = await _databaseService.GetCreditsToInvoiceAsync(_config.RefundBatchSize);
        var result = new ProcessingResult();

        foreach (var credit in credits)
        {
            try
            {
                var success = await _myobService.CreateCreditInvoiceAsync(credit);
                if (success)
                {
                    await _databaseService.MarkCreditAsInvoicedAsync(1); // TODO: Use actual credit.Id
                    result.SuccessCount++;
                }
                else
                {
                    result.FailureCount++;
                }
                result.ProcessedCount++;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to process credit {CreditId}", "TBD");
                result.FailureCount++;
                result.ProcessedCount++;
            }
        }

        return result;
    }

    private async Task<ProcessingResult> ProcessRefundInvoicesAsync()
    {
        // Early return if disabled
        if (!_config.CreateRefundInvoice)
            return new ProcessingResult();

        _logger.LogInformation("Processing refund invoices...");

        var refunds = await _databaseService.GetRefundsToInvoiceAsync(_config.RefundBatchSize);
        var result = new ProcessingResult();

        foreach (var refund in refunds)
        {
            try
            {
                var success = await _myobService.CreateRefundInvoiceAsync(refund);
                if (success)
                {
                    await _databaseService.MarkCreditAsInvoicedAsync(1); // TODO: Use actual refund.Id
                    result.SuccessCount++;
                }
                else
                {
                    result.FailureCount++;
                }
                result.ProcessedCount++;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to process refund {RefundId}", "TBD");
                result.FailureCount++;
                result.ProcessedCount++;
            }
        }

        return result;
    }
}

/// <summary>
/// Result of processing a batch of items
/// </summary>
public class ProcessingResult
{
    public int ProcessedCount { get; set; }
    public int SuccessCount { get; set; }
    public int FailureCount { get; set; }
}
