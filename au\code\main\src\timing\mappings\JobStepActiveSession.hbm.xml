<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2"
                   namespace="lep.timing.entities"
                   assembly="lep"
                   auto-import="true"
                   default-cascade="none">

    <class name="JobStepActiveSession" table="JobStepActiveSession">
        <cache usage="read-write" />

        <id name="Id" type="Int32" unsaved-value="0">
            <generator class="identity" />
        </id>

        <property name="JobId" column="JobId" type="Int32" not-null="true" />
        <property name="UserId" column="UserId" type="Int32" not-null="true" />
        <property name="Status" column="Status" type="String" length="24" not-null="true" />
        <property name="SessionId" column="SessionId" type="Guid" not-null="true" />
        <property name="CurrentState" column="CurrentState" type="lep.timing.enums.TimingSessionState, lep" not-null="true" />
        <property name="StartTime" column="StartTime" type="DateTime" not-null="true" />
        <property name="LastEventTime" column="LastEventTime" type="DateTime" not-null="true" />
        <property name="TotalPausedDuration" column="TotalPausedDuration" type="Int32" not-null="true" />
        <property name="IsActive" column="IsActive" type="Boolean" not-null="true" />
        <property name="DateCreated" column="DateCreated" type="DateTime" not-null="true" />
        <property name="DateModified" column="DateModified" type="DateTime" not-null="true" />

        <!-- Navigation properties -->
        <many-to-one name="Job" column="JobId" class="lep.job.impl.Job, lep" insert="false" update="false" />
        <many-to-one name="User" column="UserId" class="lep.user.IUser, lep" insert="false" update="false" />
    </class>
</hibernate-mapping>
