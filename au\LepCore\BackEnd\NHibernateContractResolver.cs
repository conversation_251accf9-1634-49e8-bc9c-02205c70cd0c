using Newtonsoft.Json.Serialization;
using NHibernate.Proxy;
using System;
using System.Linq;


namespace LepCore
{
	public class NHibernateContractResolver
	   : DefaultContractResolver
	//  : CamelCasePropertyNamesContractResolver
	{
		protected override JsonContract CreateContract(Type objectType)
		{
			if (typeof(INHibernateProxy).IsAssignableFrom(objectType))
			{
				var oType = objectType.GetInterfaces().FirstOrDefault(i => i.FullName.StartsWith("lep"));
				return oType != null ? base.CreateContract(oType) : base.CreateContract(objectType.BaseType);
			}

			return base.CreateContract(objectType);
		}
	}






	//public class SignalRContractResolver : IContractResolver
	//{
	//    private readonly Assembly _assembly;
	//    private readonly IContractResolver _camelCaseContractResolver;
	//    private readonly IContractResolver _defaultContractSerializer;

	//    public SignalRContractResolver ()
	//    {
	//        _defaultContractSerializer = new DefaultContractResolver();
	//        _camelCaseContractResolver = new CamelCasePropertyNamesContractResolver();
	//        _assembly = typeof(Connection).GetTypeInfo().Assembly;
	//    }


	//    public JsonContract ResolveContract (Type type)
	//    {
	//        if (type.GetTypeInfo().Assembly.Equals(_assembly))
	//            return _defaultContractSerializer.ResolveContract(type);

	//        return _camelCaseContractResolver.ResolveContract(type);
	//    }

	//}

}
