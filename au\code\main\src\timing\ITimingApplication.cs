using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using lep.timing.dto;
using lep.timing.entities;

namespace lep.timing
{
    public interface ITimingApplication
    {
        // Core timing operations
        Task<TimingActionResult> Start(TimingActionRequest request, int userId);
        Task<TimingActionResult> Pause(TimingActionRequest request, int userId);
        Task<TimingActionResult> Resume(TimingActionRequest request, int userId);
        Task<TimingActionResult> Finish(TimingActionRequest request, int userId);

        // State and validation
        Task<TimingButtonState> GetButtonState(int jobId, int userId, string status);
        Task<bool> CanUserStartTiming(int jobId, int userId, string status);
        Task<List<JobStepActiveSession>> GetUserActiveSessions(int userId);
        Task<JobStepActiveSession> GetActiveSession(int jobId, int userId, string status);

        // Instructions management
        Task<ProductionInstructionsDto> GetInstructions(string status, int? jobTypeId = null);
        Task<ProductionInstructionsDto> GetInstructionsForJob(int jobId, string status);
        Task<List<ProductionInstructionsDto>> GetAllInstructions();
        Task<ProductionInstructionsDto> SaveInstructions(ProductionInstructionsDto dto, int userId);
        Task DeleteInstructions(int id, int userId);

        // Cleanup and maintenance
        Task CleanupAbandonedSessions();
        Task AutoPauseInactiveSessions();
        Task<int> StopAllUserSessions(int userId);
        Task<int> StopAllSessionsForJob(int jobId);

        // Reporting and history
        Task<List<JobStepTimingEvent>> GetJobTimingHistory(int jobId);
        Task<List<JobStepTimingSummary>> GetJobTimingSummary(int jobId);
        Task<List<JobStepTimingEvent>> GetUserTimingHistory(int userId, DateTime from, DateTime to);
        Task<Dictionary<string, TimeSpan>> GetAverageTimeByStatus(DateTime from, DateTime to);
        Task<List<JobStepTimingEvent>> GetTimingEventsByDateRange(DateTime from, DateTime to);

        // Configuration
        Task<bool> IsTimingEnabled();
        Task<bool> RequiresInstructions();
        Task<bool> RequiresQualityChecks();
        Task<int> GetMaxSessionHours();
        Task<int> GetAutoPauseMinutes();
    }

    public interface IProductionInstructionsApplication
    {
        Task<ProductionInstructionsDto> GetInstructionsForJobStep(int jobId, string status);
        Task<List<ProductionInstructionsDto>> GetInstructionsByStatus(string status);
        Task<ProductionInstructionsDto> CreateInstructions(ProductionInstructionsDto dto, int userId);
        Task<ProductionInstructionsDto> UpdateInstructions(ProductionInstructionsDto dto, int userId);
        Task DeleteInstructions(int id, int userId);
        Task<List<string>> GetAllProductionStatuses();
        Task<List<ProductionInstructionsDto>> GetInstructionsByJobType(int jobTypeId);
    }
}
