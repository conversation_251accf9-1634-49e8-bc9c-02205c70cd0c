﻿using lep.configuration;
using lep.job;
using lep.order;
using lep.run;

using Serilog;
using RoundedRectangles;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Printing;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text;

namespace lep.despatch.impl.label
{
	public class BCReferenceLayout : PrintDocument, IDisposable
	{
		public int Cols = 6;
		public int Rows = 7;

		private Font smallFont = new Font("Tahoma", 11, FontStyle.Regular);
		private Font TitleFont = new Font("Arial", 14, FontStyle.Bold);
		private Pen penBlack3 = new Pen(Color.DarkGray, -1);
		private Font bigBoldFont = new Font("Arial", 20, FontStyle.Bold);
		//// private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);
		//public BCReferenceLayout()
		//{
		//	disposables.Add(smallFont);
		//	disposables.Add(TitleFont);
		//	disposables.Add(penBlack3);
		//	disposables.Add(bigBoldFont);
		//}
		private PrintType _printType = PrintType.O;

		public BCReferenceLayout(IRun r, IConfigurationApplication confApp, string printerAndTray, string fname)
		{
			run = r;
			_printType = run.PrintType;
			ConfigurationApplication = confApp;
			PrinterAndTray = printerAndTray;
			PrintFileName = fname;
			if (_printType == PrintType.O)
			{
				Cols = 6; Rows = 7;

				slotPos = new int[][,] 
							{
								new int[,]
								{
									{0, 0, 0, 0, 0, 0, 0},
									{0, 1, 8, 15, 22, 29, 36},
									{0, 2, 9, 16, 23, 30, 37},
									{0, 3, 10, 17, 24, 31, 38},
									{0, 4, 11, 18, 25, 32, 39},
									{0, 5, 12, 19, 26, 33, 40},
									{0, 6, 13, 20, 27, 34, 41},
									{0, 7, 14, 21, 28, 35, 42}
								},
								new int[,]
								{
									{0, 0, 0, 0, 0, 0, 0},
									{0, 36, 29, 22, 15, 8, 1},
									{0, 37, 30, 23, 16, 9, 2},
									{0, 38, 31, 24, 17, 10, 3},
									{0, 39, 32, 25, 18, 11, 4},
									{0, 40, 33, 26, 19, 12, 5},
									{0, 41, 34, 27, 20, 13, 6},
									{0, 42, 35, 28, 21, 14, 7}
								}
							};
			} 
			else if(_printType == PrintType.D)
			{
				Cols = 3; Rows = 7;
				slotPos = new int[][,]
							{
								new int[,]
								{
									{0, 0, 0,  0 },
									{0, 1, 8,  15},
									{0, 2, 9,  16},
									{0, 3, 10, 17},
									{0, 4, 11, 18},
									{0, 5, 12, 19},
									{0, 6, 13, 20},
									{0, 7, 14, 21}
								},
								new int[,]
								{
									{0, 0, 0, 0},
									{0, 15, 8, 1},
									{0, 16, 9, 2},
									{0, 17, 10, 3},
									{0, 18, 11, 4},
									{0, 19, 12, 5},
									{0, 20, 13, 6},
									{0, 21, 14, 7}
								}
							};
			}

			disposables.Add(smallFont);
			disposables.Add(TitleFont);
			disposables.Add(penBlack3);
			disposables.Add(bigBoldFont);
			SetupPrintProperties();
			FormatPrintData();
		}
		protected override void OnPrintPage(PrintPageEventArgs e)
		{
			var sfCenterClipped = new StringFormat
			{
				Alignment = StringAlignment.Center,
				FormatFlags = StringFormatFlags.NoWrap | StringFormatFlags.MeasureTrailingSpaces
			};
			try
			{
				var g = e.Graphics;
				base.OnPrintPage(e);

				#region determine page

				pageNumber++;
				var position = "";
				if (pageNumber == 1)
					position = "front";
				else if (pageNumber == 2)
					position = "back";
				else
				{
					e.HasMorePages = false;
					return;
				}

				#endregion determine page


				// draw unique customer names and number of orders in margin
				var titleHeight = g.MeasureString("X", TitleFont).Height;

				#region figureout aspect ratio and then size of cell

				var w = 94;
				var h = 55;
				ASPECT_RATIO = w / (float)h;
				h = (int)((printAreaWidth - titleHeight - 2) / 7);
				w = (int)(ASPECT_RATIO * h);

				#endregion figureout aspect ratio and then size of cell

				var runInfo = string.Format("BC Reference {0} layout for R{1}", position, run.RunNr);
				g.DrawString(runInfo, TitleFont, Brushes.Blue, 5, 2);

				var distictJobsInRun = run.Jobs.Distinct();
				var jobVsSlotCountCurrent = new Dictionary<IJob, int>();
				foreach (var j in distictJobsInRun)
				{
					jobVsSlotCountCurrent[j] = 0;
				}
				SolidBrush brushGray = new(Color.FromArgb(64, Color.Gray));
				//by now all orders that occupy multiple slots have been put in orderStatDictionary

				// Get the Job from slot
				if (run.Slots.Count == 0)
					return;
				// Draw the slots and BC details
				for (var c = 1; c <= Cols; c++)
				{
					for (var r = 1; r <= Rows; r++)
					{
						// int s = r + c * 7 - 7;   // this loop generates s = 1 .. 42
						var s = slotPos[pageNumber - 1][r, c];

						#region if there is nothign in this slot proceed to next

						// if there is nothign in this slot proceed to next
						if (run.Slots.Where(x => x.Slot == s).Count() == 0)
							continue;

						var nthSlot = run.Slots.Where(x => x.Slot == s).First();
						if (nthSlot == null || nthSlot.Job == null)
							continue;

						#endregion if there is nothign in this slot proceed to next

						var j = nthSlot.Job;
					
						#region Calculate BC layout cell border

						var left = c * w - w + 2;
						var top = r * h - h + (int)titleHeight + 2;
						var rBCCell = new Rectangle(left, top, w - 4, h - 4);
						g.DrawRectangle(penBlack3, rBCCell);

						#endregion Calculate BC layout cell border

						// draw slot
						g.DrawString(s.ToString(), smallFont, Brushes.Black, left + 1, top + 1);

						var slotWidth = (int)g.MeasureString("XXX", smallFont).Width;

						#region Draw Customer Name in Top Center

						var custName0 = j.Order.Customer.Name.Substring(0, Math.Min(j.Order.Customer.Name.Length, 30));
						var custName = custName0.ToUpper();
						var hCustName = g.MeasureString(custName, smallFont).Height;
						var rCustName = new Rectangle(left + slotWidth, top + 1, rBCCell.Width - 2 * slotWidth,
							(int)hCustName);
						g.DrawString(custName, smallFont, Brushes.Black, rCustName, sfCenterClipped);

						#endregion Draw Customer Name in Top Center

						#region Draw X of Y Cards the bottom

						var rBottomLine = new Rectangle(left, rBCCell.Bottom - (int)hCustName, rBCCell.Width,
							(int)hCustName);
						g.DrawString(textOfJobsInBCCellBottom[pageNumber - 1][r, c], smallFont, Brushes.Black,
							rBottomLine, sfCenterClipped);

						#endregion Draw X of Y Cards the bottom

						#region draw artwork thumbnail

						float hThumbRect = rBCCell.Height - (rCustName.Height + 2 + rBottomLine.Height + 2) - 1;
						var wThumbRect = ASPECT_RATIO * hThumbRect;

						var rThumb = new Rectangle(rBCCell.X + 5, rCustName.Bottom + 2, (int)wThumbRect,
							(int)hThumbRect);

						GraphicsPath path = null;
						if (j.RoundOption == RoundOption.None || j.RoundOption == RoundOption.Custom)
						{
							g.DrawRectangle(penBlack3, rThumb);
						}
						else
						{
							var radious = 12;

							var rc = RectangleCorners.None;
							/*
                            if (j.TLround)
                                rc = rc | RectangleCorners.TopLeft;
                            if (j.TRround)
                                rc = rc | RectangleCorners.TopRight;
                            if (j.BLround)
                                rc = rc | RectangleCorners.BottomLeft;
                            if (j.BRround)
                                rc = rc | RectangleCorners.BottomRight;
                            */

							path = RoundedRectangle.Create(rThumb, radious, rc);
							g.DrawPath(Pens.Black, path);
						}

						if (path != null)
							g.SetClip(path);
						
						
						g.FillRectangle(brushGray, rThumb);

						IArtwork art = j.GetArtwork(position);

						bool foundInTiff = true;
						var basePath0s =  LepGlobal.Instance.ArtworkDirectory(j, false);

						if (art != null && !String.IsNullOrEmpty(art.Ready) && System.IO.Path.GetExtension(art.Ready).Contains("tif"))
						{
							var basePath = LepGlobal.Instance.ArtworkDirectory(j, false);
							var fullPathToArtFile = Path.Combine(basePath.FullName, art.Ready);
							if (File.Exists(fullPathToArtFile))
							{
								try
								{
									using Stream fs = File.OpenRead(fullPathToArtFile);
									using var image = Image.FromStream(fs, true);
									g.DrawImage(image, rThumb);
									fs.Close();
								}
								catch (Exception loadEx)
								{
									foundInTiff = false;
									Log.Error(loadEx.Message, loadEx);
								}
							}
						}
						else
						{
							foundInTiff = false;
						}
						try
						{
							if (!foundInTiff)
							{
								var basePath = LepGlobal.Instance.ArtworkDirectory(j, false);

								var pattern0 = position == "front" ? "*_ft?.1.png" : "*_bk?.1.png";
								var pattern1 = position == "front" ? "*_multi.1.png" : "*_multi.2.png";

								var t0 = basePath.GetFiles(pattern0, SearchOption.AllDirectories).ToList();
								if (!t0.Any())
								{
									t0 = basePath.GetFiles(pattern0.Replace(".1.", ".0."), SearchOption.AllDirectories).ToList();
								}

								var t1 = basePath.GetFiles(pattern1, SearchOption.AllDirectories).ToList();
								if (!t1.Any())
								{
									var pp = pattern1;
									if (pattern1 == "*_multi.1.png") { pp = "*_multi.0.png"; }
									else if (pattern1 == "*_multi.2.png") { pp = "*_multi.1.png"; }
									t1 = basePath.GetFiles(pp, SearchOption.AllDirectories).ToList();
								}

								var t2 = new List<FileInfo>();
								t2.AddRange(t0);
								t2.AddRange(t1);

								if (t2.Any())
								{
									using Stream fs = t2.First().OpenRead();
									using var image = Image.FromStream(fs, true);
									g.DrawImage(image, rThumb);
									fs.Close();
								}
							}
						}
						catch (Exception ex)
						{
							Log.Error($"Error getting art {j.Id}");
						}

						if (path != null)
						{
							g.ResetClip();
							disposables.Add(path);
						}

						#endregion draw artwork thumbnail

						var bigFontSize = g.MeasureString("X", bigBoldFont);
						var smallFontSize = g.MeasureString("X", smallFont);
						var rUniqueCode = new RectangleF(rThumb.Right, rThumb.Y, bigFontSize.Width, bigFontSize.Height);

						// if there is a unique letter assigned
						if (statOfOrdersThatRepeat.ContainsKey(j.Order))
						{
							var uniqueLetterForThisOrder = statOfOrdersThatRepeat[j.Order].letter.ToString();
							g.DrawString(uniqueLetterForThisOrder, bigBoldFont, Brushes.Black, rUniqueCode);

							var strXofY = textOfRepeatedOrdersInBCCell[pageNumber - 1][r, c];
							g.DrawString(strXofY, smallFont, Brushes.Black, rUniqueCode.Right + 1,
								rUniqueCode.Bottom - smallFontSize.Height - 2);
						}

						#region Draw Order/Job Number in right side of BC Cell

						float rwInfo = rBCCell.Width - rThumb.Width - 5;
						var info = String.Format("O{0}\nJ{1}", j.Order.OrderNr, j.JobNr);
						var heightBCOrderJobNo = g.MeasureString(info, smallFont).Height;
						g.DrawString(info, smallFont, Brushes.Black,
							new PointF(rThumb.Right + 1, rUniqueCode.Bottom + 1));

						#endregion Draw Order/Job Number in right side of BC Cell
					}
				}

				if (pageNumber < 2)
				{
					e.HasMorePages = true;
				}
				else
				{
					e.HasMorePages = false;
				}
				brushGray.Dispose();
			}
			catch (Exception ex)
			{
				Log.Error(ex.Message, ex);
			}
		}

		private const string FONT_COURIER_NEW = "Courier New";

		private const string FONT_FREE3OF9 = "Free 3 of 9";

		private float ASPECT_RATIO = 1.8f;

		private StringBuilder basicInformation = new StringBuilder();

		private int bottomMargin;

		// private IConfigurationApplication configApp = new ConfigurationApplication();

		private List<IDisposable> disposables = new List<IDisposable>();

		protected int h;

		protected IJob job;

		private int leftMargin;

		private int pageHeight;

		private int pageNumber = 0;

		private int PageWidth;

		private int printAreaHeight;

		private int printAreaWidth;

		private StringBuilder processingText = new StringBuilder();

		private int rightMargin;

		protected IRun run;

		private int[][,] slotPos;
		//{
		//	new int[,]
		//	{
		//		{0, 0, 0, 0, 0, 0, 0},
		//		{0, 1, 8, 15, 22, 29, 36},
		//		{0, 2, 9, 16, 23, 30, 37},
		//		{0, 3, 10, 17, 24, 31, 38},
		//		{0, 4, 11, 18, 25, 32, 39},
		//		{0, 5, 12, 19, 26, 33, 40},
		//		{0, 6, 13, 20, 27, 34, 41},
		//		{0, 7, 14, 21, 28, 35, 42}
		//	},
		//	new int[,]
		//	{
		//		{0, 0, 0, 0, 0, 0, 0},
		//		{0, 36, 29, 22, 15, 8, 1},
		//		{0, 37, 30, 23, 16, 9, 2},
		//		{0, 38, 31, 24, 17, 10, 3},
		//		{0, 39, 32, 25, 18, 11, 4},
		//		{0, 40, 33, 26, 19, 12, 5},
		//		{0, 41, 34, 27, 20, 13, 6},
		//		{0, 42, 35, 28, 21, 14, 7}
		//	}
		//};

		private Dictionary<IJob, Stat> statOfJobs = new Dictionary<IJob, Stat>();

		private Dictionary<IOrder, Stat> statOfOrdersThatRepeat = new Dictionary<IOrder, Stat>();

		private string[][,] textOfJobsInBCCellBottom =
		{
			new string[8, 8],
			new string[8, 8]
		};

		private string[][,] textOfRepeatedOrdersInBCCell =
		{
			new string[8, 8],
			new string[8, 8]
		};

		private int topMargin;

		protected int w;

		#region Properties

		public IConfigurationApplication ConfigurationApplication { get; set; }

		public IJobApplication JobApplication { get; set; }

		public string PrinterAndTray { get; set; }

		public String PrintFileName { get; set; }

		//public IRun Run
		//{
		//	get { return run; }
		//	set {
		//		run = value;
		//		FormatPrintData();
		//	}
		//}

		#endregion Properties

		#region Public Methods

		public void FormatPrintData()
		{
			 
			try
			{
				// Each order that comes at least twice are assigned a Unique Letter and all the jobs under that order are numbered
				var distinctOrdersOfRun = run.Jobs.Select(j => j.Order).Distinct().OrderBy(o => o.OrderNr);
				var chr1 = 'A';
				foreach (var o in distinctOrdersOfRun)
				{
					var slotsOccupied = findOccupiedSlotsInRun(run, o);
					if (slotsOccupied > 1)
					{
						statOfOrdersThatRepeat.Add(o, new Stat() { numberOfSlots = slotsOccupied, letter = chr1, res = 0 });
						chr1++;
					}
				}

				foreach (var j in run.Jobs)
				{
					var slotsOccupied = findOccupiedSlotsInRun(run, j);
					statOfJobs.Add(j, new Stat() { numberOfSlots = slotsOccupied, res = 0 });
				}

				for (var c = 1; c <= Cols; c++)
				{
					for (var r = 1; r <= Rows; r++)
					{
						var fs = slotPos[0][r, c];
						var br = 0;
						var bc = 0;
						TranslateFrontRowColumIndices2BackRowColumnIndices(r, c, out br, out bc);

						// if there is nothign in this slot proceed to next
						if (run.Slots.Where(x => x.Slot == fs).Count() == 0)
							continue;

						var nthSlot = run.Slots.Where(x => x.Slot == fs).First();
						if (nthSlot == null || nthSlot.Job == null)
							continue;

						var j = nthSlot.Job;

						// sort  multi slot order  and their numbering
						if (statOfOrdersThatRepeat.ContainsKey(j.Order))
						{
							statOfOrdersThatRepeat[j.Order].res++;
							var textOfThisSlot = String.Format("{0}/{1}", statOfOrdersThatRepeat[j.Order].res,
								statOfOrdersThatRepeat[j.Order].numberOfSlots);
							textOfRepeatedOrdersInBCCell[0][r, c] = textOfThisSlot; // put in first page
							textOfRepeatedOrdersInBCCell[1][br, bc] = textOfThisSlot; // put in first page
						}

						if (statOfJobs.ContainsKey(j))
						{
							// sort all job numbering
							statOfJobs[j].res++;
							var xOfYCards = String.Format("{0} of {1} Cards", statOfJobs[j].res, j.Quantity);
							textOfJobsInBCCellBottom[0][r, c] = xOfYCards; // put in first page
							textOfJobsInBCCellBottom[1][br, bc] = xOfYCards; // put in first page
						}
					}
				}
			}
			catch (Exception ex)
			{
				Log.Error(ex, ex.Message);
				throw;
			}
		}

		public void SetupPrintProperties()
		{
			var ps = new PrinterSettings();
			//PrinterAndTray = "FG.DocuCentre-VII C4473 on henry.internal.lepcolourprinters.com.au|Tray 1";
			PrintUtils.ConfigurePrinterSettings(PrinterAndTray, ref ps);
			PrinterSettings = ps;

			ps.PrintToFile = (ps.PrinterName == "Microsoft XPS Document Writer") ||
							 (ps.PrinterName == "Microsoft Print to PDF");
			if (ps.PrintToFile && !string.IsNullOrEmpty(PrintFileName))
			{
				ps.PrintFileName = PrintFileName;
			}

			var pk = run.PrintType == PrintType.O ? PaperKind.A3 : PaperKind.A3; // Example fix if not always A3

			// Find paper size by name (more reliable than RawKind)
			foreach (PaperSize paperSize in ps.PaperSizes)
			{
				if (paperSize.PaperName.Equals("A3", StringComparison.OrdinalIgnoreCase))
				{
					ps.DefaultPageSettings.PaperSize = paperSize;
					break;
				}
			}

			// Set landscape for A3, portrait otherwise
			ps.DefaultPageSettings.Landscape = (pk == PaperKind.A3);

			// Set margins to 5 mm (converted to hundredths of an inch)
			int marginUnits = (int)(3 * 100 / 25.4); // 5 mm ≈ 20 units
			ps.DefaultPageSettings.Margins = new Margins(marginUnits, marginUnits, marginUnits, marginUnits);

			ps.Duplex = Duplex.Horizontal;

			DefaultPageSettings.PrinterSettings = ps;

			// Calculate dimensions
			pageHeight = DefaultPageSettings.PaperSize.Height;
			PageWidth = DefaultPageSettings.PaperSize.Width;
			leftMargin = DefaultPageSettings.Margins.Left;
			topMargin = DefaultPageSettings.Margins.Top;
			rightMargin = DefaultPageSettings.Margins.Right;
			bottomMargin = DefaultPageSettings.Margins.Bottom;
			printAreaWidth = PageWidth - rightMargin - leftMargin;
			printAreaHeight = pageHeight - topMargin - bottomMargin;
		}

		#endregion Public Methods

		#region Private Methods

		private int findOccupiedSlotsInRun(IRun run, IOrder order)
		{
			var result = 0;
			foreach (var j in order.Jobs)
			{
				result += run.Slots.Count(slot => slot.Job == j);
			}
			return result;
		}

		private int findOccupiedSlotsInRun(IRun run, IJob j)
		{
			var result = run.Slots.Count(s => s.Job == j);
			return result;
		}

		void IDisposable.Dispose()
		{
			base.Dispose();
			foreach (var d in disposables)
			{
				d.Dispose();
			}
		}

		private void TranslateFrontRowColumIndices2BackRowColumnIndices(int fr, int fc, out int br, out int bc)
		{
			var fs = slotPos[0][fr, fc];
			for (var c = 1; c <= Cols; c++)
			{
				for (var r = 1; r <= Rows; r++)
				{
					if (slotPos[1][r, c] == fs)
					{
						br = r;
						bc = c;
						return;
					}
				}
			}
			br = 0;
			bc = 0;
		}

		#endregion Private Methods
	}
}
