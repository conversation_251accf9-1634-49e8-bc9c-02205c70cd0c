﻿<div class="row">
    <div class="col-md-12">
        <div class="modal-header">
            <h3 class="modal-title">Select Celloglaze to override</h3>
        </div>
        <div class="modal-body">

       
            <select ng-model="selectedCello" id="celloglaze" class="form-control input" 
            size="10" ng-options="k as v for (k,v) in ngDialogData.cellos  track by k">
                <option selected="selected" value="">select...</option>
            </select>
 
        </div>
        <div class="modal-footer">
            <button class="btn" ng-click="confirm(selected<PERSON>ello)"> <i class="glyphicon glyphicon-ok" ></i> Ok</button>
            <button class="btn"  ng-click="closeThisDialog()"><i class="glyphicon glyphicon-remove"></i> Cancel</button>
        </div>
    </div>
</div>