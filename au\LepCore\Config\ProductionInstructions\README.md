# Production Instructions Configuration

This directory contains production instructions for different job statuses in YAML/Markdown format.

## Hybrid Instruction System

The system now supports **two types of instructions**:

### 1. Quick Checkpoints (Boss's Version)
- **Files**: `{Status}-Checkpoints.md`
- **Purpose**: Fast, focused pre-work checklists
- **Content**: Essential quality control steps and sign-offs
- **Usage**: Shown first in timing popups for quick reference

### 2. Detailed Instructions (Comprehensive Guides)
- **Files**: `{Status}.md`
- **Purpose**: Complete training and reference materials
- **Content**: Step-by-step procedures, safety notes, troubleshooting
- **Usage**: Available as detailed reference when needed

## File Structure

### Checkpoint Files (Priority)
- `Folded-Checkpoints.md` - Quick folding verification steps
- `Printing-Checkpoints.md` - Press quality control checklist
- `Cut-Checkpoints.md` - Guillotine accuracy checks
- `Cello-Checkpoints.md` - Cello setup and cleanliness verification
- `Stitched-Checkpoints.md` - Binding quality control
- `Packed-Checkpoints.md` - Despatch preparation checklist
- `BusinessCard-Checkpoints.md` - Business card specific checks
- `Plateroom-Checkpoints.md` - Plate preparation standards
- `Letterpress-Checkpoints.md` - Die cutting setup verification
- `ShrinkWrap-Checkpoints.md` - Shrink wrap quality standards

### Detailed Instruction Files
- `Folded.md` - Complete folding procedures and troubleshooting
- `Printing.md` - Comprehensive press operation guide
- `Cut.md` - Detailed cutting procedures and safety
- `Stitched.md` - Complete binding and finishing guide
- `Packed.md` - Full despatch procedures and documentation

## File Format

Each instruction file uses YAML front matter followed by Markdown content:

```yaml
---
status: StatusName
title: Human Readable Title
type: checkpoints  # or "detailed"
estimatedDuration: 15
isActive: true
requiredTools:
  - Tool 1
  - Tool 2
qualityChecks:
  - Check 1
  - Check 2
---

# Markdown Content Here

Instructions in markdown format...
```

## API Endpoints

### Get Instructions (Automatic Priority)
- `GET /api/production-instructions/{status}`
- Returns checkpoints first, falls back to detailed

### Get Both Types
- `GET /api/production-instructions/{status}/both`
- Returns both checkpoint and detailed instructions

### Clear Cache
- `POST /api/production-instructions/clear-cache`
- Refreshes instruction cache after file changes

## Status Mapping

The `StatusMapping.yaml` file maps job board statuses to instruction files, allowing multiple statuses to use the same instruction set.

## Usage in Timing System

1. **Timing Popup**: Shows quick checkpoints for immediate reference
2. **Detailed View**: Link to comprehensive instructions when needed
3. **Quality Control**: Checkbox verification of critical steps
4. **Sign-off**: Digital confirmation of completion

## Editing Instructions

1. Edit the `.md` files directly
2. Use any text editor or Markdown editor
3. Changes take effect immediately (cached for performance)
4. Use `ClearCache()` method to refresh cache if needed

## Markdown Features Supported

- Headers and subheaders
- Tables
- Task lists with checkboxes
- Code blocks
- Blockquotes
- Emphasis (bold, italic)
- Links and images
- Emojis 😊
- And more via Markdig extensions
