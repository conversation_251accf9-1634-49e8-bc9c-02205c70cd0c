<div id="jobBoardPage">
    <script type="text/javascript">
        function openJob(jid) {
            var url = window.location.origin + '/#!/staff/jobs/' + jid;
        }
        var facility = 'FG';
        var jobBoardHub;

        if (window.location.href.endsWith('/FG')) {
            facility = 'FG';
            $('#facilityx').html('Forest Glen');
        }

        var jobboardfacility = 'FG';
        var delay = 30000;
        var timer = null;
        var oT = null;
        var gjson = null;
        var gfilter = 'Exceptions';
        var gRunGrouping = true;
        var giLastVersionRendered = 0;
        var giNoOfJobs = 50;
        var t = new Date();
        var gBusy = false;

        // Define column references for better readability and maintainability
        var COLUMNS = {
            HEALTH: { data: 'health', title: 'health', name: 'health', visible: false },
            BOARDS_TO_APPEAR: { data: 'boardsToAppearDisplay', title: 'Board to appear', name: 'boardsToAppear', visible: false },
            JOB_ID: { data: 'id', title: 'Job#', name: 'id', className: 'JID', render: renderJobCell },
            ORDER_ID: { data: 'orderId', title: 'Order#', name: 'orderId', className: 'OID', render: renderOrderCell },
            RUN_ID: { data: 'runId', title: 'Run#', name: 'runId', className: 'RID', render: renderRunCell },
            CUSTOMER: { data: 'customerName', title: 'Customer', name: 'customer', width: '180px' },
            JOB_NAME: { data: 'jobName', title: 'Job Name', name: 'jobName', width: '180px' },
            PRINT_BY_DATE: { data: 'printByDate', title: 'Print by', name: 'printByDate', width: '150px', render: renderDateTimeCell },
            DESPATCH_BY_DATE: { data: 'despatchByDate', title: 'Despatch by', name: 'despatchByDate', width: '10px' },
            HOURS_TO_DESPATCH: { data: 'hd', title: 'Hours to Despatch', name: 'hd', width: '100px', render: renderHDCell },
            STATUS: { data: 'currentStatus', title: 'Status', name: 'status', visible: false },
            STATUS_DATE: { data: 'statusDate', title: 'Status Time', name: 'statusDate', width: '180px', render: renderDateTimeCell },
            NEXT_STATUS: { data: 'nextStatus', title: 'NextStatus', name: 'nextStatus', width: '180px' },
            TEMPLATE: { data: 'template', title: 'Job Type', name: 'template' },
            STOCK: { data: 'stock', title: 'Stock', name: 'stock' },
            CELLO: { data: 'cello', title: 'Cello', name: 'cello' },
            TRIM_SIZE: { data: 'trimSize', title: 'TrimSize', name: 'trimSize' },
            FOLDING: { data: 'folding', title: 'Folding', name: 'folding' },
            QUANTITY: { data: 'quantity', title: 'Qty', name: 'quantity' },
            PRESS_DETAIL_PAPER_SIZE: { data: 'pressDetailPaperSize', title: 'PS', name: 'ps' },
            PRESS_SHEETS: { data: 'pressSheets', title: 'Sheets', name: 'sheets' },
            AGE: { data: 'age', title: 'Age', name: 'age' },
            RUN_GROUPING: { data: 'runGrouping', title: 'RunG', name: 'runGrouping', width: '10px', visible: false },
            HEALTH_INDICATOR: { data: 'healthIndicator', title: 'H', name: 'healthIndicator', width: '10px', visible: false },
            RUN_HEALTH_GROUPING: { data: 'runHealthGrouping', title: 'RH', name: 'runHealthGrouping', width: '10px', visible: false },
            ROW_TYPE: { data: 'rowType', title: 'RJ', name: 'rowType', width: '10px', visible: false },
            RUN_ID_DUP: { data: 'runId', title: 'RID', name: 'runIdDup', visible: false },
            ED: { data: 'pressDetailPaperSize', title: 'ED', name: 'ed', visible: false },
            ORDER_DISPATCH_EST: { data: 'orderDispatchEst', title: 'OrderDispatchEst', name: 'orderDispatchEst', visible: false }
        };

        // Default sort order using column references
        var gSort = [
            [COLUMNS.HOURS_TO_DESPATCH.name, 'asc'],
            [COLUMNS.HEALTH.name, 'asc'],
            [COLUMNS.HEALTH_INDICATOR.name, 'asc'],
            [COLUMNS.PRESS_SHEETS.name, 'desc']
        ];

        function renderJobCell(data, type, row) {
            if (!data) return '';
            var id = data;

            if (id != 0) {
                var url = window.location.origin + '/#!/staff/order/' + row.orderId + '/job/' + row.id;
                return '<a href="' + url + '" target="_blank" >' + id + '</a>';
            }

            return '';
        }

        function renderOrderCell(data, type, row) {
            if (!data) return '';
            var id = data;
            if (id != 0) {
                var url = window.location.origin + '/#!/staff/order/' + id + '/open';
                return '<a href="' + url + '" target="_blank" >' + id + '</a>';
            }

            return '';
        }

        function renderRunCell(data, type, row) {
            if (!data) return '';
            var id = data;
            if (id != -1) {
                var url = window.location.origin + '/#!/staff/run/' + id + '';
                return '<a href="' + url + '" target="_blank" >' + id + '</a>';
            }
            return '';
        }

        function secondsToString(seconds) {
            var value = seconds;

            var units = {
                "d": 24 * 60 * 60,
                "h": 60 * 60,
                "m": 60,
            }

            var result = [];

            for (var name in units) {
                var p = Math.floor(value / units[name]);
                if (p)
                    result.push("" + p + "" + name);
                value %= units[name];
            }
            result = result.join(" ");

            return result;
        }

        // render the cell Hours to dispatch
        function renderHDCell(data, type, row) {
            var hd = data;

            if (type === "sort" || type === 'type') {
                return hd;
            }
            else {
                var result = "";
                if (!hd) return result;

                result = secondsToString(Math.abs(hd));
                if (hd && hd < 0) result = '-' + result;
                return result;
            }
        }

        function renderDateTimeCell(data, type, row) {
            if (!data) return '';
            return moment(data).format('DD/MM/YY HH:mm');
        }

        // apply filter
        function applyJobBoardFilter() {
            if (oT == null) return;
            var lFilter = gfilter;
            if (lFilter != 'undefined') {
                fnResetAllFilters();

                if (lFilter == 'All' || lFilter == '' || !lFilter) {
                    // No filter
                } else if (lFilter == 'Exceptions') {
                    // Filter for health indicator (red or amber)
                    oT.column(getColumnIndex(COLUMNS.HEALTH)).search("1|2", true, false);
                    oT.draw();
                } else if (lFilter == 'HD') {
                    // Filter for hours to dispatch
                    oT.column(getColumnIndex(COLUMNS.HOURS_TO_DESPATCH)).search('^.+$', true, false);
                    oT.column(getColumnIndex(COLUMNS.CUSTOMER)).visible(false);
                    oT.draw();
                }
                else {
                    // Filter by board name
                    oT.column(getColumnIndex(COLUMNS.CUSTOMER)).visible(true);

                    // Use a custom filter function for boardsToAppear
                    $.fn.dataTable.ext.search.push(
                        function(settings, data, dataIndex, rowData) {
                            // If no filter is applied, show all rows
                            if (!lFilter) return true;

                            // Check if boardsToAppear contains the filter value
                            if (rowData.boardsToAppear && Array.isArray(rowData.boardsToAppear)) {
                                for (var i = 0; i < rowData.boardsToAppear.length; i++) {
                                    var board = rowData.boardsToAppear[i];
                                    if (typeof board === 'string' && board.toLowerCase() === lFilter.toLowerCase()) {
                                        return true;
                                    }
                                }
                            }

                            return false;
                        }
                    );

                    oT.draw();

                    // Remove the custom filter after drawing
                    $.fn.dataTable.ext.search.pop();
                }
            }
            updateColouredJobCounts();
        };

        // Helper function to get column index by column reference
        function getColumnIndex(columnRef) {
            return oT.column(`${columnRef.name}:name`).index();
        }

        function constructJobBoard(jsonData) {
            if (jsonData.version == 0) {
                toastr.info("Job board is being created in the server, please wait a few seconds...\n");
                setTimeout(function () { window.location.reload(); }, 10000);
                return;
            }
            gBusy = true;

            var paginate = false;

            giLastVersionRendered = jsonData.version;
            $('#lastthingIwasdoing').html(t.toLocaleTimeString() + " Updating Job board...");
            $('#busy').toggle();

            // Transform the data for DataTables
            var tableData = [];
            if (jsonData.data && jsonData.data.length > 0) {
                tableData = jsonData.data.map(function(item) {
                    // Add calculated fields for sorting and filtering
                    item.rowType = item.id === 0 ? "r" : (item.runIsBC ? "jbc" : "j");
                    item.healthIndicator = item.health !== 3 ? "E" : "";
                    item.runGrouping = item.runIsBC ? `_R${item.runId.toString().padStart(6, '0')}` : "Jobs";
                    item.runHealthGrouping = item.runIsBC ? `${item.healthOfRun}_R${item.runId.toString().padStart(6, '0')}_J${item.id.toString().padStart(6, '0')}` : "Z_Jobs";

                    // Format boards to appear for display
                    item.boardsToAppearDisplay = item.boardsToAppear && item.boardsToAppear.length > 0
                        ? item.boardsToAppear.join(',')
                        : "Where does this go?";

                    return item;
                });
            }

            // Configure DataTable
            var tableConfig = {
                data: tableData,
                order: gSort,
                paging: paginate,
                lengthChange: paginate,
                autoWidth: true,
                fixedHeader: true,
                rowCallback: rowRenderCallback,
                processing: true,
                language: {
                    'processing': 'Processing Job Board...',
                    "lengthMenu": 'Show <select id="noOfJobs" name="noOfJobs"><option value="10">10</option><option value="25">25</option><option value="50">50</option><option value="100">100</option><option value="-1">All</option></select> Jobs ',
                    'eroRecords': 'No Jobs in Board',
                    'info': 'Showing _START_ to _END_ of _TOTAL_ Jobs',
                    'infoEmpty': 'Showing 0 to 0 of 0 Jobs',
                    'infoFiltered': '(filtered from _MAX_ total Jobs)',
                    'infoPostFix': '',
                    'search': 'Search:',
                    'sUrl': '',
                    'paginate': {
                        'first': 'First',
                        'previous': 'Previous',
                        'next': 'Next',
                        'last': 'Last'
                    }
                },
                // Use the column references for better readability and maintainability
                columns: [
                    COLUMNS.HEALTH,
                    COLUMNS.BOARDS_TO_APPEAR,
                    COLUMNS.JOB_ID,
                    COLUMNS.ORDER_ID,
                    COLUMNS.RUN_ID,
                    COLUMNS.CUSTOMER,
                    COLUMNS.JOB_NAME,
                    COLUMNS.PRINT_BY_DATE,
                    COLUMNS.DESPATCH_BY_DATE,
                    COLUMNS.HOURS_TO_DESPATCH,
                    COLUMNS.STATUS,
                    COLUMNS.STATUS_DATE,
                    COLUMNS.NEXT_STATUS,
                    COLUMNS.TEMPLATE,
                    COLUMNS.STOCK,
                    COLUMNS.CELLO,
                    COLUMNS.TRIM_SIZE,
                    COLUMNS.FOLDING,
                    COLUMNS.QUANTITY,
                    COLUMNS.PRESS_DETAIL_PAPER_SIZE,
                    COLUMNS.PRESS_SHEETS,
                    COLUMNS.AGE,
                    COLUMNS.RUN_GROUPING,
                    COLUMNS.HEALTH_INDICATOR,
                    COLUMNS.RUN_HEALTH_GROUPING,
                    COLUMNS.ROW_TYPE,
                    COLUMNS.RUN_ID_DUP,
                    COLUMNS.ED,
                    COLUMNS.ORDER_DISPATCH_EST
                ],
                initComplete: function () {
                    this.api().columns.adjust();
                    applyJobBoardFilter();

                    $("select#noOfJobs").change(function () {
                        giNoOfJobs = parseInt($(this).val());
                    });

                    $('tr.r').click(function () {
                        var next = $(this).next('tr');
                        while ($(next).is('tr.jbc')) {
                            $(next).toggle();
                            if (!$(next).hasClass('rexpanded-group-row'))
                                $(next).addClass('rexpanded-group-row');
                            else
                                $(next).removeClass('rexpanded-group-row');
                            next = $(next).next('tr');
                        }

                        if ($(this).hasClass('rcollapsed-group'))
                            $(this).removeClass('rcollapsed-group').addClass('rexpanded-group');
                        else if ($(this).hasClass('rexpanded-group'))
                            $(this).removeClass('rexpanded-group').addClass('rcollapsed-group');

                        return null;
                    });
                    $('#busy').toggle();
                    updateColouredJobCounts();
                    gBusy = false;
                    $('#lastthingIwasdoing').html("");
                }
            };

            if (paginate) {
                tableConfig.pagingType = "full_numbers";
                tableConfig.pageLength = giNoOfJobs;
            } else {
                tableConfig.paging = false;
                tableConfig.lengthChange = false;
            }

            gjson = jsonData; // save a copy for counting

            // Initialize or refresh the DataTable
            if ($.fn.dataTable.isDataTable('#jobBoard')) {
                oT = $('#jobBoard').DataTable();
                oT.clear().rows.add(tableData).draw();
            } else {
                oT = $('#jobBoard').DataTable(tableConfig);
            }

            applyJobBoardFilter();
            gBusy = false;
            return null;
        }

        function rowRenderCallback(row, data, index) {
            var jobHealth = data.health;
            var styleClass = ['', 'redAlert', 'amberAlert', 'noAlert'][jobHealth];
            $(row).removeClass('redAlert amberAlert noAlert').addClass(styleClass);

            if (data.rowType == 'jbc') {
                $(row).addClass('jbc').css('display', 'none');
            }

            if (data.rowType == 'r') {
                $(row).addClass('r').addClass('rcollapsed-group');
            }

            return row;
        };

        // use json to populate created table
        function buildJobBoard() {
            if (gBusy) {
                return null;
            }
            gBusy = true;

            if (window.location.href.endsWith('/FG')) {
                facility = 'FG';
                $('#facilityx').html('Forest Glen');
            }

            var auth = 'Bearer ' + window.localStorage['ngStorage-lepToken'].replace(/\"/g, '');

            $.ajaxSetup({
                headers: {
                    'Authorization': auth
                }
            });

            // check version if not changed exit
            t = new Date();
            var sUrlVersion = '/api/jobboard/version';
            $.getJSON(sUrlVersion, null, function (version) {
                $('#lastthingIwasdoing').html(t.toLocaleTimeString() + " Detecting changes if any...");

                if (giLastVersionRendered == version) {
                    $('#lastthingIwasdoing').html(t.toLocaleTimeString() + " No changes...");
                    gBusy = false;
                    return;
                }

                // rebuild the boards with new data using the JSON endpoint
                var sUrlBoard = '/api/jobboard/getjson?facilityStr=' + facility + '&t=' + (+new Date());
                $.getJSON(sUrlBoard, null, constructJobBoard);
            });
        }

        // clears the filters that are applied now
        function fnResetAllFilters() {
            if (oT == null) return;

            // Clear global search
            oT.search('');

            // Clear individual column filters using the columns API
            oT.columns().search('');

            // Apply the changes
            oT.draw();

            return null;
        };

        function updateColouredJobCounts() {
            try {
                if (oT == null) return;
                var redJobsCount = 0;
                var amberJobsCount = 0;

                if (gjson === null || !gjson.data) return;

                var data = gjson.data;
                var lFilter = gfilter;

                if (lFilter != 'All' && lFilter != 'Exceptions' && lFilter != '') {
                    for (var i = 0; i < data.length; i++) {
                        var item = data[i];
                        // Check if boardsToAppear contains the filter value (case-insensitive)
                        var hasBoard = false;
                        if (item.boardsToAppear && Array.isArray(item.boardsToAppear)) {
                            for (var j = 0; j < item.boardsToAppear.length; j++) {
                                var board = item.boardsToAppear[j];
                                if (typeof board === 'string' && board.toLowerCase() === lFilter.toLowerCase()) {
                                    hasBoard = true;
                                    break;
                                }
                            }
                        }

                        if (hasBoard) {
                            if (item.health == 1) { redJobsCount++; }
                            else if (item.health == 2) { amberJobsCount++; }
                        }
                    }
                } else {
                    for (var i = 0; i < data.length; i++) {
                        var item = data[i];
                        if (item.health == 1) { redJobsCount++; }
                        else if (item.health == 2) { amberJobsCount++; }
                    }
                }

                $('#redJobsCount').html(redJobsCount);
                $('#amberJobsCount').html(amberJobsCount);
                return null;
            } catch (ex) {
                console.error("Error updating colored job counts:", ex);
            }
        };

        $(document).ready(function () {
            gfilter = '';

            $("body").on("buildJobBoard", function (event) {
                //buildJobBoard();
            });

            buildJobBoard();

            $('#radio').find('label').click(function () {
                gfilter = $(this).clone()    //clone the element
                    .children() //select all the children
                    .remove()   //remove all the children
                    .end()  //again go back to selected element
                    .text().trim();

                if ($(this).hasClass('norungroup')) {
                    gRunGrouping = false;
                } else {
                    gRunGrouping = true;
                }

                gLastClickTab = $(this);

                if (oT == null) return;
                applyJobBoardFilter();

                // Sort by: Hours to Dispatch, Health, Health Indicator, Sheets
                oT.order([
                    [getColumnIndex(COLUMNS.HOURS_TO_DESPATCH), 'asc'],
                    [getColumnIndex(COLUMNS.HEALTH), 'asc'],
                    [getColumnIndex(COLUMNS.HEALTH_INDICATOR), 'asc'],
                    [getColumnIndex(COLUMNS.PRESS_SHEETS), 'desc']
                ]).draw();

                return null;
            });

            setInterval(buildJobBoard, delay);
            return null;
        });
    </script>

    <table style="width: 100%; border-collapse: collapse; border: 0;">
        <tr>
            <td>
                <h3 id="facilityx" style="margin:0 20px; float:left"></h3>

                <div id="radio" class="select-board">
                    <label class="control-label"> <input type="radio" name="radio" /> All</label>
                    <label class="control-label"> <input type="radio" name="radio" /> PreFlight</label>
                    <label class="control-label"> <input type="radio" name="radio" /> PrePress</label>
                    <label class="control-label"> <input type="radio" name="radio" /> DPCProduction </label>
                    <label class="control-label"> <input type="radio" name="radio" /> WideFormatProduction </label>
                    <label class="control-label"> <input type="radio" name="radio" /> Plateroom</label>
                    <label class="control-label"> <input type="radio" name="radio" /> PressRoom</label>
                    <label class="control-label"> <input type="radio" name="radio" /> Celloglaze</label>
                    <label class="control-label"> <input type="radio" name="radio" /> Guillotine</label>
                    <label class="control-label"> <input type="radio" name="radio" /> Folding</label>
                    <label class="control-label"> <input type="radio" name="radio" /> Stitching</label>
                    <label class="control-label"> <input type="radio" name="radio" /> LetterPress</label>
                    <label class="control-label"> <input type="radio" name="radio" /> Finishing</label>
                    <label class="control-label norungroup"> <input type="radio" name="radio" /> Despatch</label>
                    <label class="control-label norungroup"> <input type="radio" name="radio" /> Outwork</label>
                    <label class="control-label norungroup"> <input type="radio" name="radio" /> Pay Me</label>
                    <label class="control-label norungroup"> <input type="radio" name="radio" /> OnHold</label>
                    <label class="control-label"> <input type="radio" name="radio" />HD</label>
                </div>
            </td>

            <td align="right">
                <span style="white-space: nowrap" id="counts">
                    <span class="alertbox redAlertB">
                        Red <span id="redJobsCount"></span>
                    </span>

                    <span class="alertbox amberAlertB">
                        Amber <span id="amberJobsCount"></span>
                    </span>
                </span>
            </td>
        </tr>
    </table>

    <table class="normal" id="jobBoard"></table>
    <span id="lastthingIwasdoing">loading data for the first time</span>

    <span id="busy" style="font-size: 50px; position: absolute; top: 50%; left: 50%; display: none">busy! </span>
</div>
