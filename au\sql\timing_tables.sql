-- Production Step Time Tracking Database Tables
-- Execute this script to create the timing system tables

-- Drop existing tables if they exist (for re-runnable script)
IF OBJECT_ID('dbo.JobStepTimingSummary', 'U') IS NOT NULL
    DROP TABLE [dbo].[JobStepTimingSummary]

IF OBJECT_ID('dbo.JobStepActiveSession', 'U') IS NOT NULL
    DROP TABLE [dbo].[JobStepActiveSession]

IF OBJECT_ID('dbo.JobStepTimingEvent', 'U') IS NOT NULL
    DROP TABLE [dbo].[JobStepTimingEvent]

PRINT 'Existing timing tables dropped (if they existed)'

-- 1. Job Step Timing Events - Records every button click
CREATE TABLE [dbo].[JobStepTimingEvent](
    [Id] [int] IDENTITY(1,1) NOT NULL,
    [JobId] [int] NOT NULL,
    [UserId] [int] NOT NULL,
    [Status] [varchar](24) NOT NULL,        -- Production step (e.g., 'Filling', 'Cut', 'Folded')
    [EventType] [varchar](20) NOT NULL,     -- 'PLAY', 'PAUSE', 'RESUME', 'FINISH'
    [EventTime] [datetime] NOT NULL,        -- When the button was clicked
    [SessionId] [uniqueidentifier] NOT NULL, -- Groups related events together
    [Notes] [varchar](500) NULL,            -- Optional user notes
    [IPAddress] [varchar](45) NULL,         -- For audit purposes
    [UserAgent] [varchar](500) NULL,        -- Browser info for audit
    [InstructionsViewed] [bit] NOT NULL DEFAULT(0),     -- Did user view instructions
    [InstructionsViewedAt] [datetime] NULL,             -- When instructions were viewed
    [WorkstationId] [varchar](50) NULL,                 -- Which workstation/computer
    [QualityChecksPassed] [bit] NULL,                   -- Quality checks completed
    [CompletionNotes] [nvarchar](1000) NULL,            -- Notes on completion
    [DateCreated] [datetime] NOT NULL DEFAULT(GETDATE()),

    CONSTRAINT [PK_JobStepTimingEvent] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_JobStepTimingEvent_Job] FOREIGN KEY ([JobId]) REFERENCES [Job]([Id]),
    CONSTRAINT [FK_JobStepTimingEvent_User] FOREIGN KEY ([UserId]) REFERENCES [LepUser]([Id]),
    CONSTRAINT [CK_JobStepTimingEvent_EventType] CHECK ([EventType] IN ('PLAY', 'PAUSE', 'RESUME', 'FINISH'))
)

-- Indexes for performance
CREATE INDEX [IX_JobStepTimingEvent_JobId] ON [JobStepTimingEvent]([JobId])
CREATE INDEX [IX_JobStepTimingEvent_UserId] ON [JobStepTimingEvent]([UserId])
CREATE INDEX [IX_JobStepTimingEvent_SessionId] ON [JobStepTimingEvent]([SessionId])
CREATE INDEX [IX_JobStepTimingEvent_Status] ON [JobStepTimingEvent]([Status])
CREATE INDEX [IX_JobStepTimingEvent_EventTime] ON [JobStepTimingEvent]([EventTime])

-- 2. Active Sessions - Tracks current timing sessions
CREATE TABLE [dbo].[JobStepActiveSession](
    [Id] [int] IDENTITY(1,1) NOT NULL,
    [JobId] [int] NOT NULL,
    [UserId] [int] NOT NULL,
    [Status] [varchar](24) NOT NULL,
    [SessionId] [uniqueidentifier] NOT NULL,
    [CurrentState] [varchar](20) NOT NULL,   -- 'READY', 'PLAYING', 'PAUSED', 'FINISHED'
    [StartTime] [datetime] NOT NULL,         -- When session started (first PLAY)
    [LastEventTime] [datetime] NOT NULL,    -- Last button click time
    [TotalPausedDuration] [int] NOT NULL DEFAULT(0), -- Total paused time in seconds
    [IsActive] [bit] NOT NULL DEFAULT(1),   -- False when FINISHED
    [DateCreated] [datetime] NOT NULL DEFAULT(GETDATE()),
    [DateModified] [datetime] NOT NULL DEFAULT(GETDATE()),

    CONSTRAINT [PK_JobStepActiveSession] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_JobStepActiveSession_Job] FOREIGN KEY ([JobId]) REFERENCES [Job]([Id]),
    CONSTRAINT [FK_JobStepActiveSession_User] FOREIGN KEY ([UserId]) REFERENCES [LepUser]([Id]),
    CONSTRAINT [CK_JobStepActiveSession_State] CHECK ([CurrentState] IN ('READY', 'PLAYING', 'PAUSED', 'FINISHED')),
    CONSTRAINT [UQ_JobStepActiveSession] UNIQUE ([JobId], [UserId], [Status], [IsActive])
)

-- Indexes
CREATE INDEX [IX_JobStepActiveSession_Active] ON [JobStepActiveSession]([IsActive]) WHERE [IsActive] = 1
CREATE INDEX [IX_JobStepActiveSession_User] ON [JobStepActiveSession]([UserId]) WHERE [IsActive] = 1

-- 3. Timing Summary - Aggregated timing data for reporting
CREATE TABLE [dbo].[JobStepTimingSummary](
    [Id] [int] IDENTITY(1,1) NOT NULL,
    [JobId] [int] NOT NULL,
    [UserId] [int] NOT NULL,
    [Status] [varchar](24) NOT NULL,
    [SessionId] [uniqueidentifier] NOT NULL,
    [TotalDuration] [int] NOT NULL,         -- Total working time in seconds
    [TotalPausedTime] [int] NOT NULL,       -- Total paused time in seconds
    [SessionStart] [datetime] NOT NULL,     -- First PLAY event
    [SessionEnd] [datetime] NULL,           -- FINISH event (NULL if not finished)
    [EventCount] [int] NOT NULL,            -- Number of button clicks
    [IsCompleted] [bit] NOT NULL DEFAULT(0), -- True when FINISHED
    [DateCreated] [datetime] NOT NULL DEFAULT(GETDATE()),
    [DateModified] [datetime] NOT NULL DEFAULT(GETDATE()),

    CONSTRAINT [PK_JobStepTimingSummary] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_JobStepTimingSummary_Job] FOREIGN KEY ([JobId]) REFERENCES [Job]([Id]),
    CONSTRAINT [FK_JobStepTimingSummary_User] FOREIGN KEY ([UserId]) REFERENCES [LepUser]([Id])
)

-- String-based enum values for better readability and self-documenting database
-- Check constraints are already defined in the table creation above

-- Production Step Instructions are now stored as YAML + Markdown files
-- No database table needed - instructions are loaded from files

-- Configuration is now handled by constants in the application code
-- No database configuration entries needed

PRINT 'Timing system tables created successfully!'
