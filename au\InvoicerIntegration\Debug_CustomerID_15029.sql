-- Debug script to check why CustomerID = 15029 is not getting a record created
-- Run each section separately to identify the issue

DECLARE @CurrentTime DATETIME = GETDATE();
DECLARE @FirstDayOfMonth DATETIME = DATEADD(MONTH, DATEDIFF(MONTH, 0, GETDATE()), 0);
DECLARE @LastDayOfMonth DATETIME = DATEADD(MONTH, DATEDIFF(MONTH, -1, GETDATE()), -1);

PRINT 'Current Time: ' + CAST(@CurrentTime AS VARCHAR(50))
PRINT 'Last Day of Month: ' + CAST(@LastDayOfMonth AS VARCHAR(50))

-- Step 1: Check if customer exists and meets basic criteria
SELECT 'Step 1: Customer Basic Info' AS Step
SELECT 
    c.CustomerId,
    c.Name,
    c.PaymentTerms,
    c.FranchiseCode,
    c.MYOBPastDue,
    c.ProductPriceCode,
    c.FreightPriceCode,
    c.MAT3mA,
    c.<PERSON>reateOffer,
    -- Check each condition
    CASE WHEN c.PaymentTerms IN ('Account', 'OnHold') THEN 'PASS' ELSE 'FAIL' END AS PaymentTermsCheck,
    CASE WHEN (c.FranchiseCode = '' or c.FranchiseCode is null) THEN 'PASS' ELSE 'FAIL' END AS FranchiseCodeCheck,
    CASE WHEN c.MYOBPastDue <= 0 THEN 'PASS' ELSE 'FAIL' END AS PastDueCheck,
    CASE WHEN (C.ProductPriceCode IN ('P0', NULL, '') AND C.FreightPriceCode IN ('F0', NULL, '')) THEN 'PASS' ELSE 'FAIL' END AS PriceCodeCheck
FROM Customer c
WHERE c.CustomerId = 15029;

-- Step 2: Check EDR calculation
SELECT 'Step 2: EDR Calculation' AS Step
SELECT 
    c.CustomerId,
    c.MAT3mA,
    EDRp = CASE
        WHEN c.MAT3mA >= 10000 AND c.MAT3mA < 15000 THEN 25
        WHEN c.MAT3mA >= 7500 AND c.MAT3mA < 10000 THEN 20
        WHEN c.MAT3mA >= 5000 AND c.MAT3mA < 7500 THEN 15
        WHEN c.MAT3mA >= 2000 AND c.MAT3mA < 5000 THEN 10
        WHEN c.MAT3mA >= 500 AND c.MAT3mA < 2000 THEN 5
        ELSE NULL
    END,
    CASE 
        WHEN c.MAT3mA >= 500 THEN 'PASS - Has EDR discount'
        ELSE 'FAIL - MAT3mA too low for discount'
    END AS EDRCheck
FROM Customer c
WHERE c.CustomerId = 15029;

-- Step 3: Check if customer would be in the Cust CTE
;WITH Cust AS (
    SELECT c.CustomerId,
        EDRp = CASE
            WHEN c.MAT3mA >= 10000 AND c.MAT3mA < 15000 THEN 25
            WHEN c.MAT3mA >= 7500 AND c.MAT3mA < 10000 THEN 20
            WHEN c.MAT3mA >= 5000 AND c.MAT3mA < 7500 THEN 15
            WHEN c.MAT3mA >= 2000 AND c.MAT3mA < 5000 THEN 10
            WHEN c.MAT3mA >= 500 AND c.MAT3mA < 2000 THEN 5
            ELSE NULL
        END,
        c.Name,
        c.MAT3mA,
        c.FranchiseCode
    FROM Customer c
    WHERE c.PaymentTerms IN ('Account', 'OnHold')
        AND (c.FranchiseCode = '' or c.FranchiseCode is null)
        AND c.MYOBPastDue <= 0
        AND (C.ProductPriceCode IN ('P0', NULL, '') AND C.FreightPriceCode IN ('F0', NULL, ''))
        AND c.CustomerId = 15029
)
SELECT 'Step 3: Customer in Cust CTE' AS Step, * FROM Cust;

-- Step 4: Check available promotions
SELECT 'Step 4: Available Promotions' AS Step
SELECT 
    p.Id,
    p.PromotionCode,
    p.Discount,
    p.ShortDescription
FROM Promotion p 
WHERE p.PromotionCode LIKE 'My Reward %'
ORDER BY p.Discount;

-- Step 5: Check existing offers for this customer
SELECT 'Step 5: Existing Offers Check' AS Step
SELECT 
    co.PromotionId,
    co.CustomerId,
    co.DateOfferEnds,
    p.PromotionCode,
    p.Discount
FROM CustomerOffers co
JOIN Promotion p ON p.Id = co.PromotionId
WHERE co.CustomerId = 15029
    AND co.DateOfferEnds = @LastDayOfMonth;

-- Step 6: Full join simulation to see what would happen
;WITH Cust AS (
    SELECT c.CustomerId,
        EDRp = CASE
            WHEN c.MAT3mA >= 10000 AND c.MAT3mA < 15000 THEN 25
            WHEN c.MAT3mA >= 7500 AND c.MAT3mA < 10000 THEN 20
            WHEN c.MAT3mA >= 5000 AND c.MAT3mA < 7500 THEN 15
            WHEN c.MAT3mA >= 2000 AND c.MAT3mA < 5000 THEN 10
            WHEN c.MAT3mA >= 500 AND c.MAT3mA < 2000 THEN 5
            ELSE NULL
        END,
        c.Name,
        c.MAT3mA,
        c.FranchiseCode
    FROM Customer c
    WHERE c.PaymentTerms IN ('Account', 'OnHold')
        AND (c.FranchiseCode = '' or c.FranchiseCode is null)
        AND c.MYOBPastDue <= 0
        AND (C.ProductPriceCode IN ('P0', NULL, '') AND C.FreightPriceCode IN ('F0', NULL, ''))
        AND c.CustomerId = 15029
),
ExistingOffers AS (
    SELECT PromotionId, CustomerId
    FROM CustomerOffers
    WHERE DateOfferEnds = @LastDayOfMonth
        AND CustomerId = 15029
)
SELECT 'Step 6: Final Join Result' AS Step,
    c.CustomerId,
    c.Name,
    c.MAT3mA,
    c.EDRp,
    p.Id AS PromotionId,
    p.PromotionCode,
    p.Discount,
    eo.PromotionId AS ExistingPromotionId,
    CASE 
        WHEN eo.PromotionId IS NULL THEN 'WOULD CREATE RECORD'
        ELSE 'BLOCKED - EXISTING OFFER'
    END AS Result
FROM Cust c
JOIN Promotion p ON p.Discount = c.EDRp AND p.PromotionCode LIKE 'My Reward %'
LEFT JOIN ExistingOffers eo ON eo.PromotionId = p.Id AND eo.CustomerId = c.CustomerId;
