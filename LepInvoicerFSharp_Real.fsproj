<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <AssemblyName>LepInvoicerFSharp</AssemblyName>
  </PropertyGroup>

  <ItemGroup>
    <Compile Include="Types_Real.fs" />
    <Compile Include="Configuration_Real.fs" />
    <Compile Include="Database_Real.fs" />
    <Compile Include="MYOB_Real.fs" />
    <Compile Include="OrderProcessing_Real.fs" />
    <Compile Include="Program_Real.fs" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.EnvironmentVariables" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Console" Version="8.0.0" />
    <PackageReference Include="FSharp.Data.SqlClient" Version="2.0.7" />
    <PackageReference Include="Microsoft.Data.SqlClient" Version="5.1.2" />
    <PackageReference Include="FsToolkit.ErrorHandling" Version="4.15.3" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="appsettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

</Project>
