-- Find orders from the initial list that have JobsTotal = 0
-- Based on the job price calculation

USE PRD_AU;
GO

DECLARE @OrderIds TABLE (OrderId INT);

-- Insert the specific order IDs to investigate
INSERT INTO @OrderIds VALUES
(1416721), (1416747), (1417065), (1420095), (1420096), (1420098), (1420092), (1419029), (1420117), (1419424),
(1419774), (1419985), (1419914), (1419907), (1419194), (1420034), (1405289), (1419942), (1419807), (1419815),
(1419504), (1419541), (1419543), (1419544), (1404589), (1411056), (1419863), (1419859), (1418591), (1418647),
(1420062), (1419021), (1420094), (1419420), (1419830), (1420093), (1419989), (1419986), (1418227), (1418873),
(1420031), (1420009), (1419028), (1418692), (1418689), (1419173), (1420006), (1419961), (1419801), (1415818),
(1419838), (1419975);

-- Find orders with JobsTotal = 0
SELECT
    o.Id,
    o.Invoiced2,
    o.FinishDate,
    c.Name as CustomerName,
    (SELECT SUM(ISNULL(TRY_CAST(j.Price as decimal(18,2)), 0)) FROM Job j WHERE j.OrderId = o.Id) as JobsTotal,
    (SELECT COUNT(*) FROM Job j WHERE j.OrderId = o.Id) as JobCount
FROM [Order] o
    INNER JOIN Customer c ON o.userId = c.Id
    INNER JOIN @OrderIds oi ON o.Id = oi.OrderId
WHERE (SELECT SUM(ISNULL(TRY_CAST(j.Price as decimal(18,2)), 0)) FROM Job j WHERE j.OrderId = o.Id) = 0
ORDER BY o.Id;

-- Also show all orders with their JobsTotal for comparison
PRINT 'All orders from the list with their JobsTotal:';
SELECT
    o.Id,
    o.Invoiced2,
    c.Name as CustomerName,
    (SELECT SUM(ISNULL(TRY_CAST(j.Price as decimal(18,2)), 0)) FROM Job j WHERE j.OrderId = o.Id) as JobsTotal,
    (SELECT COUNT(*) FROM Job j WHERE j.OrderId = o.Id) as JobCount,
    CASE
        WHEN (SELECT SUM(ISNULL(TRY_CAST(j.Price as decimal(18,2)), 0)) FROM Job j WHERE j.OrderId = o.Id) = 0 THEN 'ZERO_JOBS_TOTAL'
        WHEN o.Invoiced2 IN ('Y', 'F', 'C') THEN 'ALREADY_INVOICED'
        WHEN o.FinishDate IS NULL THEN 'NO_FINISH_DATE'
        ELSE 'HAS_JOBS_TOTAL'
    END as Status
FROM [Order] o
    INNER JOIN Customer c ON o.userId = c.Id
    INNER JOIN @OrderIds oi ON o.Id = oi.OrderId
ORDER BY
    CASE WHEN (SELECT SUM(ISNULL(TRY_CAST(j.Price as decimal(18,2)), 0)) FROM Job j WHERE j.OrderId = o.Id) = 0 THEN 0 ELSE 1 END,
    o.Id;
