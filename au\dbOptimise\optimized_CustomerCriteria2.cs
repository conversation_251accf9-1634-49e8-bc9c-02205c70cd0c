// =============================================
// OPTIMIZED CustomerCriteria2 Method
// Performance improvements and security fixes
// =============================================

public ICriteria CustomerCriteria2(string customer, int customerid, int orderid, int jobid, bool? systemAccess,
    PaymentTermsOptions? paymentTermsOptions, bool IsPrintPortalEnabled = false, bool showArchived = false,
    string PostalPostCode = null, string SalesConsultant = null, string CustomerStatus = null,
    string RegionLep = null, string FranchiseCode = null, string BusinessType = null, string notes = null)
{
    var criteria = Session.CreateCriteria(typeof(ICustomerUser), "cust");
    
    // OPTIMIZATION 1: Early exit for specific ID searches
    if (customerid != 0)
    {
        criteria.Add(Restrictions.Eq("cust.Id", customerid));
        // If we have a specific customer ID, we can skip most other filters
        return criteria.SetCacheable(true);
    }

    // OPTIMIZATION 2: Most selective filters first (database optimization principle)
    
    // Apply exact match filters first (most selective)
    if (!string.IsNullOrEmpty(SalesConsultant))
    {
        criteria.Add(Restrictions.Eq("cust.SalesConsultant", SalesConsultant));
    }
    
    if (!string.IsNullOrEmpty(CustomerStatus))
    {
        criteria.Add(Restrictions.Eq("cust.CustomerStatus", CustomerStatus));
    }
    
    if (!string.IsNullOrEmpty(FranchiseCode))
    {
        criteria.Add(Restrictions.Eq("cust.FranchiseCode", FranchiseCode));
    }
    
    if (!string.IsNullOrEmpty(BusinessType))
    {
        criteria.Add(Restrictions.Eq("cust.BusinessType", BusinessType));
    }
    
    if (systemAccess.HasValue)
    {
        criteria.Add(Restrictions.Eq("cust.IsEnabled", systemAccess));
    }

    if (paymentTermsOptions.HasValue)
    {
        criteria.Add(Restrictions.Eq("cust.PaymentTerms", paymentTermsOptions));
    }

    if (IsPrintPortalEnabled)
    {
        criteria.Add(Restrictions.Eq("cust.IsPrintPortalEnabled", true));
    }

    // OPTIMIZATION 3: Improved postcode filtering (prefix match is much faster)
    if (!string.IsNullOrEmpty(PostalPostCode))
    {
        criteria.Add(Restrictions.Like("cust.BillingAddress.Postcode", PostalPostCode, MatchMode.Start));
    }

    // OPTIMIZATION 4: Optimized customer name/username search
    if (!String.IsNullOrEmpty(customer))
    {
        var customerDisjunction = new Disjunction();
        
        // Use prefix matching instead of anywhere for better performance
        customerDisjunction.Add(Restrictions.Like("cust.Name", customer, MatchMode.Start));
        customerDisjunction.Add(Restrictions.Like("cust.Username", customer, MatchMode.Start));
        
        // Only use expensive ANYWHERE searches if the string is long enough to be meaningful
        if (customer.Length >= 3)
        {
            customerDisjunction.Add(Restrictions.Like("cust.Name", customer, MatchMode.Anywhere));
            customerDisjunction.Add(Restrictions.Like("cust.Username", customer, MatchMode.Anywhere));
            
            // ContactsJsonStr search only for longer strings (very expensive)
            if (customer.Length >= 4)
            {
                customerDisjunction.Add(Restrictions.Like("cust.ContactsJsonStr", customer, MatchMode.Anywhere));
            }
        }
        
        criteria.Add(customerDisjunction);
    }

    // OPTIMIZATION 5: Improved Order/Job subquery with better performance
    if (orderid != 0 || jobid != 0)
    {
        var orderCriteria = DetachedCriteria.For(typeof(IOrder), "o");
        
        if (orderid != 0)
        {
            orderCriteria.Add(Restrictions.Eq("o.Id", orderid));
        }
        
        if (jobid != 0)
        {
            // Use EXISTS subquery instead of JOIN for better performance
            var jobSubquery = DetachedCriteria.For(typeof(IJob), "j")
                .Add(Restrictions.Eq("j.Id", jobid))
                .Add(Restrictions.EqProperty("j.Order.Id", "o.Id"))
                .SetProjection(Projections.Constant(1));
            
            orderCriteria.Add(Subqueries.Exists(jobSubquery));
        }
        
        orderCriteria.SetProjection(Projections.Property("o.Customer.Id"));
        orderCriteria.Add(Restrictions.EqProperty("o.Customer.Id", "cust.Id"));
        
        criteria.Add(Subqueries.Exists(orderCriteria));
    }

    // OPTIMIZATION 6: Fixed SQL injection vulnerability and improved RegionLep filter
    if (!string.IsNullOrEmpty(RegionLep))
    {
        // SECURITY FIX: Use parameterized subquery instead of string concatenation
        var regionSubquery = DetachedCriteria.For(typeof(SalesRegion), "sr")
            .Add(Restrictions.Eq("sr.LEP_Region", RegionLep))
            .SetProjection(Projections.Property("sr.PostCode"));
        
        criteria.Add(Subqueries.PropertyIn("cust.BillingAddress.Postcode", regionSubquery));
    }

    // OPTIMIZATION 7: Improved notes search with better performance
    if (!string.IsNullOrEmpty(notes))
    {
        // Only search notes if the search term is meaningful (3+ characters)
        if (notes.Length >= 3)
        {
            var notesCriteria = DetachedCriteria.For(typeof(CustomerNote), "note")
                .Add(Restrictions.Like("note.NoteText", notes, MatchMode.Anywhere))
                .SetProjection(Projections.Property("note.Customer.Id"))
                .Add(Restrictions.EqProperty("note.Customer.Id", "cust.Id"));

            criteria.Add(Subqueries.Exists(notesCriteria));
        }
    }

    // OPTIMIZATION 8: Enable query caching for reference data
    criteria.SetCacheable(true);
    
    return criteria;
}

// OPTIMIZATION 9: Add a projection-optimized version for list views
public ICriteria CustomerCriteria2WithProjection(string customer, int customerid, int orderid, int jobid, 
    bool? systemAccess, PaymentTermsOptions? paymentTermsOptions, bool IsPrintPortalEnabled = false, 
    bool showArchived = false, string PostalPostCode = null, string SalesConsultant = null, 
    string CustomerStatus = null, string RegionLep = null, string FranchiseCode = null, 
    string BusinessType = null, string notes = null)
{
    var criteria = CustomerCriteria2(customer, customerid, orderid, jobid, systemAccess, 
        paymentTermsOptions, IsPrintPortalEnabled, showArchived, PostalPostCode, 
        SalesConsultant, CustomerStatus, RegionLep, FranchiseCode, BusinessType, notes);

    // Use projection for list views to reduce data transfer
    criteria.SetProjection(Projections.ProjectionList()
        .Add(Projections.Property("cust.Id"), "Id")
        .Add(Projections.Property("cust.Name"), "Name")
        .Add(Projections.Property("cust.Username"), "Username")
        .Add(Projections.Property("cust.PaymentTerms"), "PaymentTerms")
        .Add(Projections.Property("cust.LastOrderDate"), "LastOrderDate")
        .Add(Projections.Property("cust.SalesConsultant"), "SalesConsultant")
        .Add(Projections.Property("cust.CustomerStatus"), "CustomerStatus")
        .Add(Projections.Property("cust.IsEnabled"), "IsEnabled")
        .Add(Projections.Property("cust.BillingAddress.Postcode"), "PostCode"))
        .SetResultTransformer(new AliasToBeanResultTransformer(typeof(CustomerSearchResult)));

    return criteria;
}

// OPTIMIZATION 10: Add a DTO for search results to improve performance
public class CustomerSearchResult
{
    public int Id { get; set; }
    public string Name { get; set; }
    public string Username { get; set; }
    public PaymentTermsOptions PaymentTerms { get; set; }
    public DateTime? LastOrderDate { get; set; }
    public string SalesConsultant { get; set; }
    public string CustomerStatus { get; set; }
    public bool IsEnabled { get; set; }
    public string PostCode { get; set; }
}

// OPTIMIZATION 11: Add method for count queries (pagination support)
public int CustomerCriteria2Count(string customer, int customerid, int orderid, int jobid, 
    bool? systemAccess, PaymentTermsOptions? paymentTermsOptions, bool IsPrintPortalEnabled = false, 
    bool showArchived = false, string PostalPostCode = null, string SalesConsultant = null, 
    string CustomerStatus = null, string RegionLep = null, string FranchiseCode = null, 
    string BusinessType = null, string notes = null)
{
    var criteria = CustomerCriteria2(customer, customerid, orderid, jobid, systemAccess, 
        paymentTermsOptions, IsPrintPortalEnabled, showArchived, PostalPostCode, 
        SalesConsultant, CustomerStatus, RegionLep, FranchiseCode, BusinessType, notes);

    criteria.SetProjection(Projections.RowCount());
    
    return criteria.UniqueResult<int>();
}
