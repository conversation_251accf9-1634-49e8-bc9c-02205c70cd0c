using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System;
using System.Diagnostics;
using System.Threading;
using System.Threading.Tasks;

namespace LepCore.Services
{
	public class LepCron3Min : BackgroundService
	{
		private ILogger<LepCron3Min> _logger;
		private readonly IServiceScopeFactory _serviceScopeFactory;
		

		public LepCron3Min(ILogger<LepCron3Min> logger,  IServiceScopeFactory serviceScopeFactory)
		{
		
			_serviceScopeFactory = serviceScopeFactory;
			_logger = logger;
		}

		protected override async Task ExecuteAsync(CancellationToken stoppingToken)
		{
			_logger.LogDebug("GracePeriodManagerService is starting.");

			stoppingToken.Register(() => _logger.LogDebug($" GracePeriod background task is stopping."));

			while (!stoppingToken.IsCancellationRequested)
			{
				_logger.LogDebug("GracePeriod task doing background work.");

				//
				// LORD - 916 : Disable Auto run allocation
				using (var scope = _serviceScopeFactory.CreateScope())
				{
					RunFunc(scope.ServiceProvider.GetService<lep.run.impl.RunEngine>().CronTask, "Auto Job Run Allocation");
				}

				using (var scope = _serviceScopeFactory.CreateScope())
				{
					RunFunc(scope.ServiceProvider.GetService<lep.despatch.impl.PrintEngine>().ReceivePrintQueue, "Print Engine");
				}

				using (var scope = _serviceScopeFactory.CreateScope())
				{
					RunFunc(scope.ServiceProvider.GetService<lep.jobmonitor.impl.JobBoardDTOHelper>().CreateJobBoard, "Create Job Board");
				}

				await Task.Delay(TimeSpan.FromMinutes(1), stoppingToken);
			}

			_logger.LogDebug("GracePeriod background task is stopping.");
		}

		private void RunFunc(Action a, String message)
		{
			Stopwatch sw = Stopwatch.StartNew();

			try
			{
				a();
			}
			catch (Exception ex)
			{
				_logger.LogError(ex.Message, ex);
			}
			sw.Stop();
			_logger.LogInformation("{0,-20} : {1}ms", message, sw.Elapsed.TotalMilliseconds);
		}
	}

	public class LepCron30Min : BackgroundService
	{
		private ILogger<LepCron30Min> _logger;
		private readonly IServiceScopeFactory _serviceScopeFactory;
	

		public LepCron30Min(ILogger<LepCron30Min> logger, IServiceProvider serviceProvider, IServiceScopeFactory serviceScopeFactory)
		{
		
			_serviceScopeFactory = serviceScopeFactory;
			_logger = logger;
		}

		protected override async Task ExecuteAsync(CancellationToken stoppingToken)
		{
			//_logger.LogDebug($"GracePeriodManagerService is starting.");

			stoppingToken.Register(() => _logger.LogDebug(" GracePeriod background task is stopping."));

			while (!stoppingToken.IsCancellationRequested)
			{
				_logger.LogDebug($"GracePeriod task doing background work.");

				using (var scope = _serviceScopeFactory.CreateScope())
				{
					RunFunc(scope.ServiceProvider.GetService<lep.order.IOrderApplication>().CronTask_ArchiveOrders, "ArchiveOrders");        // 24h
				}

				using (var scope = _serviceScopeFactory.CreateScope())
				{
					RunFunc(scope.ServiceProvider.GetService<lep.ConsignmentNotesMailerApplication>().CronTask, "Send consignment notes"); // 3times
				}

				await Task.Delay(TimeSpan.FromMinutes(30), stoppingToken);
			}

			_logger.LogDebug("GracePeriod background task is stopping.");
		}

		private void RunFunc(Action a, String message)
		{
			Stopwatch sw = Stopwatch.StartNew();

			try
			{
				a();
			}
			catch (Exception ex)
			{
				_logger.LogError(ex.Message, ex);
			}
			sw.Stop();
			_logger.LogInformation("{0,-20} : {1}ms", message, sw.Elapsed.TotalMilliseconds);
		}
	}
}
