namespace LepInvoicerFSharp

open System
open FsToolkit.ErrorHandling
open LepInvoicerFSharp.Types
open LepInvoicerFSharp.Database
open LepInvoicerFSharp.MYOB

// ============================================================================
// ORDER PROCESSING MODULE - Functional processing using FsToolkit.ErrorHandling
// ============================================================================

module OrderProcessing =
    
    /// Process all pending work (functional composition)
    let processAllPendingWork (config: InvoicerConfig) (myobState: MYOB.MYOBState) : Async<Result<ProcessingStats, string>> =
        asyncResult {
            let startTime = DateTime.Now
            
            // Get all pending work
            let! orders = Database.getOrdersToInvoice config config.InvoiceBatchSize
            let! credits = Database.getCreditsToInvoice config config.RefundBatchSize
            let! refunds = Database.getRefundsToInvoice config config.RefundBatchSize
            
            // Process orders
            let! orderResults = 
                orders
                |> List.map (fun (orderId, customerUsername) -> asyncResult {
                    // For now, just mark as processed (mock implementation)
                    do! Database.markOrderInvoiced orderId
                    return true
                })
                |> List.sequenceAsyncResultM
            
            // Process credits  
            let! creditResults =
                credits
                |> List.map (fun credit -> asyncResult {
                    let! invoiceNumber = MYOBService.processCredit myobState credit
                    do! Database.markCreditInvoiced credit.Id
                    return true
                })
                |> List.sequenceAsyncResultM
            
            // Process refunds
            let! refundResults =
                refunds
                |> List.map (fun refund -> asyncResult {
                    let! invoiceNumber = MYOBService.processCredit myobState refund // Same as credit
                    do! Database.markCreditInvoiced refund.Id
                    return true
                })
                |> List.sequenceAsyncResultM
            
            let endTime = DateTime.Now
            let stats = {
                OrdersProcessed = List.length orders
                OrdersSuccessful = List.length orderResults
                OrdersFailed = 0
                CreditsProcessed = List.length creditResults
                RefundsProcessed = List.length refundResults
                ElapsedTime = endTime - startTime
                Errors = []
            }
            
            return stats
        }
