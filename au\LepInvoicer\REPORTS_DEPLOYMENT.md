# FastReport Templates Deployment

## Overview

The LEP Invoicer application now includes FastReport templates as part of the project deployment, eliminating the need for hardcoded absolute paths to external template files.

## Changes Made

### 1. Project Structure
```
au/LepInvoicer/
├── reports/
│   ├── lep-invoice-order.frx     # Order invoice template
│   └── lep-invoice-refund.frx    # Credit/Refund invoice template
├── LepInvoicer.csproj            # Updated with Content includes
└── appsettings.json              # Updated with report configuration
```

### 2. Project File Updates (LepInvoicer.csproj)
```xml
<ItemGroup>
  <Content Include="reports\lep-invoice-order.frx">
    <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
  </Content>
  <Content Include="reports\lep-invoice-refund.frx">
    <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
  </Content>
</ItemGroup>
```

### 3. Configuration Updates

#### InvoicerConfiguration.cs
Added new properties:
- `ReportsFolder` - Relative path to reports folder (default: "reports")
- `OrderInvoiceTemplate` - Order template filename (default: "lep-invoice-order.frx")
- `RefundInvoiceTemplate` - Credit/Refund template filename (default: "lep-invoice-refund.frx")

Added helper methods:
- `GetOrderInvoiceTemplatePath()` - Returns full path to order template
- `GetRefundInvoiceTemplatePath()` - Returns full path to refund template

#### appsettings.json
```json
{
  "Invoicer": {
    "ReportsFolder": "reports",
    "OrderInvoiceTemplate": "lep-invoice-order.frx",
    "RefundInvoiceTemplate": "lep-invoice-refund.frx"
  }
}
```

### 4. Code Updates

#### Before (Hardcoded Paths)
```csharp
// Old hardcoded paths
var templatePath = @"C:\LepData\Labels2\lep-invoice-order.frx";
var templatePath = @"c:\LepData\Labels2\lep-invoice-refund.frx";
```

#### After (Dynamic Paths)
```csharp
// New dynamic paths relative to application
var templatePath = _config.GetOrderInvoiceTemplatePath();
var templatePath = _config.GetRefundInvoiceTemplatePath();
```

## Deployment Benefits

### ✅ **Advantages**
1. **Self-Contained**: Templates are deployed with the application
2. **No External Dependencies**: No need for `C:\LepData\Labels2\` folder
3. **Version Control**: Templates are versioned with the code
4. **Portable**: Application can be deployed anywhere
5. **Configurable**: Template names and folder can be changed via config
6. **Consistent**: Same deployment process for all environments

### ✅ **Deployment Structure**
When the application is built/published, the output structure will be:
```
bin/Debug/net8.0-windows7.0/
├── LepInvoicer.exe
├── appsettings.json
├── Tokens.json
├── reports/
│   ├── lep-invoice-order.frx
│   └── lep-invoice-refund.frx
└── [other dependencies...]
```

## Usage

### Template Path Resolution
The application automatically resolves template paths using:
```csharp
// Gets: {ApplicationDirectory}/reports/lep-invoice-order.frx
var orderTemplatePath = config.GetOrderInvoiceTemplatePath();

// Gets: {ApplicationDirectory}/reports/lep-invoice-refund.frx  
var refundTemplatePath = config.GetRefundInvoiceTemplatePath();
```

### Configuration Override
You can override the template settings in appsettings.json:
```json
{
  "Invoicer": {
    "ReportsFolder": "custom-reports",
    "OrderInvoiceTemplate": "my-order-template.frx",
    "RefundInvoiceTemplate": "my-refund-template.frx"
  }
}
```

## Migration Notes

### From LinqPad Script
- **Old**: `@"C:\LepData\Labels2\lep-invoice-order.frx"`
- **New**: `_config.GetOrderInvoiceTemplatePath()`

### From Previous C# Version
- **Old**: Hardcoded absolute paths
- **New**: Configuration-driven relative paths

## Testing

Run the included test script to verify deployment:
```powershell
powershell -ExecutionPolicy Bypass -File test_reports.ps1
```

This will verify that:
- Output directory exists
- Reports folder is created
- Both template files are deployed
- File sizes are reasonable

## Deployment Instructions

1. **Build/Publish** the application normally
2. **Copy** the entire output folder to the target server
3. **Update** appsettings.json if needed for environment-specific settings
4. **Run** the application - templates will be found automatically

No additional setup or file copying required!
