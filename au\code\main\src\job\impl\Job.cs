using lep.extensionmethods;
using lep.freight;
using lep.freight.impl;
using lep.jobmonitor;
using lep.order;

//using lep.quote;
using lep.run;
using lep.user;
using Newtonsoft.Json.Linq;
using NHibernate.Criterion;
using Serilog;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Text.RegularExpressions;
using static lep.job.JobApprovalOptions;
using static lep.job.JobCelloglazeOptions;
using static lep.job.JobStatusOptions;
using static lep.job.JobTypeOptions;
using static lep.OrderPaymentStatusOptions;
using static lep.PaymentTermsOptions;
using static lep.run.RunCelloglazeOptions;

namespace lep.job.impl
{


	/// <summary>
	/// </summary>
	[DebuggerDisplay("Job #{Id} {Status} {PrintType}   Template: {Template.Name}   Stock: {Stock.Name}  FinishedSize: {FinishedSize.PaperSize.Name}  Qty: {Quantity} ${Price}")]
	[Serializable]
	public class Job : IJob
	{
		private const string STR_StatusChangedTo = "Status changed to ";

		private const int QuoteExpireDays = 60;
		private const int PriceExpireDays = 90;
		// private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		public string HealthCss { get; set; }

		public virtual JobStatusOptions NextStatus { get; set; }

		public virtual bool HasFacilityChange { get; set; }

		private void AssertJobStatus(params JobStatusOptions[] checkstatus)
		{
			foreach (var s in checkstatus)
			{
				if (Status == s)
				{
					return;
				}
			}
			throw new ArgumentException("Job is not in appropriate status");
		}

		#region Job fields

		private JobStatusOptions status = Open;

		//    private IPrepress prepress = null;

		private string _price = "";

		#endregion Job fields

		#region constructors

		public Job()
		{
		}

		/// <summary>
		/// Standard constructor requiring mandatory fields and a template.
		/// The job is initialised based on the template and modifications are to
		/// be limited by the template for users but not staff (who can override
		/// JobTemplate parameters).
		/// TODO: Currently a required FoldedSize isn't reliably enforced
		/// </summary>
		/// <param name="order"></param>
		/// <param name="name"></param>
		/// <param name="quantity"></param>
		/// <param name="quote"></param>
		/// <param name="trackProgress"></param>
		/// <param name="specialInstructions"></param>
		/// <param name="template"></param>
		public Job(IOrder order, string name, int quantity, bool trackProgress, string specialInstructions,
			IJobTemplate template)
		{
			// it's possible a job without artwork

			if (order == null)
			{
				throw new ArgumentNullException("order");
			}
			if (name == null)
			{
				throw new ArgumentNullException("name");
			}
			if (quantity <= 0)
			{
				// throw new ArgumentOutOfRangeException("quantity");
			}
			if (template == null)
			{
				throw new ArgumentNullException("template");
			}
			Template = template;
			Order = order;
			FoldedSize = new Size();
			FinishedSize = new Size();
			Prepress = new Prepress();
			Name = name;
			Quantity = quantity;
			TrackProgress = trackProgress;
			SpecialInstructions = specialInstructions;
		}


		#endregion constructors

		#region IJob methods

		public void AddComment(IUser author, string commentText, bool staffOnly = false)
		{
			//ApplicationSecurityManager.AssertPermission("job.comment.create");
			if (author == null)
			{
				author = (IStaff)(new lep.user.impl.Staff() { Id = 1 });
			}
			var comment = new Comment(author, this, commentText, staffOnly);
			Comments.Add(comment);
		}

		public void AddStatusComment(IUser user)
		{

			//if (user.Id == 1 && Status == JobStatusOptions.PreflightDone)
			//	return;

			var sb = new StringBuilder(STR_StatusChangedTo);
			sb.Append(Status.ToDescription());

			if (sb.ToString() != STR_StatusChangedTo)
			{
				AddComment(user, sb.ToString());
			}

			//if(Status == InRun)
			//{
			//	AddComment(user, ("R" + Runs?.FirstOrDefault()?.RunNr ?? "?"), true);
			//}
		}

		public void SetPrice(IUser user, decimal engineprice, decimal quoteprice, bool isApproved)
		{
			// this.myob = myob; MYOB can be over ridden by Admin user, hence this parameter is being ignored here
			// in all the places this function is invoked, myob is received from PriceJob function.
			// prices can change, but myob does not unless changed by Admin

			engineprice = Math.Round(engineprice, 2, MidpointRounding.AwayFromZero);

			if (user is IStaff)
			{
				IsQuotePrice = quoteprice > 0 && quoteprice != engineprice;
			}

			if (quoteprice > 0 && quoteprice != engineprice)
			{
				//it's a quote price
				Price = quoteprice.ToString();
				if (isApproved)
				{
					QuoteNeedApprove = false;
				}
				else
				{
					if (user is IStaff)
					{
						QuoteNeedApprove = true;
						if (QuoteOutcome != QuoteStatus.Lost.ToString())
						{
							QuoteOutcome = QuoteStatus.Pending.ToString();
							AddComment(user, "Awaiting Quote Approval");
						}

					}
				}
			}
			else if (engineprice > 0)
			{
				//it's engine price
				Price = engineprice.ToString();
				QuoteNeedApprove = false;
			}
			else
			{
				//request quote
				Price = "";
				if (QuoteOutcome != QuoteStatus.Lost.ToString())
				{
					QuoteOutcome = QuoteStatus.Requested.ToString();
				}
				QuoteNeedApprove = false;
			}
		}

		public IArtwork AddArtwork(string position)
		{
			IArtwork art = new Artwork(position, this);
			Artworks.Add(art);
			return art;
		}

		public IArtwork GetArtwork(string position)
		{
			return Artworks.FirstOrDefault(_ => _.Position == position);

			//foreach (var art in Artworks)
			//{
			//	if (art.Position == position)
			//	{
			//		return art;
			//	}
			//}
			//return null;
		}
		public string JobFolderName => $"{JobNr}-{Quantity * 1.0 / 1000:0.#}k";

		public bool HasArtwork(string position)
		{
			return Artworks.FirstOrDefault(_ => _.Position == position) != null;
		}


		//check if artwork valid for submit
		public bool IsArtworkValidForSubmit()
		{
			//quote required, artwork not mandatory
			if (String.IsNullOrEmpty(Price))
			{
				return true;
			}

			if (HasArtwork("multiart"))
			{
				return true;
			}


			if (GetRequiredPosition().All(HasArtwork))
				return true;

			return false;
		}

		public bool IsArtworkValidForPreflight()
		{
			if (GetArtwork("multiart") != null && !String.IsNullOrEmpty(GetArtwork("multiart").Ready))
			{
				return true;
			}

			if (IsMagazine() || IsPresentationFolder())
			{
				foreach (var ar in Artworks)
				{
					if (!String.IsNullOrEmpty(ar.Ready))
					{
						return true;
					}
				}
			}

			return GetRequiredPosition().All(p => GetArtwork(p) != null && !String.IsNullOrEmpty(GetArtwork(p).Ready));
		}

		public bool IsQuoteExpired
		{
			get {
				return !String.IsNullOrEmpty(Price) && IsQuotePrice && DateTime.Now.AddDays(-QuoteExpireDays) > PriceDate;
			}
		}

		public bool IsAutoPriceExpired
		{
			get {
				return !String.IsNullOrEmpty(Price) && !IsQuotePrice &&
					   DateTime.Now.AddDays(-PriceExpireDays) > PriceDate;
			}
		}

		#endregion IJob methods



		#region  Quote Fields
		public virtual string QuoteEstimator { get; set; }
		public virtual decimal? QuoteCOGS { get; set; }
		public virtual decimal? QuoteOutworkCost { get; set; }

		public virtual string QuoteComments { get; set; }
		public virtual string QuotePrimary { get; set; }

		public virtual string QuoteOutcome { get; set; }
		public virtual string QuoteFollowUpNotes { get; set; }

		public virtual string QuoteLostReason { get; set; }
		public virtual string QuoteLostComments { get; set; }
		#endregion




		#region approval methods

		public bool IsOptionEditable(IUser currentUser)
		{
			//if (!Enable) {
			//	if(Order.Customer.PaymentTerms == PaymentTermsOptions.COD) {
			//		return false;
			//	}
			//}

			//LORD-1376: Allow customers to be able to amend price and resubmit Orders previously returned as �Unable to Meet Price�
			//if (Status == UnableToMeetPrice)
			//	return false;

			if (Status == RejectedVariation)
				return false;

			if (!IsQuotePrice && Order.CanUpdate(currentUser))
			{
				if (IsOpenish() || status == Submitted)
				{
					if (String.IsNullOrEmpty(Price) || !QuoteNeedApprove)
					{
						return true;
					}
				}
			}

			return false;
		}

		public bool IsArtworkEditable()
		{
			//if (!Enable) return true;
			if (status == Open)
				return true;
			if (ReOrderSourceJobId != 0)
				return false;

			if (SupplyArtworkApproval == Rejected)
			{
				return true;
			}

			//todo: iwh check for reject
			if (ReadyArtworkApproval == Rejected)
			{
				return true;
			}
			if (status >= Submitted)
			{
				if (IsQuotePrice && !Artworks.Any())
					return true;

				return false;
			}
			return true;
		}

		public bool IsAllowPreflight()
		{
			if (HasReject || NeedApproval)
			{
				return false;
			}
			if (status != Open && status != Submitted)
			{
				return false;
			}
			return IsArtworkValidForPreflight();
		}

		//CR26

		public bool IsBusinessCard() => Template?.Is(BusinessCard, BusinessCardNdd, BusinessCardSdd, DoubleBusinessCard, Postcard) ?? false;

		public bool IsPartOfMultiJobOrder()
		{
			return Order.Jobs.Count() > 1;
		}

		public bool IsFurtherProcessingRequired() => RoundOption != RoundOption.None || NumberOfMagnets > 0 || HoleDrilling != HoleDrilling.None || Scoring;

		public bool IsFurtherProcessingRequiredForOtherJobsInOrder() => Order.Jobs.Where(x => x.Id != Id).Any(x => x.IsFurtherProcessingRequired());

		public bool IsFurtherProcessingRequiredForAnyJobInOrder() => Order.Jobs.Any(x => x.IsFurtherProcessingRequired());

		public bool IsCustomerGood()
		{
			if (Order.Customer.PaymentTerms == OnHold)
				return false;

			if (Order.Customer.PaymentTerms == Account)
				return true;

			if (Order.PaymentStatus == AwaitingPayment)
				return false;

			if (Order.PaymentStatus.Is(OrderPaymentStatusOptions.NotNeeded, Paid))
				return true;

			return true;
		}

		public bool IsTheLastJobInOrderBeingProcessed(Facility facility)
		{
			var activeJobs = Order.Jobs.Where(_ => _.Facility == facility);
			var totalJobs = activeJobs.Count();
			//var scannedSofar = activeJobs.Count(_ => _.ScanCount > 0);
			var countOfPacked = activeJobs.Count(_ => _.Id != Id && _.Status.Is(Packed, Dispatched, PayMe));

			var result = (countOfPacked == (totalJobs - 1)) || (countOfPacked == totalJobs);
			Log.Information($"{Id} last job??? {result}");
			return result;
		}

		//public bool IsTheLastJobInOrderBeingProcessed()
		//{
		//	var activeJobs = Order.Jobs;
		//	var totalJobs = activeJobs.Count();
		//	//var scannedSofar = activeJobs.Count(_ => _.ScanCount > 0);
		//	var countOfPacked = activeJobs.Count(_ => _.Id != Id && _.Status.Is(Packed, Dispatched, PayMe));

		//	var result = (countOfPacked == (totalJobs - 1)) || (countOfPacked == totalJobs);
		//	Log.Information($"{Id} last job??? {result}");
		//	return result;
		//}

		public bool JobsOrderIsInSingleRun()
		{
			var orderId = Order.Id;

			var runIds = (from j in Order.Jobs where j.Runs.Count > 0 select j.Runs[0].Id).ToList();

			var distinctCount = runIds.Distinct().Count();
			return distinctCount == 1;
		}

		public void JobSaveState(IUser user, bool awaitingArtworkApproval)
		{
			// rdyartwork submited, require approval
			if (awaitingArtworkApproval)
			{
				AssertJobStatus(Submitted, Open);
				//ApplicationSecurityManager.AssertPermission("job.preflight");
				ReadyArtworkApproval = NeedsApproval;
				AddComment(user, "Awaiting Artwork Approval");
			}

			if (IsAllowPreflight())
			{
				//ApplicationSecurityManager.AssertPermission("job.preflight");
				Status = PreflightDone;
				AddStatusComment(user);
			}
		}

		public void ApproveQuote(IUser reviewer)
		{
			if (!QuoteNeedApprove)
			{
				//	throw new ArgumentException("Customer approval not needed");
			}
			//ApplicationSecurityManager.AssertIsCustomerUser(Order.Customer);
			//ApplicationSecurityManager.AssertPermission("job.approve");

			QuoteNeedApprove = false;
			QuoteOutcome = QuoteStatus.WonPendingSubmitted.ToString();
			AddComment(reviewer, "Quote Approved");


			//LORD:972 JobSaveState(reviewer, false);
		}

		public void ApproveArtwork(IUser reviewer)
		{
			if (ReadyArtworkApproval != NeedsApproval)
			{
				throw new ArgumentException("Customer approval not needed");
			}
			AssertJobStatus(Open);
			//ApplicationSecurityManager.AssertIsCustomerUser(Order.Customer);
			//ApplicationSecurityManager.AssertPermission("job.approve");

			ReadyArtworkApproval = JobApprovalOptions.NotNeeded;
			AddComment(reviewer, "Artwork Approved");
			JobSaveState(reviewer, false);
		}

		public void RejectArtwork(IUser reviewer, string commentText)
		{
			if (ReadyArtworkApproval != NeedsApproval)
			{
				throw new ArgumentException("Customer approval not needed");
			}
			AssertJobStatus(Open);
			//ApplicationSecurityManager.AssertIsCustomerUser(Order.Customer);
			//ApplicationSecurityManager.AssertPermission("job.approve");

			ReadyArtworkApproval = Rejected;
			HasRejectedBefore = true;
			if (commentText != null && commentText.Trim().Length > 0)
			{
				AddComment(reviewer, commentText);
			}
			AddComment(reviewer, "Artwork Rejected");
		}

		public void Reject(IStaff staff, string commentText)
		{
			AssertJobStatus(Submitted, PreflightDone);
			//ApplicationSecurityManager.AssertPermission("job.preflight");

			SupplyArtworkApproval = Rejected;
			Urgent = false;
			Status = Submitted;
			NextStatus = Open;
			HasRejectedBefore = true;
			if (commentText != null && commentText.Trim().Length > 0)
			{
				AddComment(staff, "Job Rejected: " + commentText);
			}
			else
			{
				AddComment(staff, "Job Rejected");
			}
		}

		public void AcceptSupplyArtwork(IUser customer)
		{
			AssertJobStatus(Open, Submitted);
			SupplyArtworkApproval = JobApprovalOptions.NotNeeded;
			AddComment(customer, "Supplied artwork checked");
		}

		public void Submit(IUser user)
		{
			AssertJobStatus(Open, UnableToMeetPrice);
			//ApplicationSecurityManager.AssertIsCustomerUser(Order.Customer);
			//ApplicationSecurityManager.AssertPermission("job.submit");

			Status = Submitted;
			ReceivedDate = DateTime.Now;

			if (IsQuoteExpired)
			{
				throw new ArgumentException("Quote expired");
			}

			if (!IsArtworkValidForSubmit() && Order.PriceOfJobs.HasValue)
			{
				throw new ArgumentException("Artwork required");
			}
			AddComment(user, "Job Submit");
		}

		public bool HasReject => SupplyArtworkApproval == Rejected ||
								 ReadyArtworkApproval == Rejected;

		public bool NeedApproval => SupplyArtworkApproval == NeedsApproval ||
									ReadyArtworkApproval == NeedsApproval || QuoteNeedApprove;

		public void AddToRun(IStaff staff, IRun run, bool addComment = true)
		{
			AssertJobStatus(PreflightDone);
			//ApplicationSecurityManager.AssertPermission("job.prepress");

			Runs.Add(run);
			Status = InRun;
			if (addComment)
			{
				AddStatusComment(staff);
			}

		}

		public void RemoveFromRun(IStaff staff, IRun run, bool addComment = true)
		{
			//AssertJobStatus(new JobStatusOptions[] { InRun, Filling });
			//ApplicationSecurityManager.AssertPermission("job.prepress");
			var statusBefore = Status;
			Runs.Remove(run);
			if (Runs.Count == 0)
			{
				Status = PreflightDone; // Note we do not want to add comment in this case as it seems someone else is preflighting file

				// LORD-983 Incorrect 'Preflight Done' status changes appearing in comments list
				// Status transition is from inRun -> PreflightDone ie backwords do not add a comment.
				// this can happen when prepress moves jobs between runs or manually manages run
				if (Status == PreflightDone && statusBefore >= PreflightDone)
					addComment = false;
			}
			if (addComment)
			{
				AddStatusComment(staff);
			}
		}

		//public void Withdraw(IUser user)
		//{
		//	//AddComment(user, "Job Withdraw");
		//	//foreach (var r in runs) {
		//	//	if (r.Jobs.Contains(this)) {
		//	//		r.Jobs.Remove(this);
		//	//	}
		//	//}
		//	//runs.Clear();
		//	//Status = JobStatusOptions.Open;
		//	//IsWithdrawn = true;
		//	//Order.Withdraw(user);

		//	//Enable = false;
		//	Status = JobStatusOptions.Open;
		//	AddComment(user, "Job Withdraw");
		//	foreach (var r in runs) {
		//		if (r.Jobs.Contains(this)) {
		//			r.Jobs.Remove(this);
		//		}
		//	}
		//	runs.Clear();

		//	//var canwithdraw = true;
		//	foreach (var j in Order.Jobs) {
		//		//if (j.Enable) {
		//		//	canwithdraw = false;
		//		//	break;
		//		//}
		//	}
		//	//if (canwithdraw) {
		//	//	Order.Enable = false;
		//	//	Order.Status = OrderStatusOptions.Open;
		//	//}
		//}

		public bool CanWithdraw(IUser user)
		{
			//if (!Enable) {
			//	return false;
			//}
			if (user is IStaff)
			{
				if (status != Complete && status != Open)
				{
					return true;
				}
			}
			if (user is ICustomerUser)
			{
				if (status == Submitted)
				{
					return true;
				}
			}
			return false;
		}

		//public void Reactivate(IUser user)
		//{
		//	Enable = true;
		//	AddComment(user, "Job Reactivate");

		//	if (status == JobStatusOptions.Submitted || status == JobStatusOptions.Open) {
		//		if (Order.Status == OrderStatusOptions.Submitted) {
		//			Status = JobStatusOptions.Submitted;
		//			NextStatus = JobStatusOptions.PreflightDone;
		//		} else {
		//			Status = JobStatusOptions.Open;
		//			NextStatus = JobStatusOptions.Open;
		//		}
		//	}
		//}

		//public bool CanReactivate(IUser user)
		//{
		//	if (Enable) {
		//		return false;
		//	}
		//	if (user is IStaff) {
		//		if (Order.Status == OrderStatusOptions.Open || Order.Status == OrderStatusOptions.Submitted) {
		//			return true;
		//		}
		//	}
		//	if (user is ICustomerUser) {
		//		if (Order.Status == OrderStatusOptions.Open || Order.Status == OrderStatusOptions.Submitted) {
		//			return true;
		//		}
		//	}
		//	return false;
		//}

		public void SetStatus(JobStatusOptions s, IUser user)
		{
			//if (Status == s) {	return;	}

			//if (s == Packed && Status == PayMe) return;
			Status = s;

			foreach (var r in Runs)
			{
				r.SetStatus();
			}
			//AddStatusComment(ApplicationSecurityManager.CurrentUser ?? ApplicationSecurityManager.SystemUser);

			AddStatusComment(user);

			if (s == Packed || s == PayMe)
			{
				var orderPacked = Order.Jobs.All(j => /*!j.Enable ||*/ j.Status == Packed || j.Status == Dispatched || j.Status == Complete || j.Status == PayMe);
				if (orderPacked)
				{
					Order.Status = OrderStatusOptions.Finished;

					if (Order.FinishDate == null)
					{
						Order.FinishDate = DateTime.Now;
					}

					// If this job is being scanned as PayMen and if there are more jobs in this order
					// Then the other jobs in this order goes as PayMe with a comment
					if (s == PayMe && Order.Jobs.Count() > 1)
					{
						Order.Jobs.Where(j => j.Id != Id).ForEach(j =>
						{
							j.Status = PayMe;
							j.AddComment(user, "Job marked as PayMe, since last job on order was marked as PayMe");
						});
					}

					if (s == Packed && Order.Jobs.Count() > 1)
					{
						Order.Jobs.Where(j => j.Id != Id && j.Status == PayMe).ForEach(j =>
						{
							j.Status = Packed;
						});
					}
				}
			}
			else if (s == Dispatched || s == Complete)
			{
				var orderDispatch = Order.Jobs.All(j =>/* !j.Enable || */ j.Status == Dispatched || j.Status == Complete);
				if (orderDispatch)
				{
					Order.Status = OrderStatusOptions.Dispatched;

					if (Order.DispatchDate == null)
					{
						Order.DispatchDate = DateTime.Now;
					}

					if (Order.FinishDate == DateTime.MinValue || Order.FinishDate == null)
					{
						Order.FinishDate = DateTime.Now;
					}
				}
			}
			// status to go backwards LORD63
			else if (s == Submitted)
			{
				var orderSubmitted = Order.Jobs.All(j => /*!j.Enable ||*/ j.Status <= Submitted);
				if (orderSubmitted)
				{
					Order.Status = OrderStatusOptions.Submitted;
				}
			}
		}

		public virtual bool IsCMYK => BackPrinting != JobPrintOptions.Unprinted;

		public virtual RunCelloglazeOptions Celloglaze
		{
			get {
				switch (FinalFrontCelloglaze)
				{
					case Gloss:
						if (FinalBackCelloglaze == Gloss)
							return GlossBoth;
						else if (FinalBackCelloglaze == Matt)
							return GlossFrontMattBack;
						else if (FinalBackCelloglaze == JobCelloglazeOptions.None)
							return GlossFront;
						break;
					case Matt: return FinalBackCelloglaze == Matt ? MattBoth : MattFront;
					case Velvet: return FinalBackCelloglaze == Velvet ? VelvetBoth : VelvetFront;
					case SpotUV: return SpotUVFrontMattBoth;
					case JobCelloglazeOptions.SpotUVFrontMattFront: return RunCelloglazeOptions.SpotUVFrontMattFront;
					case EmbossedGlossFront: return EmbossGlossFront;
					case EmbossedMattFront: return EmbossMattFront;
					case Foil: return FoilFrontMattBoth;
					case Emboss: return EmbossGlossFront;
					case MattAntiScuff:
						if (FinalBackCelloglaze == MattAntiScuff)
							return MattAntiScuffBoth;
						else if (FinalBackCelloglaze == JobCelloglazeOptions.None)
							return MattAntiScuffFront;
						break;
					default: return RunCelloglazeOptions.None;
				}
				return RunCelloglazeOptions.None;
			}
		}

		public virtual int DisplayJobSize()
		{
			var slotsize = CalculateJobSize();
			if (slotsize > 42)
			{
				return 1;
			}
			return slotsize;
		}

		public virtual int GetBaseSpace()
		{
			var basespace = 1;

			switch ((JobTypeOptions)Template.Id)
			{
				case BusinessCard:
				case BusinessCardNdd:
				case BusinessCardSdd:
				case DoubleBusinessCard:
					basespace = 1;
					if (FinishedSize.PaperSize != null && FinishedSize.PaperSize.Name == "Custom")
					{
						if ((FinishedSize.Width <= 90 && FinishedSize.Height <= 55) ||
							(FinishedSize.Height <= 90 && FinishedSize.Width <= 55))
							basespace = 1;
						else
							return CustomSlot;
					}
					if (FinishedSize.PaperSize != null && (FinishedSize.PaperSize.Id == 2 ||
														   FinishedSize.PaperSize.Id == 20))
					{

						basespace = 2;
					}
					else
					{
						basespace = 1;
					}
					break;

				case Postcard:
					basespace = 4;
					if (FinishedSize.PaperSize != null && FinishedSize.PaperSize.Name == "Custom")
					{
						if ((FinishedSize.Width <= 90 && FinishedSize.Height <= 55) ||
							(FinishedSize.Height <= 90 && FinishedSize.Width <= 55))
							basespace = 1;
						else
							return CustomSlot;
					}

					break;

				default:
					return 0;
			}

			return basespace;
		}

		public virtual int CalculateJobSize()
		{
			try
			{
				if (Status > JobStatusOptions.Filling)
				{
					if (Runs != null && Runs[0] != null && Runs[0].Status > RunStatusOptions.Filling)
					{
						var r = Runs[0];
						if (r.QtyList != null && !string.IsNullOrEmpty(r.QtyList))
						{

							dynamic d = JObject.Parse(r.QtyList);
							int y = d[Quantity.ToString()].Ups * GetBaseSpace() ?? 1;
							return y;

						}
					}
				}

			}
			catch (Exception e)
			{
				return 1;
			}

			var basespace = GetBaseSpace();
			var mult = 1;

			// 1 base per 1000
			mult = Quantity / 1000;

			// or part thereof
			if (Quantity % 1000 != 0)
			{
				mult++;
			}
			return basespace * mult;
		}

		public virtual IJob CopyJob()
		{
			Regex r = new Regex(@"Copy \d+ of \(" + Name + @"\)");
			var c = Order.Jobs.Count(j => r.Match(j.Name).Success) + 1;
			var newName = $"Copy {c} of ({Name})";

			var copy = new Job(Order, newName, Quantity, TrackProgress, SpecialInstructions, Template);
			copy.CustomSlot = CustomSlot;
			copy.Stock = Stock;
			copy.StockOverride = StockOverride;
			copy.FrontPrinting = FrontPrinting;
			copy.BackPrinting = BackPrinting;
			copy.FrontCelloglaze = FrontCelloglaze;
			copy.BackCelloglaze = BackCelloglaze;
			copy.FrontCelloglazeOverride = FrontCelloglazeOverride;
			copy.BackCelloglazeOverride = BackCelloglazeOverride;

			copy.FoilColour = FoilColour;
			copy.Envelope = Envelope;
			copy.EnvelopeType = EnvelopeType;

			copy.Rotation = Rotation;
			copy.FinishedSize = FinishedSize;
			copy.FoldedSize = FoldedSize;
			copy.RoundOption = RoundOption;
			copy.HoleDrilling = HoleDrilling;
			copy.NumberOfHoles = NumberOfHoles;
			copy.PadDirection = PadDirection;
			copy.NumberOfMagnets = NumberOfMagnets;
			copy.Pages = Pages;
			copy.SelfCovered = SelfCovered;
			copy.StockForCover = StockForCover;
			copy.Urgent = Urgent;
			copy.DieCutType = DieCutType;
			copy.Status = Open;
			//copy.MYOB = MYOB;
			copy.RoundDetailOption = RoundDetailOption;
			copy.TLround = TLround;
			copy.TRround = TRround;
			copy.BLround = BLround;
			copy.BRround = BRround;
			copy.Scoring = Scoring;
			copy.ScoringInstructions = ScoringInstructions;
			copy.Perforating = Perforating;
			copy.PerforatingInstructions = PerforatingInstructions;
			copy.PrintType = PrintType;
			copy.BindingOption = BindingOption;
			copy.Facility = Facility;
			copy.SendSamples = SendSamples;

			copy.NumberOfHoles = NumberOfHoles;

			copy.BrochureDistPackInfo = new BrochureDistPackInfo(this.BrochureDistPackInfo);

			if (Stock.Id == 18)
			{
				copy.Stock = new lep.job.impl.Stock() { Id = 91 };
				copy.AddComment(null, "'400 GSM Deluxe Artboard' is replaced by '420 GSM Deluxe Artboard'");
			}

			return copy;
		}

		public IPressDetail CreatePressDetail()
		{
			return new PressDetail();
		}

		#endregion approval methods

		#region IJob Properties

		public virtual int Id { get; set; }

		public virtual string JobNr => Id.ToString();
		public virtual string Barcode => $"J{JobNr}";
		public virtual string Name { get; set; }

		public virtual IJobTemplate Template { get; set; }

		public virtual IOrder Order { get; set; }

		public virtual JobStatusOptions Status
		{
			get { return status; }
			set {
				//if (status == value) return;
				StatusDate = DateTime.Now;
				status = value;
				if (status == JobStatusOptions.UnableToMeetPrice)
					return;
				NextStatus = StandardRouting.Instance.GetNextRouteForJob(this);
			}
		}

		public virtual DateTime StatusDate { get; set; } = DateTime.Now;

		public virtual decimal ThicknessOfSingleJob { get; set; }
		public virtual int Quantity { get; set; }

		public virtual RotationOption Rotation { get; set; } = RotationOption.Portrait;
		public virtual JobBoundEdgeOptions BoundEdge { get; set; } = JobBoundEdgeOptions.None;

		public virtual DateTime? ReceivedDate { get; set; }

		public virtual string ArtSuppliedVia { get; set; } = "";

		public virtual IPrepress Prepress { get; set; } = new Prepress();

		public virtual IStaff PrepressCheckedBy { get; set; } = null;

		public virtual IStaff PrintedBy { get; set; } = null;

		public virtual DateTime? PrintedDate { get; set; }

		public virtual int NumPressSheets { get; set; } = 0;

		public virtual string Folding { get; set; } = "";
		public virtual bool Scoring { get; set; } = false;
		public virtual string ScoringInstructions { get; set; } = "";
		public virtual bool Perforating { get; set; } = false;
		public virtual string PerforatingInstructions { get; set; } = "";
		public virtual CutOptions DieCutType { get; set; } = CutOptions.None;
		public virtual string DieCutting { get; set; } = "";
		public virtual PadDirection PadDirection { get; set; } = PadDirection.None;
		public virtual string RequestedPackaging { get; set; } = "";
		public virtual string ActualPackaging { get; set; } = "";
		public virtual string DispatchRequirements { get; set; } = "";

		public virtual bool NCRNumbered { get; set; }
		public virtual string NCRStartingNumber { get; set; }
		public virtual NCRInfo NCRInfo { get; set; }

		public virtual string SpecialInstructions { get; set; } = "";
		public virtual string LepSpecialInstructions { get; set; } = "";
		public virtual bool SendSamples { get; set; }
		public virtual string ProductionInstructions { get; set; } = "";
		public virtual int ReOrderSourceJobId { get; set; }
		public virtual IStaff FinishedBy { get; set; } = null;
		public virtual DateTime? FinishedDate { get; set; }
		public virtual ArtworkStatusOption ArtworkStatus { get; set; }
		public virtual JobProofStatus ProofStatus { get; set; }
		public virtual IList<IArtwork> Artworks { get; set; } = new List<IArtwork>();
		public virtual string Preview { get; set; } = "";
		public virtual DateTime? DispatchDate { get; set; }
		public virtual DateTime? PrintByDate { get; set; }

		public virtual IList<IComment> Comments { get; set; } = new List<IComment>();
		public virtual IList<IPressDetail> PressDetails { get; set; } = new List<IPressDetail>();

		public virtual IList<IRun> Runs { get; set; } = new List<IRun>();

		public virtual JobApprovalOptions ReadyArtworkApproval { get; set; } = JobApprovalOptions.NotNeeded;
		public virtual JobApprovalOptions SupplyArtworkApproval { get; set; } = JobApprovalOptions.NotNeeded;
		public virtual bool HasRejectedBefore { get; set; } = false;
		public virtual bool QuoteNeedApprove { get; set; } = false;

		//public virtual bool Enable { get; set; } = true;
		public virtual bool Urgent { get; set; } = false;

		public virtual IStock Stock { get; set; }
		public virtual IStock StockOverride { get; set; }

		public IStock FinalStock {
			get {
				if (StockOverride != null && StockOverride.Id != 0)
					return StockOverride;
				else
					return Stock;
			}
		}
		public virtual JobPrintOptions FrontPrinting { get; set; } = JobPrintOptions.Unprinted;
		public virtual JobPrintOptions BackPrinting { get; set; } = JobPrintOptions.Unprinted;
		public virtual JobCelloglazeOptions FrontCelloglaze { get; set; } = JobCelloglazeOptions.None;
		public virtual JobCelloglazeOptions BackCelloglaze { get; set; } = JobCelloglazeOptions.None;


		public virtual JobCelloglazeOptions? FrontCelloglazeOverride { get; set; }
		public virtual JobCelloglazeOptions? BackCelloglazeOverride { get; set; }


		public JobCelloglazeOptions FinalFrontCelloglaze {
			get {
				if( FrontCelloglazeOverride != null)
					return FrontCelloglazeOverride.Value;
				else
					return FrontCelloglaze;
			}
		}
		public JobCelloglazeOptions FinalBackCelloglaze {
			get {
				if( BackCelloglazeOverride != null)
					return BackCelloglazeOverride.Value;
				else
					return BackCelloglaze;
			}
		}

		// foil & envelope for Brochure IInvitation packs
		public virtual string FoilColour { get; set; }

		public virtual string Envelope { get; set; }
		public virtual string EnvelopeType { get; set; }

		public virtual ISize FinishedSize { get; set; }
		public virtual ISize FoldedSize { get; set; } = null;
		public virtual int Pages { get; set; } = 0;
		public virtual bool SelfCovered { get; set; } = false;
		public virtual IStock StockForCover { get; set; } = null;
		public virtual IStock StockForCoverOverride { get; set; } = null;
		public IStock FinalStockForCover {
			get {
				if (StockForCoverOverride != null && StockForCoverOverride.Id != 0)
					return StockForCoverOverride;
				else
					return StockForCover;
			}
		}
		//public virtual bool Magnet { get; set; } = false;
		public virtual int NumberOfMagnets { get; set; } = 0;
		public virtual RoundOption RoundOption { get; set; } = RoundOption.None;
		public virtual RoundDetailOption RoundDetailOption { get; set; } = RoundDetailOption.None;
		public virtual string CustomDieCut { get; set; }
		public virtual bool TRround { get; set; }
		public virtual bool TLround { get; set; }
		public virtual bool BRround { get; set; }
		public virtual bool BLround { get; set; }
		public virtual int? NumberOfHoles { get; set; } = null;
		public virtual HoleDrilling HoleDrilling { get; set; } = HoleDrilling.None;
		public virtual IProofs Proofs { get; set; } = new Proofs(false, 0, 0, 0);
		public virtual bool TrackProgress { get; set; } = true;
		public virtual bool IsQuotePrice { get; set; }

		public virtual decimal? CustomerRequestedPrice { get; set; }
		public virtual DateTime? PriceDate { get; set; }
		public virtual string PriceBase { get; set; } = "";
		public virtual decimal PriceMargin { get; set; } = 0;
		public virtual string PriceMarginValue { get; set; } = "";
		public virtual string ProductPriceCode { get; set; } = "";

		public virtual string Price
		{
			get => _price;
			set {
				if (_price != value)
				{
					_price = value;
					PriceDate = DateTime.Now;
				}
			}
		}

		public virtual bool IsWhiteLabel { get; set; }
		public virtual string PriceWL { get; set; }

		public virtual IFreight Freight { get; set; } = new Freight();

		public IUser CreatedBy { get; set; }
		public virtual DateTime DateCreated { get; set; }
		public virtual DateTime DateModified { get; set; }
		public virtual bool Printed { get; set; } = false;
		public virtual bool MailedPrePayment { get; set; } = false;
		public virtual bool MailedGonePlate { get; set; } = false;
		public virtual bool MailedGoneFinish { get; set; } = false;
		public virtual bool MailedComplete { get; set; } = false;
		public virtual bool MailedCompletePayment { get; set; } = false;
		public virtual int CustomSlot { get; set; } = 0;
		//public virtual string MYOB { get; set; }
		public virtual DateTime? RequiredByDate { get; set; }
		public virtual bool IsReprint { get; set; }
		public bool IsRestart => (IsReprint && ReprintFromPreviousJobNo == Id);
		public virtual bool IsReprintJobDispatch { get; set; }
		public virtual string ReprintReason { get; set; }
		public virtual string ReprintReasonPredefined { get; set; }
		public virtual string ReprintResult { get; set; }
		public virtual IUser ReprintBy { get; set; }
		public virtual IBindingOption BindingOption { get; set; }
		public virtual Facility? Facility { get; set; }
		public virtual bool IsCustomFacility { get; set; }
		public virtual int ScanCount { get; set; }

		public virtual bool InvolvesOutwork { get; set; }




		public IList<string> GetRequiredPosition()
		{

			IList<string> positions = new List<string>();
			if (IsMagazine())
			{
				positions.Add("Cover");
				positions.Add("Text");

				if (FinalFrontCelloglaze != null && FinalFrontCelloglaze == JobCelloglazeOptions.SpotUVFrontMattFront)
				{
					positions.Add("Spot UV");
				}
			}
			else
			{
				if (DieCutType == CutOptions.Custom)
				{
					positions.Add("diecut");
				}
				if (FrontPrinting != JobPrintOptions.Unprinted)
				{
					positions.Add("front");
				}
				if (BackPrinting != JobPrintOptions.Unprinted)
				{
					positions.Add("back");
				}

				if (Template.Is(VinylSticker) && FinishedSize.PaperSize.Name == "Custom")
				{
					positions.Add("Cut Contour");
				}

			}
			return positions;
		}

		public virtual PrintType PrintType { get; set; }
		public virtual PrintType? ForcedPrintType { get; set; }

		#endregion IJob Properties

		#region Reprint / Restart info

		public virtual int ReprintFromPreviousJobNo { get; set; }
		public virtual int ReprintFromPreviousRunNo { get; set; }
		public virtual string NCRNo { get; set; }
		public virtual string ReprintCost { get; set; }

		#endregion Reprint / Restart info

		public string StatusCss
		{
			get {
				if (NeedApproval || QuoteNeedApprove)
					return "purple";

				if (HasReject || ProofStatus == JobProofStatus.OnHold)
					return "red";

				if (IsOpenish())
					return "#d56600";

				if (Status == Submitted)
					return "green";

				return "";
			}
		}

		public bool IsOpenish() => Status == Open || Status == UnableToMeetPrice || status == RejectedVariation;

		public string Render(bool forStaff)
		{
			if (Status == RejectedVariation)
			{
				return "Rejected Variation";
			}
			if (NeedApproval)
			{
				return "Approval required";
			}
			else if (HasReject)
			{
				return "Rejected";
			}

			if (ArtworkStatus == ArtworkStatusOption.LATER)
			{
				return "Waiting for Artwork";
			}

			if (ProofStatus == JobProofStatus.OnHold)
			{
				return "On Hold";
			}
			var isStaff = forStaff;
			switch (Status)
			{
				case Open:
					return "Not Submitted";

				case Submitted:
					return isStaff ? "New Order" : "Submitted";

				case DPCPreProduction:
					return isStaff ? "In DPC Pre Production" : "In Pre Production";

				case DPCPrinted:
					return isStaff ? "In DPC Printed" : "In Printed";

				case DPCComplete:
					return "DPC Complete";

				case WideFormatProduction:
					return isStaff ? "In Wide Format Production" : "In Production";

				case WideFormatComplete:
					return "Wide Format Complete";

				case PreflightDone:
					return isStaff ? "Preflight done" : "Prepress Commenced";

				case InRun:
					return isStaff ? "In run" : "In Prepress";

				case Filling:
					return "Print Run Filling";

				case LayoutRequired:
					return "Layout in Progress";

				case LayoutDone:
					return "Layout Completed";

				case Cut:
					return isStaff ? "Cut" : "Guillotine completed";

				case ApprovedForPlating:
					return "Plating in Progress";

				case PlatingDone:
					return "Plating Completed";

				case PressDone:
					return "Printed";

				case Celloglazed:
					return isStaff ? "Celloglazed" : "Celloglazing completed";

				case Folded:
					return isStaff ? "Folded" : "Folding completed";

				case Letterpressed:
					return isStaff ? "Diecut/Scored" : "Diecut/Score completed";

				case Stitched:
					return isStaff ? "Stitched" : "Stitching completed";

				case Scored:
					return isStaff ? "Scored" : "Scoring completed";

				case Drilled:
					return isStaff ? "Drilled" : "Drilling completed";

				case Rounded:
					return isStaff ? "Rounded" : "Rounding completed";

				case Perforated:
					return isStaff ? "Perforated" : "Perforating completed";

				case Outwork:
					return "Outwork";

				case PayMe:
					return "Pay Me";

				case Packed:
					return "Packed";

				case Dispatched:
					return "Dispatched";

				case Complete:
					return "Completed";

				default:
					return Status.ToString();
			}
		}

		public string StatusS => Render(true);

		public string StatusC => Render(false);

		public string JobInfo
		{
			get {
				string jobinfo = Name.Length > 16 ? Name.Substring(0, 13) + "..." : Name;
				return $"{jobinfo} x {Quantity / 1000.0:0.##}K";
			}
		}

		public string DisplayQuantity
		{
			get {
				int num = 1;
				if (Template.Is(Notepads))
				{
					num = Pages;
				}

				return (num * Quantity).ToString("N");
			}
		}

		public virtual BrochureDistPackInfo BrochureDistPackInfo { get; set; } = new BrochureDistPackInfo();

		public virtual WiroMagazineInfo WiroInfo { get; set; } = new WiroMagazineInfo();

		public int CelloSides()
		{
			var numOfCelloSide = 0;
			if (FinalFrontCelloglaze != JobCelloglazeOptions.None) numOfCelloSide++;
			if (FinalBackCelloglaze != JobCelloglazeOptions.None) numOfCelloSide++;
			return numOfCelloSide;
		}

		public JobProgress GetProgress()
		{
			var result = new JobProgress();
			if (Template == null || Stock == null || Id == 0)
			{
				return result;
			}

			try
			{
				IEnumerable<JobStatusOptions> routesAll =
					StandardRouting.Instance.GetAllRoutesForJob(this).Where(s => s <= Dispatched); //.SkipWhile(s => s <= JobStatusOptions.Submitted);
				var countOfAllSteps = routesAll.Count();
				var countOfStepsDone = routesAll.Count(x => x <= Status);
				var progress = (int)(countOfStepsDone / (float)countOfAllSteps * 100);

				result.routesAllList = routesAll;
				result.progress = progress;
			}
			catch (Exception ex)
			{
				var m = ex.Message;
			}

			return result;
		}
		public bool IsWiroMagazine() => Template?.Is(WiroMagazines) ?? false;

		public bool IsMagazine() => Template?.Is(Magazine, MagazineNDD, MagazineSeparate, A4CalendarSelfCover, A4CalendarSeparateCover) ?? false;
		public bool IsMagazineSeparate() => Template?.Is(MagazineSeparate, A4CalendarSeparateCover) ?? false;

		public bool IsBrochure() => Template?.Is(Brochure, BrochureNDD, BrochureSDD, BrochureSpecial, TentCalendars, DLCalendars, GreetingCards) ?? false;

		public bool IsPresentationFolder() => Template?.Is(PresentationFolder, PresentationFolderNDD) ?? false;

		public bool IsDigital() => PrintType == PrintType.D;

		public bool IsDigitalAndRunGanged() {
			if (PrintType != PrintType.D)
				return false;

			if (IsSDD())
				return false;

			if (Runs.Any())
				return true;

			int[] _gsmsToConsider = new[] { 310, 360, 420 };

			if (PrintType == PrintType.D && _gsmsToConsider.Contains(FinalStock.GSM))
				return true;
			else
				return false;
		}

		public bool IsWideFormat() => PrintType == PrintType.W;//|| Template.Is(DuplicateNCRBooks, TriplicateNCRBooks, QuadruplicateNCRBooks);

		public bool IsNCRBook() => Template.Is(DuplicateNCRBooks, TriplicateNCRBooks, QuadruplicateNCRBooks);

		public bool IsEnvelope() => Template.Is(EnvelopeBlack, Envelope1Pms, Envelope2Pms, EnvelopeCmyk);

		public bool IsSDD() => Template?.Is(BusinessCardSdd, LetterheadSDD, ComplimentsSDD, StationerySDD, BrochureSDD) ?? false;

		public bool IsNDD() => Template?.Is(BrochureNDD, LetterheadNDD, ComplimentsNDD, PresentationFolderNDD, MagazineNDD, BusinessCardNdd) ?? false;

		/// <summary>
		/// Determines if a job is an outwork job based on its template
		/// </summary>
		public bool IsOutworkJob() => Template?.Is(DuplicateNCRBooks, TriplicateNCRBooks, QuadruplicateNCRBooks,
		                                          EnvelopeBlack, Envelope1Pms, Envelope2Pms, EnvelopeCmyk) ?? false;

		public string GetActualPackaging()
		{
			var order = Order;
			return string.Format("{0} {1}\n{2}\n{3} {4}\n{5}\n{6}\n{7}\n{8}", order.RecipientName,
					 order.RecipientPhone, order.DeliveryAddress.Address1, order.DeliveryAddress.Address2,
					 order.DeliveryAddress.Address3, order.DeliveryAddress.City, order.DeliveryAddress.Postcode,
					 order.DeliveryAddress.State, order.DeliveryAddress.Country);
		}

		public int NumberOfSlots
		{
			get { return DisplayJobSize(); }
			set { }
		}

		// teporary will get removed after
		public virtual bool AACNotPerformedOld { get; set; }

		public virtual bool? AACNotPerformed {
			get {
				bool v = Artworks.Any(_ => !(_.AACPerformed ?? false));

				if(Artworks.Any() && Artworks.All(_ => _.AACPerformed != null)){
					if (Artworks.Any(_ => !(bool)_.AACPerformed))
						return true;
					else
						return false;
				}
				else
				{
					return AACNotPerformedOld;
				}
			}
		}

		public virtual JobSplits Splits { get; set; }

		public bool HasSplitDelivery => Splits?.Any() ?? false;

		public decimal GetSpineWidth()
		{
			decimal spineWidth = 0.0m;
			decimal stockType = 0.0m;
			try
			{
				var s = FinalStock;
				if (IsMagazine() && (BindingOption?.Id ?? 0).Is(3, 4, 5))
				{
					if (s.Name.Contains("Bond") || s.Name.Contains("Uncoated"))
						stockType = 1.1m;
					else if (s.Name.Contains("Gloss"))
						stockType = 0.8m;
					if (s.Name.Contains("Matt"))
						stockType = 0.9m;
					if (stockType == 0.0m) return 0.0m;

					var gsm = s?.GSM ?? 0;
					if (gsm == 0.0m) return 0.0m;


					if (Pages == 0) return 0.0m;

					decimal result = ((gsm * stockType / 1000.0m) * (Pages / 2.0m) * 1.1m) + 0.5m;

					spineWidth = Math.Round(result, 1);
				}
			}
			catch (Exception ex)
			{
				var m = ex.Message;
			}
			return spineWidth;
		}

		public string GetDigitalJobMailHouseInstuctions()
		{
			var t = "";
			try
			{
				if (!IsDigital() || !IsBrochure())
				{
					return t;
				}
				if (BrochureDistPackInfo == null)
				{
					return t;
				}

				t = BrochureDistPackInfo.Summerize();
				return t;
			}
			catch (Exception ex)
			{
				Log.Error(ex, ex.Message);
			}
			return t;
		}



		public string TopLevelPackages()
		{
			try
			{
				var packages = this.Order.PackDetail.GetPackages(this.Facility) as ListOfPackages;
				var result = packages.ToString3();
				return result;
			}
			catch (Exception ex)
			{
				return "";
			}
		}
	}

	public class JobProgress
	{
		public IEnumerable<JobStatusOptions> routesAllList { get; set; }
		public int progress { get; set; }

		public JobProgress()
		{
			routesAllList = new List<JobStatusOptions>();
			progress = 0;
		}
	}

}
