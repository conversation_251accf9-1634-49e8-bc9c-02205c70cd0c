﻿<Project Sdk="Microsoft.NET.Sdk">
  <!-- -->
  <Target Name="DisableAnalyzers" BeforeTargets="CoreCompile" Condition="'$(UseRoslynAnalyzers)' == 'false'">
    <ItemGroup>
      <Analyzer Remove="@(Analyzer)" />
    </ItemGroup>
  </Target>

  <PropertyGroup>
    <ApplicationRevision>0</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <SatelliteResourceLanguages>en</SatelliteResourceLanguages>
    <AssemblyName>lep</AssemblyName>
    <OutputType>Library</OutputType>
    <PackageId>lep</PackageId>
    <RootNamespace>lep</RootNamespace>
    <IsPublishable>False</IsPublishable>
    <NoWarn>$(NoWarn);IDE0001;IDE0002;IDE0049;NU1701;NU1702;CA1416;1608;1069;CS1608;CS1069;CS0659</NoWarn>
    <TargetFramework>net8.0-windows7.0</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Properties\**" />
    <EmbeddedResource Remove="Properties\**" />
    <None Remove="Properties\**" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="FastReport">
      <HintPath>..\..\Libs\FastReport.dll</HintPath>
    </Reference>
    <Reference Include="FastReport.Data.Json">
      <HintPath>..\..\Libs\FastReport.Data.Json.dll</HintPath>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <Compile Update="Service References\SmartFreight\Reference.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Reference.svcmap</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <Compile Remove="src\courier\CourierApplication.cs" />
    <Compile Remove="src\courier\csv\FastwayPostcodeReader.cs" />
    <Compile Remove="src\courier\csv\StarTrackPostcodeReader.cs" />
    <Compile Remove="src\courier\csv\StarTrackRateReader.cs" />
    <Compile Remove="src\courier\FastwayLabelType.cs" />
    <Compile Remove="src\courier\impl\AustraliaPOSTApplication.cs" />
    <Compile Remove="src\courier\impl\FastwayApplication.cs" />
    <Compile Remove="src\courier\impl\FastwayPostcode.cs" />
    <Compile Remove="src\courier\impl\StarTrackApplication.cs" />
    <Compile Remove="src\courier\impl\StarTrackPostcode.cs" />
    <Compile Remove="src\courier\impl\StarTrackRate.cs" />
    <Compile Remove="src\courier\impl\TNTApplication.cs" />
    <Compile Remove="src\IStatusChangeEvent.cs" />
    <Compile Remove="src\jobmonitor\Coloured.cs" />
    <Compile Remove="src\jobmonitor\HealthChecker.cs" />
    <Compile Remove="src\NHibernateSQLAppender.cs" />
    <Compile Remove="src\quote\impl\Quote.cs" />
    <Compile Remove="src\quote\impl\QuoteApplication.cs" />
    <Compile Remove="src\quote\impl\QuoteStatusOptionsEnum.cs" />
    <Compile Remove="src\quote\IQuote.cs" />
    <Compile Remove="src\quote\IQuoteApplication.cs" />
    <Compile Remove="src\quote\QuoteStatusOptions.cs" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="src\user\impl\user.hbm.xml" />
    <EmbeddedResource Include="src\configuration\impl\configuration.hbm.xml" />
    <EmbeddedResource Include="src\job\impl\job.hbm.xml" />
    <EmbeddedResource Include="src\run\impl\run.hbm.xml" />
    <EmbeddedResource Include="src\order\impl\order.hbm.xml" />
    <EmbeddedResource Include="src\report\impl\report.hbm.xml" />
    <EmbeddedResource Include="src\pricing\impl\pricepoint.hbm.xml" />
    <EmbeddedResource Include="src\content\impl\content.hbm.xml" />
    <EmbeddedResource Include="src\email\impl\email.hbm.xml" />
    <EmbeddedResource Include="src\onlineTxn\impl\OnlineTxn.hbm.xml" />
    <EmbeddedResource Include="src\promotion\impl\Promotion.hbm.xml" />
    <EmbeddedResource Include="src\freight\impl\freight.hbm.xml" />
    <EmbeddedResource Include="src\courier\impl\courier.hbm.xml" />
    <EmbeddedResource Include="src\backupdelete\impl\Usage.hbm.xml" />
    <EmbeddedResource Include="src\onlineIVRTxn\impl\OnlineIVRTxn.hbm.xml" />
    <EmbeddedResource Include="src\jobmonitor\impl\Schedule.hbm.xml" />
    <EmbeddedResource Include="src\audit\audit.hbm.xml" />
  </ItemGroup>

  <ItemGroup>
    <WCFMetadata Include="Service References\" />
  </ItemGroup>
  <ItemGroup>
    <WCFMetadataStorage Include="Service References\SmartFreight\" />
  </ItemGroup>

  <ItemGroup>
    <Folder Remove="src\courier" />
    <Folder Remove="src\barcode\www\" />
    <Folder Remove="src\courier\csv\" />
    <Folder Remove="src\cron\www\" />
    <Folder Remove="src\job\www\" />
    <Folder Remove="src\order\www\" />
  </ItemGroup>

  <ItemGroup>
    <None Update="Service References\SmartFreight\Reference.svcmap">
      <Generator>WCF Proxy Generator</Generator>
      <LastGenOutput>Reference.cs</LastGenOutput>
    </None>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\lumen\lumen.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="CsvHelper" Version="32.0.3" />
    <PackageReference Include="FastReport.Compat" Version="2023.1.0" />
    <PackageReference Include="FastReport.OpenSource" Version="2023.1.1" />
    <PackageReference Include="FastReport.OpenSource.Data.Json" Version="2021.4.0" />
    <PackageReference Include="FastReport.OpenSource.Export.PdfSimple" Version="2023.1.1" />
    <PackageReference Include="System.Data.SqlClient" Version="4.8.5" />
    <PackageReference Include="Ghostscript.NET" Version="1.2.3" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.1" />
    <PackageReference Include="NHibernate" Version="5.5.0" />
    <PackageReference Include="NHibernate.Caches.CoreMemoryCache" Version="5.8.0" />
    <PackageReference Include="Serilog" Version="2.12.0" />
    <PackageReference Include="System.ComponentModel.Annotations" Version="5.0.0" />
    <PackageReference Include="System.ComponentModel.Composition" Version="6.0.0" />
    <PackageReference Include="System.Drawing.Common" Version="6.0.0" />
    <PackageReference Include="System.Configuration.ConfigurationManager" Version="6.0.1" />
    <PackageReference Include="System.ServiceModel.Primitives" Version="4.10.0" />
    <PackageReference Include="Microsoft.AspNetCore.Http" Version="2.2.2" />
  </ItemGroup>
</Project>