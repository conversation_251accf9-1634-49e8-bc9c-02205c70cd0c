-- Convert timing enum columns from integers to strings
-- This makes the database more readable and self-documenting

USE PRD_AU
GO

-- First, drop existing check constraints
IF EXISTS (SELECT * FROM sys.check_constraints WHERE name = 'CK_JobStepActiveSession_CurrentState')
    ALTER TABLE JobStepActiveSession DROP CONSTRAINT CK_JobStepActiveSession_CurrentState
GO

IF EXISTS (SELECT * FROM sys.check_constraints WHERE name = 'CK_JobStepActiveSession_State')
    ALTER TABLE JobStepActiveSession DROP CONSTRAINT CK_JobStepActiveSession_State
GO

IF EXISTS (SELECT * FROM sys.check_constraints WHERE name = 'CK_JobStepTimingEvent_EventType')
    ALTER TABLE JobStepTimingEvent DROP CONSTRAINT CK_JobStepTimingEvent_EventType
GO

-- Update CurrentState values from integers to strings
UPDATE JobStepActiveSession 
SET CurrentState = CASE CurrentState
    WHEN 0 THEN 'READY'
    WHEN 1 THEN 'PLAYING' 
    WHEN 2 THEN 'PAUSED'
    WHEN 3 THEN 'FINISHED'
    ELSE 'READY'
END
WHERE CurrentState IN (0, 1, 2, 3)
GO

-- Update EventType values from integers to strings  
UPDATE JobStepTimingEvent
SET EventType = CASE EventType
    WHEN 1 THEN 'PLAY'
    WHEN 2 THEN 'PAUSE' 
    WHEN 3 THEN 'RESUME'
    WHEN 4 THEN 'FINISH'
    ELSE 'PLAY'
END
WHERE EventType IN (1, 2, 3, 4)
GO

-- Change column types to varchar
ALTER TABLE JobStepActiveSession
ALTER COLUMN CurrentState VARCHAR(20) NOT NULL
GO

ALTER TABLE JobStepTimingEvent  
ALTER COLUMN EventType VARCHAR(20) NOT NULL
GO

-- Add new check constraints for string values
ALTER TABLE JobStepActiveSession 
ADD CONSTRAINT CK_JobStepActiveSession_CurrentState 
CHECK (CurrentState IN ('READY', 'PLAYING', 'PAUSED', 'FINISHED'))
GO

ALTER TABLE JobStepTimingEvent
ADD CONSTRAINT CK_JobStepTimingEvent_EventType  
CHECK (EventType IN ('PLAY', 'PAUSE', 'RESUME', 'FINISH'))
GO

PRINT 'Successfully converted timing enum columns to strings'
PRINT 'CurrentState values: READY, PLAYING, PAUSED, FINISHED'
PRINT 'EventType values: PLAY, PAUSE, RESUME, FINISH'
