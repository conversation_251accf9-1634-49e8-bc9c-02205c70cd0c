### eslint-disable ###
### jshint ignore: start ###

appCore = angular.module('app.core')

# Readonly Job Details Directive for Job Board Popup
appCore.directive 'lepJobDetailsReadonly', () ->
    restrict: 'EA'
    scope:
        job: '='
        showTitle: '=?'  # Optional: show/hide the title
        compact: '=?'    # Optional: compact view
    templateUrl: 'common/directives/job-details-readonly.component.html'
    controller: 'JobDetailsReadonlyController'

appCore.controller 'JobDetailsReadonlyController', [
    '$scope', '$log', 'enums',
    ($scope, $log, enums) ->
        $scope._ = 'JobDetailsReadonlyController'
        
        # Initialize defaults
        $scope.showTitle = $scope.showTitle ? true
        $scope.compact = $scope.compact ? false
        
        # Helper function to check if job has special options
        $scope.hasSpecialOptions = () ->
            job = $scope.job
            return false unless job
            
            return !!(
                job.RoundOption ||
                job.HoleDrilling ||
                job.Perforating ||
                job.Scoring ||
                job.CustomDieCut ||
                job.NumberOfMagnets ||
                job.Envelope
            )
        
        # Helper function to format size display
        $scope.formatSize = (size) ->
            return '' unless size?.PaperSize?.Name
            
            if size.PaperSize.Name == 'Custom'
                return "#{size.Width}mm × #{size.Height}mm"
            else
                return "#{size.PaperSize.Name} (#{size.Width}mm × #{size.Height}mm)"
        
        # Helper function to get print type display
        $scope.getPrintTypeDisplay = (printType) ->
            switch printType
                when 'O' then 'Offset'
                when 'D' then 'Digital'
                when 'W' then 'Wide Format'
                when 'N' then 'N/A'
                else printType
        
        # Helper function to get cello display
        $scope.getCelloDisplay = (frontCello, backCello) ->
            return '' unless frontCello || backCello
            
            frontDesc = enums?.ValueDesc?.JobCelloglazeOptions?[frontCello] || 'None'
            backDesc = enums?.ValueDesc?.JobCelloglazeOptions?[backCello] || 'None'
            
            if frontDesc == backDesc
                return frontDesc
            else
                return "#{frontDesc} / #{backDesc}"
        
        # Helper function to check if job has artwork
        $scope.hasArtwork = () ->
            job = $scope.job
            return job?.Artworks?.length > 0
        
        # Helper function to get artwork status display
        $scope.getArtworkStatusDisplay = (status) ->
            switch status
                when 0 then 'Not Supplied'
                when 1 then 'Supplied'
                when 2 then 'Ready'
                when 3 then 'Needs Attention'
                else 'Unknown'
        
        # Helper function to get status CSS class
        $scope.getStatusClass = (status) ->
            switch status?.toLowerCase()
                when 'ready' then 'success'
                when 'printing', 'inprogress' then 'info'
                when 'finished', 'packed' then 'success'
                when 'onhold' then 'warning'
                when 'cancelled' then 'danger'
                else 'default'
        
        # Watch for job changes
        $scope.$watch 'job', (newJob, oldJob) ->
            if newJob
                $log.debug 'JobDetailsReadonly: Job loaded', newJob.Id, newJob.Name
                
                # Ensure we have required data
                if !newJob.Template
                    newJob.Template = { Name: 'Unknown Template' }
                
                if !newJob.FinishedSize
                    newJob.FinishedSize = { PaperSize: { Name: 'Unknown' } }
        
        # Initialize when controller loads
        $scope.$on '$viewContentLoaded', () ->
            $log.debug 'JobDetailsReadonly: View loaded'
]

### jshint ignore: end ###
