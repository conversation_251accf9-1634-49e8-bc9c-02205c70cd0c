-- Job Step Performance Analysis
-- Average time spent at each job status/step

WITH StepTiming AS (
    SELECT
        e.JobId,
        e.Status,
        e.EventType,
        e.EventTime,
        LAG(e.EventTime) OVER (PARTITION BY e.JobId, e.Status ORDER BY e.EventTime) AS PrevEventTime,
        CASE
            WHEN e.EventType = 4 AND -- FINISH (4)
                 LAG(e.EventType) OVER (PARTITION BY e.JobId, e.Status ORDER BY e.EventTime) = 1 -- PLAY (1)
            THEN DATEDIFF(SECOND,
                LAG(e.EventTime) OVER (PARTITION BY e.JobId, e.Status ORDER BY e.EventTime),
                e.EventTime)
        END AS DurationSeconds
    FROM JobStepTimingEvent e
)
SELECT
    st.Status,
    COUNT(*) AS CompletedJobs,
    AVG(CAST(st.DurationSeconds AS FLOAT)) AS AvgDurationSeconds,
    FORMAT(DATEADD(SECOND, AVG(st.DurationSeconds), 0), 'HH:mm:ss') AS AvgFormattedDuration,
    MIN(st.DurationSeconds) AS MinDurationSeconds,
    MAX(st.DurationSeconds) AS MaxDurationSeconds,
    FORMAT(DATEADD(SECOND, MIN(st.DurationSeconds), 0), 'HH:mm:ss') AS MinFormattedDuration,
    FORMAT(DATEADD(SECOND, MAX(st.DurationSeconds), 0), 'HH:mm:ss') AS MaxFormattedDuration
FROM StepTiming st
WHERE st.DurationSeconds IS NOT NULL
GROUP BY st.Status
ORDER BY AvgDurationSeconds DESC;
