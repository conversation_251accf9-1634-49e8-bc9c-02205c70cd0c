﻿using lep.configuration;
using lep.job;
using RoundedRectangles;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Printing;
using System.IO;
using System.Linq;
using System.Text;
using System.Drawing.Drawing2D;
using System.Drawing.Text;

namespace lep.despatch.impl.label
{
	public class BaseFurtherProcessingLabel : PrintDocument, IDespatchLabel, IDisposable
	{
		protected List<IDisposable> disposables = new List<IDisposable>();
		protected int hTitle = 50;
		protected int pageNumber = 0;
		protected const int defaultFontSize = 13;
		protected const int titlefontsize = 14;
		protected const int specialInstructionsFontSize = 10;
		protected Font defaultFont = new Font(FontConsolas, defaultFontSize, FontStyle.Bold, GraphicsUnit.Pixel);
		protected Font titleFont = new Font(FontConsolas, titlefontsize, FontStyle.Bold);
		protected Font barCodeFont = new Font(Font3of9, 36, GraphicsUnit.Point);
		protected Font specialInstructionsFont = new Font(FontConsolas, specialInstructionsFontSize, FontStyle.Bold, GraphicsUnit.Pixel);
		protected Font smallFont = new Font(FontConsolas, 6, FontStyle.Regular);
		protected Font quantityBoldFont = new Font(FontConsolas, 13, FontStyle.Bold);
		protected string fmt = "{0,-8}: {1}";

		protected StringFormat titleAlignment = new StringFormat()
		{
			LineAlignment = StringAlignment.Near,
			Alignment = StringAlignment.Center,
			Trimming = StringTrimming.None
		};

		protected StringFormat middleFormat = new StringFormat
		{
			Trimming = StringTrimming.EllipsisCharacter,
			FormatFlags = StringFormatFlags.NoWrap | StringFormatFlags.DisplayFormatControl,
			Alignment = StringAlignment.Near
		};

		protected StringFormat rightFormat = new StringFormat
		{
			Trimming = StringTrimming.EllipsisCharacter,
			FormatFlags = StringFormatFlags.NoWrap | StringFormatFlags.DisplayFormatControl,
			Alignment = StringAlignment.Near
		};

		protected StringFormat frontTopFormat = new StringFormat()
		{
			LineAlignment = StringAlignment.Near,
			Alignment = StringAlignment.Center,
			Trimming = StringTrimming.None
		};

		protected StringFormat frontBottomFormat = new StringFormat()
		{
			LineAlignment = StringAlignment.Far,
			Alignment = StringAlignment.Center,
			Trimming = StringTrimming.None
		};

		protected StringFormat InstructionsFormat = new StringFormat
		{
			Trimming = StringTrimming.None,
			FormatFlags = StringFormatFlags.FitBlackBox | StringFormatFlags.DisplayFormatControl,
			Alignment = StringAlignment.Near
		};

		protected StringFormat quantityAlignment = new StringFormat()
		{
			LineAlignment = StringAlignment.Center,
			Alignment = StringAlignment.Center,
			Trimming = StringTrimming.None
		};

		protected const string Font3of9 = "Free 3 of 9";
		protected const string FontConsolas = "Verdana";
		protected const int PaperHeight = 600;
		protected const int PaperWidth = 400;

		protected int width = PaperWidth - MarginWidth * 2;
		protected int left = MarginWidth;
		protected int top = MarginHeight;

		protected const int MarginWidth = 25;
		protected const int NestedMarginWidth = 50;
		protected const int MarginHeight = 20;

		protected StringBuilder basicInformation = new StringBuilder();
		protected StringBuilder processingText1 = new StringBuilder();
		protected string strInstructions = "";
		protected StringBuilder processingText = new StringBuilder();
		protected StringBuilder processingText2 = new StringBuilder();

		protected Rectangle rBasicInfoTxt = new Rectangle(NestedMarginWidth, 285, 240, 100);
		protected Rectangle rThumb = new Rectangle(NestedMarginWidth, 380, 180, 100);
		protected Rectangle rRoundedCorner = new Rectangle(260, 190, 76, 76);
		protected Rectangle rTitle = new Rectangle(MarginWidth, MarginHeight, PaperWidth - MarginWidth * 2, 50);
		protected IJob job;
		protected Pen penBlack3 = new Pen(Color.Black, 1);
		protected Pen penRed3 = new Pen(Color.Red, 1);

		public virtual IJob Job
		{
			get { return job; }

			set {
				job = value;
				FormatPrintContent();
			}
		}

		public string PrinterAndTray { get; set; }
		public string PrintFileName { get; set; }
		public IConfigurationApplication ConfigurationApplication { get; set; }

		public BaseFurtherProcessingLabel()
		{
			disposables.Add(defaultFont);
			disposables.Add(titleFont);
			disposables.Add(barCodeFont);
			disposables.Add(specialInstructionsFont);
			disposables.Add(smallFont);
			disposables.Add(quantityBoldFont);

			disposables.Add(penBlack3);
			disposables.Add(penRed3);
		}

		void IDespatchLabel.Print()
		{
			Print();
		}

		void IDisposable.Dispose()
		{
			base.Dispose();
			foreach (var d in disposables)
			{
				d.Dispose();
			}
		}

		public void SetupPrintProperties()
		{
			var ps = new PrinterSettings();
			PrintUtils.ConfigurePrinterSettings(PrinterAndTray, ref ps);

			PrinterSettings = ps;
			DefaultPageSettings.PaperSize = new PaperSize("210 x 297 mm", 400, 600);

			ps.PrintToFile = ps.PrinterName == "Microsoft XPS Document Writer";
			ps.DefaultPageSettings.Margins = new Margins(MarginWidth, MarginWidth, MarginHeight, MarginHeight);

			if (ps.PrintToFile)
			{
				ps.PrintFileName = PrintFileName;
			}

			PrinterSettings = ps;
		}

		public void FormatPrintContent()
		{
		}

		protected override void OnPrintPage(PrintPageEventArgs e)
		{
			base.OnPrintPage(e);
		}

		protected void DrawBarcode(Graphics g)
		{
			g.TranslateTransform(314, 500);
			g.RotateTransform(-90.0f);
			g.DrawString("*" + Job.Barcode + "*", barCodeFont, Brushes.Black, new Point(0, 0));
			g.ResetTransform();
		}

		protected void DrawThumbnail(Graphics g)
		{
			g.FillRectangle(new SolidBrush(Color.FromArgb(64, Color.Gray)), rThumb);

			var thumbs2 = LepGlobal.Instance.GetThumbs(Job).Where(fn => !fn.Name.Contains("_bk"));
			if (thumbs2.Any())
			{
				using (Stream fs = thumbs2.First().OpenRead())
				{
					using (var image = Image.FromStream(fs, true))
					{
						g.DrawImage(image, rThumb);
						fs.Close();
					}
				}
			}
		}

		protected void DrawEDD(Graphics g)
		{
			if (Job.Order.DispatchEst != null)
			{
				var edd = "EDD: " + Job.Order.DispatchEst.Value.ToString("dd/MMM/yy");
				var sz = g.MeasureString(edd, defaultFont);
				g.DrawRectangle(penBlack3, 218, 158, sz.Width + 2, sz.Height + 2);
				g.DrawString(edd, defaultFont, Brushes.Black, 220, 160);
			}
		}

		protected void DrawRoundedCorndersBox(Graphics g)
		{
			// draw sticker
			var rRoundedCorner = new Rectangle(270, 190, 76, 76);

			g.DrawString(job.Quantity.ToString(), quantityBoldFont, Brushes.Black, rRoundedCorner, quantityAlignment);
			g.DrawString("Front top", smallFont, Brushes.Black, rRoundedCorner, frontTopFormat);
			g.DrawString("Front bottom", smallFont, Brushes.Black, rRoundedCorner, frontBottomFormat);
			var radious = 12;
			// figure corners
			var rc = RectangleCorners.None;
			if (Job.TLround)
				rc = rc | RectangleCorners.TopLeft;
			if (Job.TRround)
				rc = rc | RectangleCorners.TopRight;
			if (Job.BLround)
				rc = rc | RectangleCorners.BottomLeft;
			if (Job.BRround)
				rc = rc | RectangleCorners.BottomRight;

			GraphicsPath path = null;
			if (Job.RoundOption == RoundOption.None)
			{
				g.DrawRectangle(penBlack3, rThumb);
				g.DrawRectangle(penBlack3, rRoundedCorner);
			}
			else
			{
				path = RoundedRectangle.Create(rThumb, 1, rc);
				g.DrawPath(Pens.Black, path);
				var path2 = RoundedRectangle.Create(rRoundedCorner, radious, rc);
				disposables.Add(path2);
				g.DrawPath(Pens.Black, path2);
			}

			if (path != null)
				g.SetClip(path);
			if (path != null)
			{
				g.ResetClip();
				disposables.Add(path);
			}
		}

		protected bool specialInstructionsOnNextPage = false;

		protected void DrawSpecialInstructions(PrintPageEventArgs e, Graphics g)
		{
			SizeF measure = g.MeasureString(strInstructions, specialInstructionsFont);
			var hInstructions = (int)measure.Height;
			if (pageNumber == 1)
			{
				var rInstructions = new Rectangle(left, 510, width, 86);
				if (rInstructions.Height > measure.Height)
				{
					specialInstructionsOnNextPage = false;
					g.DrawString(strInstructions, specialInstructionsFont, Brushes.Black, rInstructions, InstructionsFormat);
				}
				else
				{
					e.HasMorePages = true;
					specialInstructionsOnNextPage = true;
					g.DrawString("Instructions on next page...", specialInstructionsFont, Brushes.Red, rInstructions, InstructionsFormat);
				}
			}
			else if (pageNumber == 2 && specialInstructionsOnNextPage)
			{
				strInstructions = $"Job #: {Job.Id}\n\n" + strInstructions;
				var rInstructions = new Rectangle(left, top, width + MarginWidth, 800);
				g.DrawString(strInstructions, specialInstructionsFont, Brushes.Black, rInstructions, InstructionsFormat);
				pageNumber = 0;
				specialInstructionsOnNextPage = false;
			}
		}
	}
}
