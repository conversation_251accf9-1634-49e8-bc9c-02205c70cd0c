using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc.Filters;
using Serilog;
using lep.timing;
using lep.timing.dto;
using lep.security;
using lep.user;

namespace LepCore.BackEnd.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class ProductionInstructionsController : Controller
    {
        private readonly IProductionInstructionsService _instructionsService;
        private readonly IUserApplication _userApplication;
        private IUser _currentUser;

        public ProductionInstructionsController(IProductionInstructionsService instructionsService, IUserApplication userApplication)
        {
            _instructionsService = instructionsService;
            _userApplication = userApplication;
        }

        public override void OnActionExecuting(ActionExecutingContext context)
        {
            var userId = User.Claims.Where(c => c.Type == "UserId").Select(c => Convert.ToInt32(c.Value)).FirstOrDefault();
            _currentUser = _userApplication.GetUser(userId);
            base.OnActionExecuting(context);
        }

        [HttpGet("{status}")]
        public async Task<IActionResult> GetInstructions(string status, [FromQuery] int? jobId = null)
        {
            try
            {
                ProductionInstructionsDto instructions;

                if (jobId.HasValue)
                {
                    instructions = await _instructionsService.GetInstructionsForJobStep(jobId.Value, status);
                }
                else
                {
                    instructions = await _instructionsService.GetInstructions(status);
                }

                if (instructions == null)
                {
                    return NotFound(new { message = $"No instructions found for status '{status}'" });
                }

                return Ok(instructions);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error getting instructions for status {Status}", status);
                return StatusCode(500, new { success = false, message = "Internal server error" });
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetAllInstructions()
        {
            try
            {
                var instructions = await _instructionsService.GetAllInstructions();
                return Ok(instructions);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error getting all instructions");
                return StatusCode(500, new { success = false, message = "Internal server error" });
            }
        }

        [HttpPost]
        public async Task<IActionResult> CreateInstructions([FromBody] ProductionInstructionsDto dto)
        {
            try
            {
                var currentUser = _currentUser;
                if (currentUser == null)
                {
                    return Unauthorized("User not authenticated");
                }

                if (string.IsNullOrWhiteSpace(dto.Status))
                {
                    return BadRequest(new { message = "Status is required" });
                }

                if (string.IsNullOrWhiteSpace(dto.Title))
                {
                    return BadRequest(new { message = "Title is required" });
                }

                if (string.IsNullOrWhiteSpace(dto.Instructions))
                {
                    return BadRequest(new { message = "Instructions content is required" });
                }

                var savedInstructions = await _instructionsService.CreateInstructions(dto, currentUser.Id);

                Log.Information("Created production instructions for status {Status} by user {UserId}",
                    dto.Status, currentUser.Id);

                return CreatedAtAction(nameof(GetInstructions),
                    new { status = savedInstructions.Status },
                    savedInstructions);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error creating production instructions");
                return StatusCode(500, new { success = false, message = "Internal server error" });
            }
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateInstructions(int id, [FromBody] ProductionInstructionsDto dto)
        {
            try
            {
                var currentUser = _currentUser;
                if (currentUser == null)
                {
                    return Unauthorized("User not authenticated");
                }

                if (id != dto.Id)
                {
                    return BadRequest(new { message = "ID mismatch" });
                }

                if (string.IsNullOrWhiteSpace(dto.Status))
                {
                    return BadRequest(new { message = "Status is required" });
                }

                if (string.IsNullOrWhiteSpace(dto.Title))
                {
                    return BadRequest(new { message = "Title is required" });
                }

                if (string.IsNullOrWhiteSpace(dto.Instructions))
                {
                    return BadRequest(new { message = "Instructions content is required" });
                }

                var updatedInstructions = await _instructionsService.UpdateInstructions(dto, currentUser.Id);

                Log.Information("Updated production instructions {Id} for status {Status} by user {UserId}",
                    id, dto.Status, currentUser.Id);

                return Ok(updatedInstructions);
            }
            catch (ArgumentException ex)
            {
                Log.Warning(ex, "Instructions not found for update: {Id}", id);
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error updating production instructions {Id}", id);
                return StatusCode(500, new { success = false, message = "Internal server error" });
            }
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteInstructions(int id)
        {
            try
            {
                var currentUser = _currentUser;
                if (currentUser == null)
                {
                    return Unauthorized("User not authenticated");
                }

                await _instructionsService.DeleteInstructions(id, currentUser.Id);

                Log.Information("Deleted production instructions {Id} by user {UserId}",
                    id, currentUser.Id);

                return NoContent();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error deleting production instructions {Id}", id);
                return StatusCode(500, new { success = false, message = "Internal server error" });
            }
        }

        [HttpGet("status/{status}/exists")]
        public async Task<IActionResult> CheckInstructionsExist(string status, [FromQuery] int? jobTypeId = null)
        {
            try
            {
                var instructions = await _instructionsService.GetInstructions(status);
                return Ok(new { exists = instructions != null });
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error checking if instructions exist for status {Status}", status);
                return StatusCode(500, new { success = false, message = "Internal server error" });
            }
        }

        [HttpGet("preview/{status}")]
        public async Task<IActionResult> PreviewInstructions(string status, [FromQuery] int? jobId = null)
        {
            try
            {
                ProductionInstructionsDto instructions;

                if (jobId.HasValue)
                {
                    instructions = await _instructionsService.GetInstructionsForJobStep(jobId.Value, status);
                }
                else
                {
                    instructions = await _instructionsService.GetInstructions(status);
                }

                if (instructions == null)
                {
                    return Ok(new {
                        hasInstructions = false,
                        message = $"No instructions available for status '{status}'"
                    });
                }

                return Ok(new {
                    hasInstructions = true,
                    title = instructions.Title,
                    hasSafetyNotes = instructions.HasSafetyNotes,
                    hasQualityChecks = instructions.HasQualityChecks,
                    hasRequiredTools = instructions.HasRequiredTools,
                    estimatedDuration = instructions.EstimatedDurationFormatted
                });
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error previewing instructions for status {Status}", status);
                return StatusCode(500, new { success = false, message = "Internal server error" });
            }
        }

        /// <summary>
        /// Get both checkpoint and detailed instructions for a status
        /// </summary>
        [HttpGet("{status}/both")]
        public async Task<IActionResult> GetBothInstructionTypes(string status)
        {
            try
            {
                var currentUser = _currentUser;
                if (currentUser == null)
                {
                    return Unauthorized("User not authenticated");
                }

                var (checkpoints, detailed) = await _instructionsService.GetBothInstructionTypes(status);

                return Ok(new
                {
                    success = true,
                    checkpoints = checkpoints,
                    detailed = detailed,
                    hasCheckpoints = checkpoints != null,
                    hasDetailed = detailed != null
                });
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error getting both instruction types for status: {Status}", status);
                return StatusCode(500, new { success = false, message = "Failed to get instructions" });
            }
        }

        /// <summary>
        /// Clear the instructions cache
        /// </summary>
        [HttpPost("clear-cache")]
        public IActionResult ClearCache()
        {
            try
            {
                var currentUser = _currentUser;
                if (currentUser == null)
                {
                    return Unauthorized("User not authenticated");
                }

                _instructionsService.ClearCache();

                Log.Information("Instructions cache cleared by user {UserId}", currentUser.Id);

                return Ok(new { success = true, message = "Cache cleared successfully" });
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error clearing instructions cache");
                return StatusCode(500, new { success = false, message = "Failed to clear cache" });
            }
        }
    }
}
