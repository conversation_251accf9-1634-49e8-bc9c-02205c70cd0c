-- Quick diagnostic for CustomerID = 15029
-- Copy and paste this into SQL Server Management Studio

-- Check if customer exists and basic info
SELECT 
    'Customer 15029 Basic Info' AS CheckType,
    c.<PERSON>er<PERSON>d,
    c.<PERSON>,
    c.PaymentTerms,
    c.Franchise<PERSON>ode,
    c.MYOBPastDue,
    c.<PERSON>,
    c.<PERSON><PERSON>PriceCode,
    c.MAT3mA,
    -- Calculate what EDR discount they should get
    CASE
        WHEN c.MAT3mA >= 10000 AND c.MAT3mA < 15000 THEN 25
        WHEN c.MAT3mA >= 7500 AND c.MAT3mA < 10000 THEN 20
        WHEN c.MAT3mA >= 5000 AND c.MAT3mA < 7500 THEN 15
        WHEN c.MAT3mA >= 2000 AND c.MAT3mA < 5000 THEN 10
        WHEN c.MAT3mA >= 500 AND c.MAT3mA < 2000 THEN 5
        ELSE NULL
    END AS CalculatedEDRDiscount,
    -- Check each condition
    CASE WHEN c.PaymentTerms IN ('Account', 'OnHold') THEN 'PASS' ELSE 'FAIL: ' + ISNULL(c.PaymentTerms, 'NULL') END AS PaymentTermsCheck,
    CASE WHEN (c.FranchiseCode = '' or c.FranchiseCode is null) THEN 'PASS' ELSE 'FAIL: ' + ISNULL(c.FranchiseCode, 'NULL') END AS FranchiseCheck,
    CASE WHEN c.MYOBPastDue <= 0 THEN 'PASS' ELSE 'FAIL: ' + CAST(c.MYOBPastDue AS VARCHAR(20)) END AS PastDueCheck,
    CASE WHEN (C.ProductPriceCode IN ('P0', NULL, '') AND C.FreightPriceCode IN ('F0', NULL, '')) THEN 'PASS' 
         ELSE 'FAIL: Prod=' + ISNULL(C.ProductPriceCode, 'NULL') + ', Freight=' + ISNULL(C.FreightPriceCode, 'NULL') END AS PriceCodeCheck
FROM Customer c
WHERE c.CustomerId = 15029

UNION ALL

-- Check if there are matching promotions
SELECT 
    'Available Promotions' AS CheckType,
    CAST(p.Id AS VARCHAR(10)) AS CustomerId,
    p.PromotionCode AS Name,
    CAST(p.Discount AS VARCHAR(10)) AS PaymentTerms,
    p.ShortDescription AS FranchiseCode,
    NULL AS MYOBPastDue,
    NULL AS ProductPriceCode,
    NULL AS FreightPriceCode,
    NULL AS MAT3mA,
    NULL AS CalculatedEDRDiscount,
    NULL AS PaymentTermsCheck,
    NULL AS FranchiseCheck,
    NULL AS PastDueCheck,
    NULL AS PriceCodeCheck
FROM Promotion p 
WHERE p.PromotionCode LIKE 'My Reward %'

UNION ALL

-- Check existing offers for this customer this month
SELECT 
    'Existing Offers This Month' AS CheckType,
    CAST(co.CustomerId AS VARCHAR(10)) AS CustomerId,
    p.PromotionCode AS Name,
    CAST(p.Discount AS VARCHAR(10)) AS PaymentTerms,
    CONVERT(VARCHAR(20), co.DateOfferEnds, 120) AS FranchiseCode,
    NULL AS MYOBPastDue,
    NULL AS ProductPriceCode,
    NULL AS FreightPriceCode,
    NULL AS MAT3mA,
    NULL AS CalculatedEDRDiscount,
    NULL AS PaymentTermsCheck,
    NULL AS FranchiseCheck,
    NULL AS PastDueCheck,
    NULL AS PriceCodeCheck
FROM CustomerOffers co
JOIN Promotion p ON p.Id = co.PromotionId
WHERE co.CustomerId = 15029
    AND co.DateOfferEnds = DATEADD(MONTH, DATEDIFF(MONTH, -1, GETDATE()), -1)

ORDER BY CheckType;
