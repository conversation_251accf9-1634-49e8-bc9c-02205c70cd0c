**Job Board Redesign-/ New Version of side by side an new Jobboard /  Scoping Document**

1. Product Routings  
1) As per spreadsheets  
2) Digital & Offset Print Types to follow the same workflow  
3) Digital Print Type Jobs to just go into Runs (auto allocation) similar to the way Offset Jobs are handled once pre-flight done status occurs. i.e. Not into DPC Pre Production, until Pre Press operator chooses this action

2. Job Boards  
1) FG Press Room – two discreet Press Room Job Boards \- separate by “Method”  
   1. “Press 920” – PER, STR  
   2. “Press 924”  \- SS, SW, TURN  
2) Folding  
   1. By Folder  
      1. Stahl – Based on A1 Sheet size  
   2. By Fold  
      1. A4\>DL – Folder 4  
      2. Crash Fold – Crash Folder  
3) Magazine/Booklet – Separate Cover  
   1. Two parts  
      1. Part A – Cover  
      2. Part B \- Text

3. Jobs  
1) Start time  
2) Finish Time  
3) By Operator  
4) By Machine

4. Process:  
1) Note: Every process to have a PC with touch screen  
2) No scanners (except at despatch)  
3) Operator selects Run/Job from Job Board (can’t work on a Job not on that work centres Job Board)   
4) My LEP brings up a Pop Up window (on Touch screen Monitor) with key info. E.g.  
   1. (Note: Info will be different depending on whether it is a “Run” or a “Job” that has been selected.  Selecting an Order number has no effect re this functionality)  
   2. Pre start checklist for that Job Type for that work centre (refer separate sample “Pre Start Checklists”)  
   3. Job details, including thumb nail  
5) Operator selects name from drop down list  
6) Once operator name selected other fields appear/not greyed out e.g. “Start Job”  
7) Operator selects “Start Job” \- this acts to record Job start time and operator name against the job  
8) When Job complete operator signs job bag and hits button on touch screen to say “Job Finished”  
   1. This records Job & Operator finish time  
   2. Updates Job Status  
   3. Triggers My LEP to send out Job Status Email Notification  
9) If Job not finished when its end of shift, operator to click “Job paused” button, which records finish time for that operator  
10) When next operator selects this job to finish, My LEP presents a “Resume Job” button – which starts recording Job & Operator time.