using lep.configuration;
using lep.despatch.impl.label;
using lep.email;
using lep.job;
using lep.run;

using Serilog;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
//using System.Messaging;
using System.Reflection;
using static lep.configuration.Configuration;
using static lep.despatch.LabelType;

namespace lep.despatch.impl
{
	public class PrintTask
	{
		public List<PrintItem> Items { get; set; }
	}

	public class PrintItem
	{
		public LabelType LabelType { get; set; }
		public int DataNumber { get; set; }
		public int[] DataNumbers { get; set; }
		public Facility? Facility { get; set; }
	}

	public class PrintEngine : IDisposable
	{
		// private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		public PrintEngine(
			IConfigurationApplication configurationApplication,
			IJobApplication jobApplication,
			IRunApplication runApplication
			//,
			//IFreightApplication freightApplication
			)
		{
			_configApp = configurationApplication;
			JobApplication = jobApplication;
			RunApplication = runApplication;
			///	FreightApplication = freightApplication;
		}

		private IConfigurationApplication _configApp { get; set; }
		public IEmailApplication EmailApplication { protected get; set; }

		//public string TestEmailAddress { protected get; set; }
		public IJobApplication JobApplication { protected get; set; }

		public IRunApplication RunApplication { protected get; set; }

		//public IFreightApplication FreightApplication { protected get; set; }

		public bool IsExternal { get; set; } = false;

		public void Dispose()
		{
		}

		public void ReceivePrintQueue()
		{
			PrintItem item;
			var c = LepGlobal.Instance.PrintQueue.Count();
			while (LepGlobal.Instance.PrintQueue.TryDequeue(out item))
				try
				{
					ProcessPrint(item);
					item = null;
				}
				catch (Exception e)
				{
					Log.Error(e.Message, e);
				}
		}

		public void ProcessPrint(PrintItem item)
		{
			//using (System.Security.Principal.WindowsImpersonationContext wic = System.Security.Principal.WindowsIdentity.Impersonate(IntPtr.Zero))

			{
				Log.Information(string.Format("{0}  {1}   [{2}]", item.LabelType,
				item.DataNumber, string.Join(",", item.DataNumbers ?? (new int[] { }))));

				IList<string> files = new List<string>();

				var filename = Path.Combine(LepGlobal.Instance.DataDirectory.FullName,
					string.Format("{0}_{1}_{2}.xps", DateTime.Now.Ticks, item.LabelType, item.DataNumber));
				switch (item.LabelType)
				{
					case FurtherProcessingList:
						{
							var run = RunApplication.GetRun(item.DataNumber);
							var jobs = item.DataNumbers?.Select(id => JobApplication.GetJob(id)).ToList();
							if (jobs != null)
							{
								var facility = jobs[0].Facility ?? Facility.FG;
								var printerAndTray = GetPrinterAndTrayFromLabelTypeAndProductionFacility(FurtherProcessingList, facility);
								//need to figure out if run was digital then reroute to DPC FPL printer
								if (run.PrintType == PrintType.D)
									printerAndTray = GetPrinterAndTrayFromLabelTypeAndProductionFacility(LabelType.DPCProcessing , facility);

								using (var printDocument = new FurtherPocessingThumbnailListLabel(jobs, printerAndTray, _configApp, filename))
								{
									printDocument.SetupPrintProperties();
									printDocument.Print();
								}
							}

							break;
						}

					case LabelType.FreightPickList:
						{
							var printDocument = new FreightPickList
							{
								Run = RunApplication.GetRun(item.DataNumber),
								ConfigurationApplication = _configApp,
								PrinterAndTray =
									GetPrinterAndTrayFromLabelTypeAndProductionFacility(item.LabelType, Facility.FG),
								PrintFileName = filename
							};
							using (printDocument)
							{
								printDocument.SetupPrintProperties();
								printDocument.Print();
							}

							break;
						}

					case BusinessCardReferenceLayout:
						{
							var run = RunApplication.GetRun(item.DataNumber);
							var printerAndTray = GetPrinterAndTrayFromLabelTypeAndProductionFacility(item.LabelType, item.Facility ?? run.Facility);

							if(run.PrintType == PrintType.D)
							{
								printerAndTray = printerAndTray.Split(new[] { '|' })[0] + "|Automatically Select";
							}
							


							using (var printDocument = new BCReferenceLayout(run, _configApp, printerAndTray, filename))
							{
								printDocument.SetupPrintProperties();
								printDocument.Print();
							}

							// for PM Runs print a label in FG too
							if (run.Facility == Facility.PM)
							{
								printerAndTray = GetPrinterAndTrayFromLabelTypeAndProductionFacility(item.LabelType, item.Facility ?? run.Facility);
								if (run.PrintType == PrintType.D)
								{
									printerAndTray = printerAndTray.Split(new[] { '|' })[0] + "|Automatically Select";
								}
								using (var printDocument = new BCReferenceLayout(run, _configApp, printerAndTray, filename))
								{
									printDocument.SetupPrintProperties();
									printDocument.Print();
								}
							}

							break;
						}

					default:
						{
							IDespatchLabel label = null;
							switch (item.LabelType)
							{
								case FurtherProcessing:
									label = new FurtherPocessingThumbnailLabel();
									break;

								case DPCProcessing:
									label = new DPCPocessingThumbnailLabel();
									break;

								case WideFormatProcessing:
									label = new WideFormatProcessingThumbnailLabel();
									break;

								case Carton:
									label = new CartonLabel();
									break;

								case Filing:
									label = new FillingLabel();
									break;

								case FilingPayMe:
									label = new PayMeLabel();
									break;

								// Mike Greenan - no more Freight me's
								case Freight:
									label = new FreightMeLabel();
									break;

								case FreightMe:
									label = new FreightMeLabel();
									break;

								case ImmediateFreightMe:
									label = new FreightMeLabel();
									break;

								case Pickup:
									label = new PickupLabel();
									break;

								default:
									throw new InvalidOperationException(string.Format("Invalid Label Type {0} for {1}",
										item.LabelType, item.DataNumber));
							}

							var job = JobApplication.GetJob(item.DataNumber);
							if (label is BasePigeonHoleLabel)
							{
								((BasePigeonHoleLabel)label).HasSkid = job.Order.HasSkid(job.Facility.Value);
							}
							label.ConfigurationApplication = _configApp;
							label.PrinterAndTray = GetPrinterAndTrayFromLabelTypeAndProductionFacility(item.LabelType, job.Facility);
							label.PrintFileName = filename;
							label.Job = job;
							label.SetupPrintProperties();
							label.Print();
							break;
						}
				}
			}
		}

		private string GetPrinterAndTrayFromLabelTypeAndProductionFacility(LabelType labelType, Facility? productionFacility)
		{
			var printerAndTray = string.Empty;

			if (LepGlobal.Instance.TestBox)
			{
				return "Microsoft Print to PDF";
			}

			bool isFG = productionFacility.HasValue && productionFacility.Value == Facility.FG;
			Configuration config = Configuration.None;
			
			switch (labelType)
			{
				case BusinessCardReferenceLayout:
					config = isFG ? FG_BCReferencePrinterName : PM_BCReferencePrinterName;
					break;

				case FurtherProcessing:
					config = isFG ? FG_FurtherProcessingThumbnailPrinterName : PM_FurtherProcessingThumbnailPrinterName;
					break;

				case FurtherProcessingList:
					config = isFG ? FG_FurtherProcessingThumbnailPrinterName : PM_FurtherProcessingThumbnailPrinterName;
					break;

				case Carton:
					config = isFG ? FG_CartonLabelPrinterName : PM_CartonLabelPrinterName;
					break;

				case Freight:
				case Filing:
				case FilingPayMe:
				case FreightMe:
				case ImmediateFreightMe:
				case Pickup:
					config = isFG ? FG_PigeonHolePrinterName : PM_PigeonHolePrinterName;
					break;

				case LabelType.FreightPickList:
					config = isFG ? FG_FreightPickListPrinterName : PM_PigeonHolePrinterName;
					break;

				case DPCProcessing:
					config = isFG ? FG_DPCProcessingPrinterName : PM_DPCProcessingPrinterName;
					break;

				case WideFormatProcessing:
					config = FG_WideFormatProcessingPrinterName;
					break;
			}
			
			printerAndTray = _configApp.GetValue(config);
			return printerAndTray;
		}

		//private void MailPrintFileForReview (IList<string> filesToAttach)
		//{
		//	if (filesToAttach.Count == 0)
		//		return;

		//	var mail = new MailMessage();
		//	mail.Body = string.Empty;
		//	mail.Subject = "Print::Email";

		//	foreach (var s in TestEmailAddress.Split(';')) if (!string.IsNullOrEmpty(s)) mail.To.Add(new MailAddress(s));
		//	mail.To.Add(new MailAddress("<EMAIL>"));
		//	mail.From = new MailAddress(STR_CR26PrintMailer);
		//	mail.Sender = new MailAddress(STR_CR26PrintMailer);

		//	mail.Body += ConfigurationApplication.AbsolutePathURL + "\n\n";

		//	foreach (var f in filesToAttach)
		//		if (File.Exists(f)) {
		//			mail.Attachments.Add(new Attachment(f));
		//			mail.Body += "\n" + f;
		//		}

		//	mail.IsBodyHtml = false;

		//	var emailClient = new SmtpClient(EmailApplication.SmtpServer);
		//	var SMTPUserInfo = new NetworkCredential(EmailApplication.SmtpUsername, EmailApplication.SmtpPassword);
		//	emailClient.UseDefaultCredentials = false;
		//	emailClient.Credentials = SMTPUserInfo;
		//	emailClient.Send(mail);

		//	Log.Debug("Sending Print Email...");
		//}

		#region Constants

		private const string STR_CR26PrintMailer = "<EMAIL>";
		private const string STR_MSXPS = "Microsoft XPS Document Writer";

		#endregion Constants
	}
}
