using Microsoft.AspNetCore.Mvc;
using System;
using System.Reflection;

namespace LepCore.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class DiagnosticController : ControllerBase
    {
        [HttpGet("health")]
        public IActionResult Health()
        {
            return Ok(new
            {
                Status = "Healthy",
                Timestamp = DateTime.Now,
                Version = Assembly.GetExecutingAssembly().GetName().Version?.ToString(),
                Environment = Environment.MachineName,
                ProcessId = Environment.ProcessId
            });
        }

        [HttpGet("startup")]
        public IActionResult StartupInfo()
        {
            return Ok(new
            {
                StartupTime = DateTime.Now,
                WorkingDirectory = Environment.CurrentDirectory,
                RuntimeVersion = Environment.Version.ToString(),
                OSVersion = Environment.OSVersion.ToString(),
                Is64BitProcess = Environment.Is64BitProcess,
                ProcessorCount = Environment.ProcessorCount
            });
        }
    }
}
