-- Debug price calculation for specific orders
-- Check all the components that go into PriceOfJobs calculation

USE PRD_AU;
GO

-- Check the specific orders and their pricing components
SELECT
    o.Id,
    o.Price as OrderPrice,
    o.PromotionBenefit,
    o.<PERSON>p<PERSON>harge,
    o.FreightPrice,
    o.GST,
    c.Name as CustomerName,
    -- Calculate raw job total
    (SELECT SUM(ISNULL(TRY_CAST(j.Price as decimal(18,2)), 0)) FROM Job j WHERE j.OrderId = o.Id) as JobsTotal,
    -- Check if there are any jobs
    (SELECT COUNT(*) FROM Job j WHERE j.OrderId = o.Id) as JobCount,
    -- Check promotion details
    o.PromotionId
FROM [Order] o
    INNER JOIN Customer c ON o.userId = c.Id
WHERE o.Id IN (1419173, 1419424, 1419863, 1411056)
ORDER BY o.Id;

-- Check individual jobs for these orders
SELECT
    j.Id as JobId,
    j.OrderId,
    j.<PERSON> as <PERSON><PERSON><PERSON>,
    TRY_CAST(j.<PERSON> as decimal(18,2)) as JobPriceDecimal,
    j.<PERSON>QuotePrice
FROM Job j
WHERE j.OrderId IN (1419173, 1419424, 1419863, 1411056)
ORDER BY j.OrderId, j.Id;

-- Check if these orders have any special conditions
SELECT 
    o.Id,
    o.IsQuote,
    o.PaymentStatus,
    o.Status,
    o.IsDeleted,
    o.IsEnable
FROM [Order] o
WHERE o.Id IN (1419173, 1419424, 1419863, 1411056)
ORDER BY o.Id;
