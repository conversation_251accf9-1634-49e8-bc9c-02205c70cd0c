<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2"
                   namespace="lep.timing.entities"
                   assembly="lep"
                   auto-import="true"
                   default-cascade="none">

    <class name="JobStepTimingSummary" table="JobStepTimingSummary">
        <cache usage="read-write" />

        <id name="Id" type="Int32" unsaved-value="0">
            <generator class="identity" />
        </id>

        <property name="JobId" column="JobId" type="Int32" not-null="true" />
        <property name="UserId" column="UserId" type="Int32" not-null="true" />
        <property name="Status" column="Status" type="String" length="24" not-null="true" />
        <property name="SessionId" column="SessionId" type="Guid" not-null="true" />
        <property name="TotalDuration" column="TotalDuration" type="Int32" not-null="true" />
        <property name="TotalPausedTime" column="TotalPausedTime" type="Int32" not-null="true" />
        <property name="SessionStart" column="SessionStart" type="DateTime" not-null="true" />
        <property name="SessionEnd" column="SessionEnd" type="DateTime" />
        <property name="EventCount" column="EventCount" type="Int32" not-null="true" />
        <property name="IsCompleted" column="IsCompleted" type="Boolean" not-null="true" />
        <property name="DateCreated" column="DateCreated" type="DateTime" not-null="true" />
        <property name="DateModified" column="DateModified" type="DateTime" not-null="true" />

        <!-- Navigation properties -->
        <many-to-one name="Job" column="JobId" class="lep.job.impl.Job, lep" insert="false" update="false" />
        <many-to-one name="User" column="UserId" class="lep.user.IUser, lep" insert="false" update="false" />
    </class>
</hibernate-mapping>
