### eslint-disable ###
### jshint ignore: start ###

appCore = angular.module('app.core')

# Compact Job Details Directive for Job Board Popup
appCore.directive 'lepJobDetailsCompact', () ->
    restrict: 'EA'
    scope:
        job: '='
        showOrderInfo: '=?'  # Optional: show/hide order information
    templateUrl: 'common/directives/job-details-compact.component.html'
    controller: 'JobDetailsCompactController'

appCore.controller 'JobDetailsCompactController', [
    '$scope', '$log', 'enums',
    ($scope, $log, enums) ->
        $scope._ = 'JobDetailsCompactController'
        
        # Initialize defaults
        $scope.showOrderInfo = $scope.showOrderInfo ? true
        
        # Helper function to check if job has special options
        $scope.hasSpecialOptions = () ->
            job = $scope.job
            return false unless job
            
            return !!(
                job.RoundOption ||
                job.HoleDrilling ||
                job.Perforating ||
                job.Scoring ||
                job.CustomDieCut ||
                job.NumberOfMagnets ||
                job.FoilColour
            )
        
        # Helper function to format size display
        $scope.formatSize = (size) ->
            return '' unless size?.PaperSize?.Name
            
            if size.PaperSize.Name == 'Custom'
                return "#{size.Width} × #{size.Height}mm"
            else if size.Width && size.Height
                return "#{size.PaperSize.Name} (#{size.Width} × #{size.Height}mm)"
            else
                return size.PaperSize.Name
        
        # Helper function to get print type display
        $scope.getPrintTypeDisplay = (printType) ->
            switch printType
                when 'O' then 'Offset'
                when 'D' then 'Digital'
                when 'W' then 'Wide Format'
                when 'N' then 'N/A'
                else printType || 'Unknown'
        
        # Helper function to get cello display
        $scope.getCelloDisplay = (frontCello, backCello) ->
            return '' unless frontCello || backCello
            
            frontDesc = $scope.$root?.enums?.ValueDesc?.JobCelloglazeOptions?[frontCello] || 'None'
            backDesc = $scope.$root?.enums?.ValueDesc?.JobCelloglazeOptions?[backCello] || 'None'
            
            if frontDesc == backDesc
                return frontDesc
            else
                return "#{frontDesc} / #{backDesc}"
        
        # Helper function to get status CSS class
        $scope.getStatusClass = (status) ->
            return 'default' unless status
            
            statusLower = status.toString().toLowerCase()
            switch statusLower
                when 'ready' then 'success'
                when 'printing', 'inprogress', 'filling' then 'info'
                when 'finished', 'packed', 'despatched' then 'success'
                when 'onhold' then 'warning'
                when 'cancelled' then 'danger'
                when 'setup' then 'primary'
                else 'default'
        
        # Helper function to check if job is urgent
        $scope.isUrgent = () ->
            job = $scope.job
            return false unless job?.PrintByDate
            
            now = new Date()
            printBy = new Date(job.PrintByDate)
            hoursUntilDue = (printBy - now) / (1000 * 60 * 60)
            
            return hoursUntilDue <= 24  # Urgent if due within 24 hours
        
        # Helper function to get urgency class
        $scope.getUrgencyClass = () ->
            if $scope.isUrgent()
                return 'text-danger'
            else
                return 'text-warning'
        
        # Watch for job changes and update status CSS
        $scope.$watch 'job', (newJob, oldJob) ->
            if newJob
                $log.debug 'JobDetailsCompact: Job loaded', newJob.Id, newJob.Name
                
                # Ensure we have required data
                if !newJob.Template
                    newJob.Template = { Name: 'Unknown Template' }
                
                if !newJob.FinishedSize
                    newJob.FinishedSize = { PaperSize: { Name: 'Unknown' } }
                
                # Set status CSS if not already set
                if !newJob.StatusCss
                    newJob.StatusCss = $scope.getStatusClass(newJob.Status || newJob.StatusC)
        
        # Initialize when controller loads
        $scope.$on '$viewContentLoaded', () ->
            $log.debug 'JobDetailsCompact: View loaded'
]

### jshint ignore: end ###
