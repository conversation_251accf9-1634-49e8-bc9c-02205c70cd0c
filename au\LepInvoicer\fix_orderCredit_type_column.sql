-- Fix OrderCredit.Type column to remove space padding
-- Change from char(10) to varchar(10) to eliminate trailing spaces

USE PRD_AU;
GO

-- Check current data before the change
SELECT
    'BEFORE CHANGE' as Status,
    Type,
    LEN(Type) as TypeLength,
    DATALENGTH(Type) as TypeDataLength,
    COUNT(*) as RecordCount
FROM OrderCredit
GROUP BY Type, LEN(Type), DATALENGTH(Type)
ORDER BY Type;

-- Drop the default constraint on Type column
ALTER TABLE OrderCredit
DROP CONSTRAINT DF__OrderCredi__Type__66C12539;

-- Alter the column from char(10) to varchar(10)
ALTER TABLE OrderCredit
ALTER COLUMN Type varchar(10) NOT NULL;

-- Check data after the change
SELECT 
    'AFTER CHANGE' as Status,
    Type,
    LEN(Type) as TypeLength,
    DATALENGTH(Type) as TypeDataLength,
    COUNT(*) as RecordCount
FROM OrderCredit
GROUP BY Type, LEN(Type), DATALENGTH(Type)
ORDER BY Type;

-- Verify the column definition has changed
SELECT 
    COLUMN_NAME, 
    DATA_TYPE, 
    CHARACTER_MAXIMUM_LENGTH, 
    IS_NULLABLE 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'OrderCredit' 
    AND COLUMN_NAME = 'Type';

PRINT 'OrderCredit.Type column successfully changed from char(10) to varchar(10)';
