{"name": "asp.net", "private": true, "bower_components": "bower_components", "dependencies": {"jquery": "2.2.4", "bootstrap": "3.3.7", "moment": "2.18.1", "lodash": "4.17.15", "bootbox": "5.3.4", "angular": "1.7.5", "angular-loader": "1.6.2", "angular-resource": "1.6.2", "angular-strap": "2.3.12", "angular-ui-router": "0.4.2", "angularjs-slider": "^5.3.0", "angular-loading-bar": "0.9.0", "angular-moment": "1.0.1", "angular-pager": "https://github.com/brantwills/Angular-Paging.git#v2.2.2", "angular-ui-grid": "4.6.6", "ng-dialog": "1.4.0", "ngstorage": "^0.3.10", "toastr": "^2.1.2", "angular-animate-model-change": "ng-animate-model-change#^0.0.1", "angularjs-datetime-picker": "^0.1.20", "angucomplete-alt": "^3.0.0", "ng-droplet": "^0.7.0", "angular-ui-select": "^0.19.8", "angular-ui-sortable": "^0.19.0", "oclazyload": "^1.1.0", "angular-cookies": "1.6.2", "signature_pad": "^4.0.4"}, "overrides": {"fast-json-patch": {"main": ["dist/json-patch-duplex.min.js"]}, "angularjs-datetime-picker": {"main": ["angularjs-datetime-picker.css", "angularjs-datetime-picker.js"]}, "angular-animate-model-change": {"main": ["dist/ng-animate-model-change.css", "dist/ng-animate-model-change.js"]}, "angularjs-slider": {"main": ["dist/rzslider.js", "dist/rzslider.css"]}, "bootstrap": {"main": ["dist/js/bootstrap.js"]}, "jsondiffpatch": {"main": ["public/build/jsondiffpatch.js", "public/build/jsondiffpatch-formatters.js", "public/formatters-styles/annotated.css", "public/formatters-styles/html.css"]}, "angular-ui-grid": {"main": ["ui-grid.css", "ui-grid.js", "ui-grid.eot", "ui-grid.svg", "ui-grid.ttf", "ui-grid.woff"]}, "angucomplete-alt": {"main": ["angucomplete-alt.js"]}, "angulartics": {"main": ["src/angulartics.js", "src/angulartics-debug.js", "src/angulartics-gtm.js"]}, "ng-dialog": {"main": ["js/ngDialog.js"]}}, "resolutions": {"jquery": ">=1.6", "angular": "1.6.2"}}