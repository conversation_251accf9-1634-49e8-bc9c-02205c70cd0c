using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc.Filters;
using Serilog;
using lep.timing;
using lep.timing.dto;
using lep.security;
using lep.user;

namespace LepCore.BackEnd.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [AllowAnonymous]
    public class JobTimingController : Controller
    {
        private readonly ITimingApplication _timingApp;

        public JobTimingController(ITimingApplication timingApp)
        {
            _timingApp = timingApp;
        }

        [HttpPost("play")]
        public async Task<IActionResult> Play([FromBody] TimingActionRequest request)
        {
            try
            {
                request.WorkstationId = GetWorkstationId();

                Log.Information("🎬 TIMING PLAY REQUEST - JobId: {JobId}, Status: {Status}, WorkstationId: {WorkstationId}",
                    request.JobId, request.Status, request.WorkstationId);

                var result = await _timingApp.Play(request, 1); // Use hardcoded user ID 1 for testing

                Log.Information("🎬 TIMING PLAY RESULT - JobId: {JobId}, Success: {Success}, Message: {Message}, SessionId: {SessionId}, ButtonState: {@ButtonState}",
                    request.JobId, result.Success, result.Message, result.SessionId, result.ButtonState);

                return Ok(result);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "❌ ERROR in JobTiming Play endpoint - JobId: {JobId}", request?.JobId);
                return StatusCode(500, new { success = false, message = "Internal server error" });
            }
        }

        [HttpPost("pause")]
        public async Task<IActionResult> Pause([FromBody] TimingActionRequest request)
        {
            try
            {
                request.WorkstationId = GetWorkstationId();
                var result = await _timingApp.Pause(request, 1);

                Log.Information("Timing PAUSE for Job {JobId}: {Success}", request.JobId, result.Success);
                return Ok(result);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error in JobTiming Pause endpoint");
                return StatusCode(500, new { success = false, message = "Internal server error" });
            }
        }

        [HttpPost("resume")]
        public async Task<IActionResult> Resume([FromBody] TimingActionRequest request)
        {
            try
            {
                request.WorkstationId = GetWorkstationId();
                var result = await _timingApp.Resume(request, 1);

                Log.Information("Timing RESUME for Job {JobId}: {Success}", request.JobId, result.Success);
                return Ok(result);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error in JobTiming Resume endpoint");
                return StatusCode(500, new { success = false, message = "Internal server error" });
            }
        }

        [HttpPost("finish")]
        public async Task<IActionResult> Finish([FromBody] TimingActionRequest request)
        {
            try
            {
                request.WorkstationId = GetWorkstationId();
                var result = await _timingApp.Finish(request, 1);

                Log.Information("Timing FINISH for Job {JobId}: {Success}", request.JobId, result.Success);
                return Ok(result);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error in JobTiming Finish endpoint");
                return StatusCode(500, new { success = false, message = "Internal server error" });
            }
        }

        [HttpGet("state/{jobId}/{status}")]
        public async Task<IActionResult> GetButtonState(int jobId, string status)
        {
            try
            {
                Log.Information("🔍 GET BUTTON STATE REQUEST - JobId: {JobId}, Status: {Status}", jobId, status);

                var state = await _timingApp.GetButtonState(jobId, 1, status);

                Log.Information("🔍 GET BUTTON STATE RESULT - JobId: {JobId}, Status: {Status}, ButtonState: {@ButtonState}",
                    jobId, status, state);

                return Ok(state);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "❌ ERROR getting button state for Job {JobId}, Status {Status}", jobId, status);
                return StatusCode(500, new { success = false, message = "Internal server error" });
            }
        }

        [HttpGet("user/active")]
        public async Task<IActionResult> GetUserActiveSessions()
        {
            try
            {
                var sessions = await _timingApp.GetUserActiveSessions(1);
                return Ok(sessions);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error getting active sessions for current user");
                return StatusCode(500, new { success = false, message = "Internal server error" });
            }
        }

        [HttpGet("job/{jobId}/history")]
        public async Task<IActionResult> GetJobTimingHistory(int jobId)
        {
            try
            {
                var history = await _timingApp.GetJobTimingHistory(jobId);
                return Ok(history);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error getting timing history for Job {JobId}", jobId);
                return StatusCode(500, new { success = false, message = "Internal server error" });
            }
        }

        [HttpGet("job/{jobId}/summary")]
        public async Task<IActionResult> GetJobTimingSummary(int jobId)
        {
            try
            {
                var summary = await _timingApp.GetJobTimingSummary(jobId);
                return Ok(summary);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error getting timing summary for Job {JobId}", jobId);
                return StatusCode(500, new { success = false, message = "Internal server error" });
            }
        }

        [HttpPost("stop-all")]
        public async Task<IActionResult> StopAllUserSessions()
        {
            try
            {
                var stoppedCount = await _timingApp.StopAllUserSessions(1);
                Log.Information("Stopped {Count} active sessions for user 1", stoppedCount);
                return Ok(new { success = true, message = $"Stopped {stoppedCount} active sessions", count = stoppedCount });
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error stopping all user sessions");
                return StatusCode(500, new { success = false, message = "Internal server error" });
            }
        }

        [HttpGet("can-start/{jobId}/{status}")]
        public async Task<IActionResult> CanUserStartTiming(int jobId, string status)
        {
            try
            {
                var canStart = await _timingApp.CanUserStartTiming(jobId, 1, status);
                return Ok(new { canStart });
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error checking if user can start timing");
                return StatusCode(500, new { success = false, message = "Internal server error" });
            }
        }

        private string GetWorkstationId()
        {
            // Try to get workstation ID from headers or generate one
            var workstationId = Request.Headers["X-Workstation-Id"].FirstOrDefault();
            if (string.IsNullOrEmpty(workstationId))
            {
                workstationId = $"WEB-{Environment.MachineName}-{DateTime.Now:yyyyMMdd}";
            }
            return workstationId;
        }
    }
}
