appStaff = angular.module('app.staff')

appStaff.service 'JobBoardSignalRService', [
    '$q', '$rootScope'
    ($q, $rootScope) ->

        # SignalR connection management (integrated from signalr.service.coffee)
        connection = null
        isConnected = false
        connectionPromise = null
        subscriptions = new Map()

        # Initialize SignalR connection
        connect = () =>
            return connectionPromise if connectionPromise

            connectionPromise = $q (resolve, reject) ->
                try
                    # Create connection (compatible with SignalR 1.0.x)
                    connection = new signalR.HubConnectionBuilder()
                        .withUrl('/lepcorehub')
                        .configureLogging(signalR.LogLevel.Information)
                        .build()

                    # Connection event handlers (SignalR 1.0.x compatible)
                    connection.onclose (error) ->
                        console.log('❌ SignalR: Connection closed', error)
                        isConnected = false
                        connectionPromise = null

                        # Manual reconnection logic for SignalR 1.0.x
                        if error
                            console.log('🔄 SignalR: Attempting to reconnect in 5 seconds...')
                            setTimeout(() ->
                                attemptReconnect()
                            , 5000)

                        $rootScope.$apply()

                    # Setup event listeners
                    setupEventListeners()

                    # Start connection
                    connection.start()
                        .then () ->
                            console.log('✅ SignalR: Connected successfully')
                            isConnected = true
                            resolve(connection)
                            $rootScope.$apply()
                        .catch (error) ->
                            console.error('❌ SignalR: Connection failed', error)
                            connectionPromise = null
                            reject(error)

                catch error
                    console.error('❌ SignalR: Setup failed', error)
                    connectionPromise = null
                    reject(error)

            return connectionPromise

        # Manual reconnection for SignalR 1.0.x
        attemptReconnect = () =>
            if connectionPromise
                return # Already attempting to connect

            console.log('🔄 SignalR: Attempting manual reconnection...')
            connectionPromise = null
            connect().then(() ->
                console.log('✅ SignalR: Reconnected successfully')
                resubscribeAll()
            ).catch((error) ->
                console.error('❌ SignalR: Reconnection failed', error)
                # Try again in 10 seconds
                setTimeout(() ->
                    attemptReconnect()
                , 10000)
            )

        # Setup all event listeners
        setupEventListeners = () =>
            return unless connection

            # Timing events - broadcast to Angular scope for service-agnostic handling
            connection.on 'TimingActionResult', (result) ->
                console.log('🎬 SignalR: Timing action result:', result)
                $rootScope.$broadcast('timing:actionResult', result)
                $rootScope.$apply()

            connection.on 'TimingActionError', (error) ->
                console.error('❌ SignalR: Timing action error:', error)
                $rootScope.$broadcast('timing:actionError', error)
                $rootScope.$apply()

            connection.on 'TimingStateChanged', (data) ->
                console.log('🔄 SignalR: Timing state changed:', data)
                $rootScope.$broadcast('timing:stateChanged', data)
                $rootScope.$apply()

            # Job board events - broadcast to Angular scope for service-agnostic handling
            connection.on 'JobBoardRefreshRequired', (data) ->
                console.log('📋 SignalR: Job board refresh required:', data)
                $rootScope.$broadcast('jobboard:refreshRequired', data)
                $rootScope.$apply()

            connection.on 'JobStatusChanged', (data) ->
                console.log('🔄 SignalR: Job status changed:', data)
                $rootScope.$broadcast('job:statusChanged', data)
                $rootScope.$apply()

            # Subscription confirmations
            connection.on 'SubscriptionConfirmed', (groupName) ->
                console.log('✅ SignalR: Subscription confirmed for:', groupName)
                $rootScope.$apply()

            # Additional event listeners for service responses
            connection.on 'GetJobsResult', (result) ->
                console.log('📋 SignalR: GetJobs result:', result)
                $rootScope.$broadcast('service:getJobsResult', result)
                $rootScope.$apply()

            connection.on 'GetJobsError', (error) ->
                console.error('❌ SignalR: GetJobs error:', error)
                $rootScope.$broadcast('service:getJobsError', error)
                $rootScope.$apply()

            connection.on 'GetVersionResult', (result) ->
                console.log('📋 SignalR: GetVersion result:', result)
                $rootScope.$broadcast('service:getVersionResult', result)
                $rootScope.$apply()

            connection.on 'TimingStateResult', (result) ->
                console.log('🔍 SignalR: TimingState result:', result)
                $rootScope.$broadcast('service:timingStateResult', result)
                $rootScope.$apply()

            connection.on 'GetProductionInstructionsResult', (result) ->
                console.log('📋 SignalR: ProductionInstructions result:', result)
                $rootScope.$broadcast('service:productionInstructionsResult', result)
                $rootScope.$apply()

        # Resubscribe to all groups after reconnection
        resubscribeAll = () =>
            subscriptions.forEach (value, key) ->
                parts = key.split('_')
                if parts.length >= 2
                    boardType = parts[0]
                    facility = if parts.length > 2 then parts[1] else null
                    subscribeToJobBoard(boardType, facility)

        # Job Board Subscriptions
        subscribeToJobBoard = (boardType, facility = null) =>
            connect().then () ->
                key = "#{boardType}_#{facility || 'All'}"
                subscriptions.set(key, true)
                connection.invoke('SubscribeToJobBoard', boardType, facility)
                    .catch (error) ->
                        console.error('❌ SignalR: Subscribe failed:', error)
                        subscriptions.delete(key)

        unsubscribeFromJobBoard = (boardType, facility = null) =>
            return unless isConnected
            key = "#{boardType}_#{facility || 'All'}"
            subscriptions.delete(key)
            connection.invoke('UnsubscribeFromJobBoard', boardType, facility)
                .catch (error) ->
                    console.error('❌ SignalR: Unsubscribe failed:', error)

        # Generic method invocation for any controller action
        invokeMethod = (methodName, args...) =>
            connect().then () ->
                console.log("🔧 SignalR: Invoking #{methodName} with args:", args)
                connection.invoke.apply(connection, [methodName].concat(args))
                    .catch (error) ->
                        console.error("❌ SignalR: Method #{methodName} failed:", error)
                        throw error

        # One-time event listener (removes itself after first call)
        onceEvent = (eventName, callback) =>
            if !connection
                console.warn("⚠️ SignalR: Cannot set up one-time event listener - not connected")
                return

            wrappedCallback = (data) ->
                try
                    callback(data)
                finally
                    # Remove the event listener after it fires once
                    connection.off(eventName, wrappedCallback)

            connection.on(eventName, wrappedCallback)
            console.log("🔔 SignalR: Set up one-time listener for event: #{eventName}")

        # Core SignalR timing operations (from signalr.service.coffee)
        startTimingCore = (jobId, status, notes = null, instructionsViewed = false, qualityChecksPassed = false) =>
            connect().then () ->
                console.log("🎬 SignalR: Starting timing for Job #{jobId}, Status #{status}")
                # Set workstation ID header for SignalR hub to use
                if connection.connection
                    connection.connection.headers = connection.connection.headers || {}
                    connection.connection.headers['X-Workstation-Id'] = getWorkstationId()
                connection.invoke('StartTiming', jobId, status, notes, instructionsViewed, qualityChecksPassed)
                    .catch (error) ->
                        console.error('❌ SignalR: Start timing failed:', error)
                        throw error

        pauseTimingCore = (jobId, status, notes = null) =>
            return unless isConnected
            console.log("⏸️ SignalR: Pausing timing for Job #{jobId}, Status #{status}")
            if connection.connection
                connection.connection.headers = connection.connection.headers || {}
                connection.connection.headers['X-Workstation-Id'] = getWorkstationId()
            connection.invoke('PauseTiming', jobId, status, notes)
                .catch (error) ->
                    console.error('❌ SignalR: Pause timing failed:', error)
                    throw error

        resumeTimingCore = (jobId, status, notes = null) =>
            return unless isConnected
            console.log("▶️ SignalR: Resuming timing for Job #{jobId}, Status #{status}")
            if connection.connection
                connection.connection.headers = connection.connection.headers || {}
                connection.connection.headers['X-Workstation-Id'] = getWorkstationId()
            connection.invoke('ResumeTiming', jobId, status, notes)
                .catch (error) ->
                    console.error('❌ SignalR: Resume timing failed:', error)
                    throw error

        finishTimingCore = (jobId, status, notes = null) =>
            return unless isConnected
            console.log("✅ SignalR: Finishing timing for Job #{jobId}, Status #{status}")
            if connection.connection
                connection.connection.headers = connection.connection.headers || {}
                connection.connection.headers['X-Workstation-Id'] = getWorkstationId()
            connection.invoke('FinishTiming', jobId, status, notes)
                .catch (error) ->
                    console.error('❌ SignalR: Finish timing failed:', error)
                    throw error

        # Helper function to get workstation ID
        getWorkstationId = () ->
            workstationId = localStorage.getItem('workstationId')
            if !workstationId
                workstationId = "WS-#{Date.now()}-#{Math.random().toString(36).substr(2, 9)}"
                localStorage.setItem('workstationId', workstationId)
            workstationId

        # Auto-connect on service creation
        connect()

        # Job Board API methods (existing functionality)

        @getJobs = (facilityStr, board) ->
            console.log("🌐 SIGNALR SERVICE: GET JOBS - Facility: #{facilityStr}, Board: #{board}")

            deferred = $q.defer()

            # Set up one-time event listener for the response
            responseHandler = (result) ->
                console.log("🌐 SIGNALR SERVICE: GET JOBS RESPONSE:", result)
                deferred.resolve(result)

            errorHandler = (error) ->
                console.error("🌐 SIGNALR SERVICE: GET JOBS ERROR:", error)
                deferred.reject(error)

            onceEvent('GetJobsResult', responseHandler)
            onceEvent('GetJobsError', errorHandler)

            invokeMethod('GetJobs', facilityStr || 'FG', board || 'All')
                .catch (error) ->
                    console.error("🌐 SIGNALR SERVICE: GET JOBS INVOKE ERROR:", error)
                    deferred.reject(error)

            deferred.promise

        @getVersion = () ->
            console.log("🌐 SIGNALR SERVICE: GET VERSION")

            deferred = $q.defer()

            # Set up one-time event listener for the response
            responseHandler = (result) ->
                console.log("🌐 SIGNALR SERVICE: GET VERSION RESPONSE:", result)
                deferred.resolve(result)

            errorHandler = (error) ->
                console.error("🌐 SIGNALR SERVICE: GET VERSION ERROR:", error)
                deferred.reject(error)

            onceEvent('GetVersionResult', responseHandler)
            onceEvent('GetVersionError', errorHandler)

            invokeMethod('GetVersion')
                .catch (error) ->
                    console.error("🌐 SIGNALR SERVICE: GET VERSION INVOKE ERROR:", error)
                    deferred.reject(error)

            deferred.promise

        # Timing API methods (SignalR only)
        @startTiming = (jobId, status, notes, instructionsViewed, qualityChecksPassed) ->
            console.log("🌐 SIGNALR SERVICE: START TIMING REQUEST - JobId: #{jobId}, Status: #{status}")

            deferred = $q.defer()

            # Set up one-time event listeners for the response
            responseHandler = (result) ->
                console.log("🌐 SIGNALR SERVICE: START TIMING RESPONSE:", result)
                deferred.resolve(result)

            errorHandler = (error) ->
                console.error("🌐 SIGNALR SERVICE: START TIMING ERROR:", error)
                deferred.reject(error)

            onceEvent('TimingActionResult', responseHandler)
            onceEvent('TimingActionError', errorHandler)

            # Send the SignalR request
            startTimingCore(jobId, status, notes, instructionsViewed, qualityChecksPassed)
                .catch (error) ->
                    console.error("🌐 SIGNALR SERVICE: START TIMING INVOKE ERROR:", error)
                    deferred.reject(error)

            deferred.promise

        @pauseTiming = (jobId, status, notes) ->
            console.log("🌐 SIGNALR SERVICE: PAUSE TIMING REQUEST - JobId: #{jobId}, Status: #{status}")

            deferred = $q.defer()

            # Set up one-time event listeners for the response
            responseHandler = (result) ->
                console.log("🌐 SIGNALR SERVICE: PAUSE TIMING RESPONSE:", result)
                deferred.resolve(result)

            errorHandler = (error) ->
                console.error("🌐 SIGNALR SERVICE: PAUSE TIMING ERROR:", error)
                deferred.reject(error)

            onceEvent('TimingActionResult', responseHandler)
            onceEvent('TimingActionError', errorHandler)

            # Send the SignalR request
            pauseTimingCore(jobId, status, notes)
                .catch (error) ->
                    console.error("🌐 SIGNALR SERVICE: PAUSE TIMING INVOKE ERROR:", error)
                    deferred.reject(error)

            deferred.promise

        @resumeTiming = (jobId, status, notes) ->
            console.log("🌐 SIGNALR SERVICE: RESUME TIMING REQUEST - JobId: #{jobId}, Status: #{status}")

            deferred = $q.defer()

            # Set up one-time event listeners for the response
            responseHandler = (result) ->
                console.log("🌐 SIGNALR SERVICE: RESUME TIMING RESPONSE:", result)
                deferred.resolve(result)

            errorHandler = (error) ->
                console.error("🌐 SIGNALR SERVICE: RESUME TIMING ERROR:", error)
                deferred.reject(error)

            onceEvent('TimingActionResult', responseHandler)
            onceEvent('TimingActionError', errorHandler)

            # Send the SignalR request
            resumeTimingCore(jobId, status, notes)
                .catch (error) ->
                    console.error("🌐 SIGNALR SERVICE: RESUME TIMING INVOKE ERROR:", error)
                    deferred.reject(error)

            deferred.promise

        @finishTiming = (jobId, status, notes) ->
            console.log("🌐 SIGNALR SERVICE: FINISH TIMING REQUEST - JobId: #{jobId}, Status: #{status}")

            deferred = $q.defer()

            # Set up one-time event listeners for the response
            responseHandler = (result) ->
                console.log("🌐 SIGNALR SERVICE: FINISH TIMING RESPONSE:", result)
                deferred.resolve(result)

            errorHandler = (error) ->
                console.error("🌐 SIGNALR SERVICE: FINISH TIMING ERROR:", error)
                deferred.reject(error)

            onceEvent('TimingActionResult', responseHandler)
            onceEvent('TimingActionError', errorHandler)

            # Send the SignalR request
            finishTimingCore(jobId, status, notes)
                .catch (error) ->
                    console.error("🌐 SIGNALR SERVICE: FINISH TIMING INVOKE ERROR:", error)
                    deferred.reject(error)

            deferred.promise

        @getTimingState = (jobId, status) ->
            console.log("🌐 SIGNALR SERVICE: GET TIMING STATE REQUEST - JobId: #{jobId}, Status: #{status}")

            deferred = $q.defer()

            # Set up one-time event listeners
            responseHandler = (result) ->
                console.log("🌐 SIGNALR SERVICE: GET TIMING STATE RESPONSE:", result)
                deferred.resolve(result)

            errorHandler = (error) ->
                console.error("🌐 SIGNALR SERVICE: GET TIMING STATE ERROR:", error)
                deferred.reject(error)

            onceEvent('TimingStateResult', responseHandler)
            onceEvent('TimingStateError', errorHandler)

            invokeMethod('GetTimingState', jobId, status)
                .catch (error) ->
                    console.error("🌐 SIGNALR SERVICE: GET TIMING STATE INVOKE ERROR:", error)
                    deferred.reject(error)

            deferred.promise

        @stopAllSessions = () ->
            console.log("🌐 SIGNALR SERVICE: STOP ALL SESSIONS REQUEST")

            deferred = $q.defer()

            # Set up one-time event listeners
            responseHandler = (result) ->
                console.log("🌐 SIGNALR SERVICE: STOP ALL SESSIONS RESPONSE:", result)
                deferred.resolve(result)

            errorHandler = (error) ->
                console.error("🌐 SIGNALR SERVICE: STOP ALL SESSIONS ERROR:", error)
                deferred.reject(error)

            onceEvent('StopAllSessionsResult', responseHandler)
            onceEvent('StopAllSessionsError', errorHandler)

            invokeMethod('StopAllSessions')
                .catch (error) ->
                    console.error("🌐 SIGNALR SERVICE: STOP ALL SESSIONS INVOKE ERROR:", error)
                    deferred.reject(error)

            deferred.promise

        @getProductionInstructions = (status) ->
            console.log("🌐 SIGNALR SERVICE: GET PRODUCTION INSTRUCTIONS REQUEST - Status: #{status}")

            deferred = $q.defer()

            # Set up one-time event listener (only success, errors resolve to null)
            responseHandler = (result) ->
                console.log("🌐 SIGNALR SERVICE: GET PRODUCTION INSTRUCTIONS RESPONSE:", result)
                deferred.resolve(result)

            onceEvent('GetProductionInstructionsResult', responseHandler)

            invokeMethod('GetProductionInstructions', status)
                .catch (error) ->
                    console.warn("🌐 SIGNALR SERVICE: Production instructions not available:", error)
                    deferred.resolve(null)  # Match API service behavior

            deferred.promise

        @getWorkstationId = getWorkstationId

        # Additional SignalR connection methods (for compatibility)
        @connect = connect
        @isConnected = () -> isConnected
        @getConnection = () -> connection
        @subscribeToJobBoard = subscribeToJobBoard
        @unsubscribeFromJobBoard = unsubscribeFromJobBoard
        @invokeMethod = invokeMethod
        @onceEvent = onceEvent

        # Disconnect method
        @disconnect = () ->
            if connection && isConnected
                connection.stop()
                isConnected = false
                connectionPromise = null
                subscriptions.clear()

        # Additional timing operations (for compatibility with original signalr.service.coffee)
        @getJobDetails = (jobId) ->
            return unless isConnected
            connection.invoke('GetJobDetails', jobId)
                .catch (error) ->
                    console.error('❌ SignalR: Get job details failed:', error)
                    throw error

        @updateJobStatus = (jobId, newStatus) ->
            return unless isConnected
            connection.invoke('UpdateJobStatus', jobId, newStatus)
                .catch (error) ->
                    console.error('❌ SignalR: Update job status failed:', error)
                    throw error

        @
]
