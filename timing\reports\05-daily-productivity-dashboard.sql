-- Daily Productivity Dashboard
-- Daily summary of work completed by status and worker

WITH DailyTiming AS (
    SELECT
        CAST(e.EventTime AS DATE) AS WorkDate,
        e.Status,
        e.UserId,
        e.WorkstationId,
        e.JobId,
        e.EventType,
        e.EventTime,
        LAG(e.EventTime) OVER (PARTITION BY e.JobId, e.Status, e.UserId, CAST(e.EventTime AS DATE) ORDER BY e.EventTime) AS PrevEventTime,
        LAG(e.EventType) OVER (PARTITION BY e.JobId, e.Status, e.UserId, CAST(e.EventTime AS DATE) ORDER BY e.EventTime) AS PrevEventType
    FROM JobStepTimingEvent e
    WHERE e.EventTime >= DATEADD(DAY, -30, GETDATE()) -- Last 30 days
)
SELECT
    dt.WorkDate,
    dt.Status,
    dt.UserId,
    u.UserName,
    dt.WorkstationId,
    COUNT(DISTINCT dt.JobId) AS JobsCompleted,
    SUM(CASE WHEN dt.EventType = 4 AND dt.PrevEventType = 1 THEN -- FINISH after PLAY
        DATEDIFF(SECOND, dt.PrevEventTime, dt.EventTime)
    END) AS TotalSecondsWorked,
    FORMAT(DATEADD(SECOND,
        SUM(CASE WHEN dt.EventType = 4 AND dt.PrevEventType = 1 THEN
            DATEDIFF(SECOND, dt.PrevEventTime, dt.EventTime)
        END), 0), 'HH:mm:ss') AS TotalTimeFormatted,
    MIN(dt.EventTime) AS FirstActivity,
    MAX(dt.EventTime) AS LastActivity,
    DATEDIFF(HOUR, MIN(dt.EventTime), MAX(dt.EventTime)) AS HoursSpan
FROM DailyTiming dt
    INNER JOIN LepUser u ON dt.UserId = u.Id
GROUP BY dt.WorkDate, dt.Status, dt.UserId, u.UserName, dt.WorkstationId
ORDER BY WorkDate DESC, TotalSecondsWorked DESC;
