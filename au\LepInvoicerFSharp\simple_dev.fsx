// F# Interactive Development - Simple version without external packages
open System

printfn "=== LEP Invoicer F# - Simple Functional Development ==="

// ============================================================================
// CORE DOMAIN TYPES - Pure functional types
// ============================================================================

type CreditType = 
    | Credit
    | Refund
    | Adjustment
    | Other

type Customer = {
    Id: int
    Name: string
    Username: string
}

type Job = {
    Id: int
    OrderId: int
    Description: string
    Price: decimal option
    IsEnabled: bool
}

type Order = {
    Id: int
    OrderNumber: string
    Customer: Customer
    Jobs: Job list
    FinishDate: DateTime option
    SubmissionDate: DateTime
    PurchaseOrder: string option
    PromotionBenefit: decimal
    PickUpCharge: decimal
    GST: decimal
    IsInvoiced: bool
}

type OrderCredit = {
    Id: int
    OrderId: int
    CustomerId: int
    CreditType: CreditType
    Amount: decimal
    Description: string
    IsInvoiced: bool
    DateCreated: DateTime
}

type InvoicerConfig = {
    ConnectionString: string
    InvoiceBatchSize: int
    RefundBatchSize: int
    MinimumFinishDate: DateTime
    IgnoreCustomers: string list
    TestMode: bool
}

// ============================================================================
// PURE BUSINESS LOGIC - No side effects
// ============================================================================

module OrderLogic =
    
    /// Calculate total price for an order (pure function)
    let calculateTotalPrice (order: Order) : decimal option =
        let jobTotal = 
            order.Jobs
            |> List.choose (fun job -> if job.IsEnabled then job.Price else None)
            |> List.sum
        
        if jobTotal > 0m then
            Some (jobTotal + order.PromotionBenefit + order.PickUpCharge + order.GST)
        else
            None
    
    /// Check if order is ready for invoicing (pure function)
    let isReadyForInvoicing (config: InvoicerConfig) (order: Order) : bool =
        let hasValidPrice = calculateTotalPrice order |> Option.isSome
        let isNotIgnored = not (List.contains order.Customer.Username config.IgnoreCustomers)
        let isFinished = 
            match order.FinishDate with
            | Some finishDate -> finishDate >= config.MinimumFinishDate
            | None -> false
        let isNotAlreadyInvoiced = not order.IsInvoiced
        
        hasValidPrice && isNotIgnored && isFinished && isNotAlreadyInvoiced

module CreditLogic =
    
    /// Check if credit is ready for invoicing (pure function)
    let isReadyForInvoicing (config: InvoicerConfig) (credit: OrderCredit) : bool =
        let hasValidAmount = credit.Amount > 0m
        let isNotAlreadyInvoiced = not credit.IsInvoiced
        
        hasValidAmount && isNotAlreadyInvoiced

// ============================================================================
// FUNCTIONAL ERROR HANDLING - Simple Result type
// ============================================================================

// We'll use the built-in Result type instead of external packages
type AsyncResult<'T, 'Error> = Async<Result<'T, 'Error>>

module AsyncResult =
    
    let retn (value: 'T) : AsyncResult<'T, 'Error> =
        async { return Ok value }
    
    let bind (f: 'T -> AsyncResult<'U, 'Error>) (asyncResult: AsyncResult<'T, 'Error>) : AsyncResult<'U, 'Error> =
        async {
            let! result = asyncResult
            match result with
            | Ok value -> return! f value
            | Error error -> return Error error
        }
    
    let map (f: 'T -> 'U) (asyncResult: AsyncResult<'T, 'Error>) : AsyncResult<'U, 'Error> =
        async {
            let! result = asyncResult
            return Result.map f result
        }

// ============================================================================
// TEST THE PURE FUNCTIONS
// ============================================================================

// Test data
let testCustomer = {
    Id = 1
    Name = "Test Customer"
    Username = "testuser"
}

let testJob = {
    Id = 1
    OrderId = 1
    Description = "Test Job"
    Price = Some 100.0m
    IsEnabled = true
}

let testOrder = {
    Id = 1
    OrderNumber = "O001"
    Customer = testCustomer
    Jobs = [testJob]
    FinishDate = Some DateTime.Now
    SubmissionDate = DateTime.Now
    PurchaseOrder = Some "PO123"
    PromotionBenefit = 5.0m
    PickUpCharge = 2.0m
    GST = 10.7m
    IsInvoiced = false
}

let testConfig = {
    ConnectionString = "test"
    InvoiceBatchSize = 10
    RefundBatchSize = 5
    MinimumFinishDate = DateTime.Now.AddDays(-1.0)
    IgnoreCustomers = []
    TestMode = true
}

// Test the pure functions
printfn "\n=== Testing Pure Business Logic ==="

let totalPrice = OrderLogic.calculateTotalPrice testOrder
printfn "Order total price: %A" totalPrice

let isReady = OrderLogic.isReadyForInvoicing testConfig testOrder
printfn "Order ready for invoicing: %b" isReady

let testCredit = {
    Id = 1
    OrderId = 1
    CustomerId = 1
    CreditType = Credit
    Amount = 50.0m
    Description = "Test credit"
    IsInvoiced = false
    DateCreated = DateTime.Now
}

let creditReady = CreditLogic.isReadyForInvoicing testConfig testCredit
printfn "Credit ready for invoicing: %b" creditReady

// Test AsyncResult
let testAsyncResult () =
    AsyncResult.retn 42
    |> AsyncResult.bind (fun x -> AsyncResult.retn (x * 2))
    |> AsyncResult.map (fun x -> x + 1)

let testResult = testAsyncResult () |> Async.RunSynchronously
printfn "AsyncResult test: %A" testResult

printfn "\n=== Pure functions working correctly! ==="
