### eslint-disable ###
### jshint ignore: start ###

appCore = angular.module('app.core')

# Job Board Popup Service
appCore.service 'JobBoardPopupService', [
    '$modal', '$log',
    ($modal, $log) ->
        
        # Open job board popup with timing and instructions
        @openJobBoardPopup = (jobId, status, options = {}) ->
            $log.debug 'Opening job board popup for job:', jobId, 'status:', status
            
            modalOptions = {
                templateUrl: 'common/templates/job-board-popup.html'
                controller: 'JobBoardPopupController'
                size: options.size || 'lg'
                backdrop: options.backdrop || 'static'
                keyboard: options.keyboard || true
                resolve: {
                    jobId: () -> jobId
                    status: () -> status
                }
            }
            
            modalInstance = $modal.open(modalOptions)
            
            # Handle modal result
            modalInstance.result
                .then (result) ->
                    $log.debug 'Job board popup closed with result:', result
                    options.onSuccess?(result)
                .catch (reason) ->
                    $log.debug 'Job board popup dismissed:', reason
                    options.onDismiss?(reason)
            
            return modalInstance
        
        # Open compact job details popup (readonly)
        @openJobDetailsPopup = (jobId, options = {}) ->
            $log.debug 'Opening job details popup for job:', jobId
            
            modalOptions = {
                templateUrl: 'common/templates/job-details-popup.html'
                controller: 'JobDetailsPopupController'
                size: options.size || 'md'
                backdrop: options.backdrop || true
                keyboard: options.keyboard || true
                resolve: {
                    jobId: () -> jobId
                }
            }
            
            modalInstance = $modal.open(modalOptions)
            
            return modalInstance
        
        return @
]

### jshint ignore: end ###
