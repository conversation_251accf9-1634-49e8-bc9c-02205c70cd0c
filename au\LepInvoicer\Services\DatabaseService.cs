using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Data.SqlClient;

namespace LepInvoicer.Services;

/// <summary>
/// Database service implementation
/// </summary>
public class DatabaseService : IDatabaseService, IDisposable
{
    private readonly ILogger<DatabaseService> _logger;
    private readonly InvoicerConfiguration _config;
    private SqlConnection? _connection;
    private bool _disposed = false;

    public DatabaseService(ILogger<DatabaseService> logger, IOptions<InvoicerConfiguration> config)
    {
        _logger = logger;
        _config = config.Value;
    }

    public Task InitializeAsync()
    {
        try
        {
            _logger.LogInformation("Initializing database connection...");
            _connection = new SqlConnection(_config.ConnectionString);
            _connection.Open();
            _logger.LogInformation("Database connection established successfully");
            return Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize database connection");
            throw;
        }
    }

    public Task<List<KeyValuePair<int, string>>> GetOrdersToInvoiceAsync(int batchSize)
    {
        _logger.LogInformation("Getting {BatchSize} orders to invoice", batchSize);
        
        // TODO: Implement actual database query
        // For now, return empty list
        var orders = new List<KeyValuePair<int, string>>();
        
        _logger.LogInformation("Found {OrderCount} orders to invoice", orders.Count);
        return Task.FromResult(orders);
    }

    public Task<List<OrderCredit>> GetCreditsToInvoiceAsync(int batchSize)
    {
        _logger.LogInformation("Getting {BatchSize} credits to invoice", batchSize);
        
        // TODO: Implement actual database query
        var credits = new List<OrderCredit>();
        
        _logger.LogInformation("Found {CreditCount} credits to invoice", credits.Count);
        return Task.FromResult(credits);
    }

    public Task<List<OrderCredit>> GetRefundsToInvoiceAsync(int batchSize)
    {
        _logger.LogInformation("Getting {BatchSize} refunds to invoice", batchSize);
        
        // TODO: Implement actual database query
        var refunds = new List<OrderCredit>();
        
        _logger.LogInformation("Found {RefundCount} refunds to invoice", refunds.Count);
        return Task.FromResult(refunds);
    }

    public Task<IOrder> GetOrderAsync(int orderId)
    {
        _logger.LogInformation("Getting order {OrderId}", orderId);
        
        // TODO: Implement actual database query
        throw new NotImplementedException("GetOrderAsync not yet implemented");
    }

    public Task ExecuteSqlAsync(string sql)
    {
        if (_connection == null)
            throw new InvalidOperationException("Database not initialized");

        try
        {
            _logger.LogDebug("Executing SQL: {Sql}", sql);
            
            using var command = new SqlCommand(sql, _connection);
            command.ExecuteNonQuery();
            
            return Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to execute SQL: {Sql}", sql);
            throw;
        }
    }

    public Task MarkOrderAsInvoicedAsync(int orderId)
    {
        var sql = string.Format(InvoicerConstants.SqlQueries.MarkOrderInvoicedSql, orderId);
        return ExecuteSqlAsync(sql);
    }

    public Task MarkOrderAsFailedAsync(int orderId, string errorMessage)
    {
        var escapedMessage = InvoicerUtilities.EscapeSqlString(errorMessage);
        var sql = string.Format(InvoicerConstants.SqlQueries.MarkOrderFailedSql, orderId, escapedMessage);
        return ExecuteSqlAsync(sql);
    }

    public Task MarkCreditAsInvoicedAsync(int creditId)
    {
        var sql = string.Format(InvoicerConstants.SqlQueries.MarkCreditInvoicedSql, creditId);
        return ExecuteSqlAsync(sql);
    }

    public Task LogInvoicingResultAsync(int orderId, int jobCount, decimal total, DateTime finishDate, bool success, string? details)
    {
        var successFlag = success ? InvoicerConstants.Database.LogSuccessFlag : InvoicerConstants.Database.LogFailureFlag;
        var escapedDetails = details != null ? $"'{InvoicerUtilities.EscapeSqlString(details)}'" : "null";
        
        var sql = string.Format(InvoicerConstants.SqlQueries.LogInvoicingResultSql,
            orderId, jobCount, total, finishDate.ToString(_config.DateFormat), successFlag, escapedDetails, DateTime.Now.ToString(_config.DateFormat));
        
        return ExecuteSqlAsync(sql);
    }

    public Task CleanupInvoicerLogsAsync()
    {
        _logger.LogInformation("Cleaning up invoicer logs...");
        return ExecuteSqlAsync(InvoicerConstants.SqlQueries.CleanupInvoicerLogsSql);
    }

    public void Dispose()
    {
        if (!_disposed)
        {
            _connection?.Close();
            _connection?.Dispose();
            _disposed = true;
            _logger.LogInformation("Database connection disposed");
        }
    }
}
