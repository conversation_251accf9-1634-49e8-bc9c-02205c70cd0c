-- Check customer email addresses for orders from June 9th that had "No email address found"
SELECT 
    o.Id as OrderId,
    c.Id as CustomerId,
    c.Name as <PERSON><PERSON><PERSON><PERSON>,
    c.Email,
    c.AccountEmail,
    o.ContactEmail
FROM [Order] o
INNER JOIN [Customer] c ON o.userId = c.Id
WHERE o.Id IN (1415584, 1415771, 1415807, 1416407, 1416475, 1416484, 1416493, 1416507, 1416580, 1416609)
ORDER BY o.Id;

-- Check if there are any customers with email addresses
SELECT TOP 10
    c.Id,
    c.Name,
    c.Email,
    c.AccountEmail
FROM [Customer] c
WHERE c.Email IS NOT NULL OR c.AccountEmail IS NOT NULL
ORDER BY c.Id DESC;
