-- =============================================
-- Simple Database Optimization Script
-- Target: Most critical performance issues
-- Compatible with all SQL Server editions
-- =============================================

USE [PRD_AU];
GO

PRINT '=== SIMPLE DATABASE OPTIMIZATION ===';
PRINT 'Database: ' + DB_NAME();
PRINT 'Server: ' + @@SERVERNAME;
PRINT 'Date: ' + CONVERT(VARCHAR(20), GETDATE(), 120);
PRINT '';

-- Create the most critical missing index: Order table
PRINT 'Creating critical index on Order table...';
IF NOT EXISTS (SELECT 1 FROM sys.indexes WHERE object_id = OBJECT_ID('dbo.Order') AND name = 'IX_Order_Status_DateModified')
BEGIN
    CREATE INDEX [IX_Order_Status_DateModified] ON [dbo].[Order] ([Status], [DateModified]) 
    INCLUDE ([Id], [userId]);
    PRINT 'SUCCESS: IX_Order_Status_DateModified created';
END
ELSE
    PRINT 'Index IX_Order_Status_DateModified already exists';

-- Create index on Comment table for JobId lookups
PRINT 'Creating index on Comment table...';
IF NOT EXISTS (SELECT 1 FROM sys.indexes WHERE object_id = OBJECT_ID('dbo.Comment') AND name = 'IX_Comment_JobId')
BEGIN
    CREATE INDEX [IX_Comment_JobId] ON [dbo].[Comment] ([JobId]);
    PRINT 'SUCCESS: IX_Comment_JobId created';
END
ELSE
    PRINT 'Index IX_Comment_JobId already exists';

-- Update statistics on critical tables
PRINT 'Updating statistics...';
UPDATE STATISTICS [dbo].[Order];
UPDATE STATISTICS [dbo].[Job];
UPDATE STATISTICS [dbo].[Comment];
PRINT 'Statistics updated';

-- Verification
PRINT '';
PRINT 'Verification:';
SELECT 
    OBJECT_NAME(object_id) AS table_name, 
    name AS index_name
FROM sys.indexes 
WHERE name IN ('IX_Order_Status_DateModified', 'IX_Comment_JobId');

PRINT '';
PRINT 'Optimization complete!';
