-- Complete diagnostic for CustomerID = 15029 INSERT issue
-- Run this entire script to see exactly what's happening

DECLARE @CurrentTime DATETIME = GETDATE();
DECLARE @FirstDayOfMonth DATETIME = DATEADD(MONTH, DATEDIFF(MONTH, 0, GETDATE()), 0);
DECLARE @LastDayOfMonth DATETIME = DATEADD(MONTH, DATEDIFF(MONTH, -1, GETDATE()), -1);

PRINT '=== DIAGNOSTIC REPORT FOR CUSTOMER 15029 ==='
PRINT 'Current Time: ' + CAST(@CurrentTime AS VARCHAR(50))
PRINT 'Last Day of Month: ' + CAST(@LastDayOfMonth AS VARCHAR(50))
PRINT ''

-- 1. Check customer 15029 basic info
PRINT '1. CUSTOMER 15029 BASIC INFO:'
SELECT 
    c.CustomerId,
    c.<PERSON>,
    c.PaymentTerms,
    c.<PERSON><PERSON>,
    c.<PERSON><PERSON><PERSON><PERSON><PERSON>astDue,
    c.<PERSON>,
    c.<PERSON>ei<PERSON>,
    c.MAT3mA,
    c<PERSON><PERSON>ffer
FROM Customer c
WHERE c.CustomerId = 15029;

-- 2. Check EDR calculation for customer 15029
PRINT '2. EDR CALCULATION:'
SELECT 
    c.CustomerId,
    c.MAT3mA,
    CASE
        WHEN c.MAT3mA >= 10000 AND c.MAT3mA < 15000 THEN 25
        WHEN c.MAT3mA >= 7500 AND c.MAT3mA < 10000 THEN 20
        WHEN c.MAT3mA >= 5000 AND c.MAT3mA < 7500 THEN 15
        WHEN c.MAT3mA >= 2000 AND c.MAT3mA < 5000 THEN 10
        WHEN c.MAT3mA >= 500 AND c.MAT3mA < 2000 THEN 5
        ELSE NULL
    END AS CalculatedEDRp
FROM Customer c
WHERE c.CustomerId = 15029;

-- 3. Check all available promotions
PRINT '3. AVAILABLE PROMOTIONS:'
SELECT 
    p.Id,
    p.PromotionCode,
    p.Discount,
    p.ShortDescription
FROM Promotion p 
WHERE p.PromotionCode LIKE 'My Reward %'
ORDER BY p.Discount;

-- 4. Check if customer 15029 qualifies for Cust CTE
PRINT '4. CUSTOMER IN CUST CTE:'
;WITH Cust AS (
    SELECT c.CustomerId,
        EDRp = CASE
            WHEN c.MAT3mA >= 10000 AND c.MAT3mA < 15000 THEN 25
            WHEN c.MAT3mA >= 7500 AND c.MAT3mA < 10000 THEN 20
            WHEN c.MAT3mA >= 5000 AND c.MAT3mA < 7500 THEN 15
            WHEN c.MAT3mA >= 2000 AND c.MAT3mA < 5000 THEN 10
            WHEN c.MAT3mA >= 500 AND c.MAT3mA < 2000 THEN 5
            ELSE NULL
        END,
        c.Name,
        c.MAT3mA,
        c.FranchiseCode
    FROM Customer c
    WHERE c.PaymentTerms IN ('Account', 'OnHold')
        AND (c.FranchiseCode = '' or c.FranchiseCode is null)
        AND c.MYOBPastDue <= 0
        AND (C.ProductPriceCode IN ('P0', NULL, '') AND C.FreightPriceCode IN ('F0', NULL, ''))
        AND c.CustomerId = 15029
)
SELECT * FROM Cust;

-- 5. Check existing offers for customer 15029
PRINT '5. EXISTING OFFERS FOR CUSTOMER 15029:'
SELECT 
    co.PromotionId,
    co.CustomerId,
    co.DateOfferEnds,
    p.PromotionCode,
    p.Discount
FROM CustomerOffers co
JOIN Promotion p ON p.Id = co.PromotionId
WHERE co.CustomerId = 15029
    AND co.DateOfferEnds = @LastDayOfMonth;

-- 6. Simulate the exact JOIN that the procedure does
PRINT '6. SIMULATING THE PROCEDURE JOIN:'
;WITH Cust AS (
    SELECT c.CustomerId,
        EDRp = CASE
            WHEN c.MAT3mA >= 10000 AND c.MAT3mA < 15000 THEN 25
            WHEN c.MAT3mA >= 7500 AND c.MAT3mA < 10000 THEN 20
            WHEN c.MAT3mA >= 5000 AND c.MAT3mA < 7500 THEN 15
            WHEN c.MAT3mA >= 2000 AND c.MAT3mA < 5000 THEN 10
            WHEN c.MAT3mA >= 500 AND c.MAT3mA < 2000 THEN 5
            ELSE NULL
        END,
        c.Name,
        c.MAT3mA,
        c.FranchiseCode
    FROM Customer c
    WHERE c.PaymentTerms IN ('Account', 'OnHold')
        AND (c.FranchiseCode = '' or c.FranchiseCode is null)
        AND c.MYOBPastDue <= 0
        AND (C.ProductPriceCode IN ('P0', NULL, '') AND C.FreightPriceCode IN ('F0', NULL, ''))
        AND c.CustomerId = 15029
),
ExistingOffers AS (
    SELECT PromotionId, CustomerId
    FROM CustomerOffers
    WHERE DateOfferEnds = @LastDayOfMonth
        AND CustomerId = 15029
)
SELECT 
    'JOIN RESULT' AS Step,
    c.CustomerId,
    c.Name,
    c.MAT3mA,
    c.EDRp,
    p.Id AS PromotionId,
    p.PromotionCode,
    p.Discount,
    eo.PromotionId AS ExistingPromotionId,
    CASE 
        WHEN eo.PromotionId IS NULL THEN 'WOULD INSERT'
        ELSE 'BLOCKED BY EXISTING OFFER'
    END AS Result
FROM Cust c
JOIN Promotion p ON p.Discount = c.EDRp AND p.PromotionCode LIKE 'My Reward %'
LEFT JOIN ExistingOffers eo ON eo.PromotionId = p.Id AND eo.CustomerId = c.CustomerId;

-- 7. Check if the JOIN is failing
PRINT '7. CHECKING WHY JOIN MIGHT FAIL:'
;WITH Cust AS (
    SELECT c.CustomerId,
        EDRp = CASE
            WHEN c.MAT3mA >= 10000 AND c.MAT3mA < 15000 THEN 25
            WHEN c.MAT3mA >= 7500 AND c.MAT3mA < 10000 THEN 20
            WHEN c.MAT3mA >= 5000 AND c.MAT3mA < 7500 THEN 15
            WHEN c.MAT3mA >= 2000 AND c.MAT3mA < 5000 THEN 10
            WHEN c.MAT3mA >= 500 AND c.MAT3mA < 2000 THEN 5
            ELSE NULL
        END,
        c.Name,
        c.MAT3mA,
        c.FranchiseCode
    FROM Customer c
    WHERE c.PaymentTerms IN ('Account', 'OnHold')
        AND (c.FranchiseCode = '' or c.FranchiseCode is null)
        AND c.MYOBPastDue <= 0
        AND (C.ProductPriceCode IN ('P0', NULL, '') AND C.FreightPriceCode IN ('F0', NULL, ''))
        AND c.CustomerId = 15029
)
SELECT 
    'CUSTOMER SIDE' AS Side,
    c.CustomerId,
    c.EDRp,
    'Looking for promotion with discount = ' + CAST(c.EDRp AS VARCHAR(10)) AS LookingFor
FROM Cust c

UNION ALL

SELECT 
    'PROMOTION SIDE' AS Side,
    CAST(p.Id AS VARCHAR(10)) AS CustomerId,
    p.Discount AS EDRp,
    'Available: ' + p.PromotionCode AS LookingFor
FROM Promotion p 
WHERE p.PromotionCode LIKE 'My Reward %'
ORDER BY Side, EDRp;

PRINT '=== END DIAGNOSTIC REPORT ==='
