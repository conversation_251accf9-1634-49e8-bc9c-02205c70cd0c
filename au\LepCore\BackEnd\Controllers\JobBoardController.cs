using lep.job;
using lep.jobmonitor;
using lep.jobmonitor.impl;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace LepCore.Controllers
{

	[Produces("application/json")]
	[Route("api/[controller]")]
	[Authorize(Roles = LepRoles.Staff)]
	[ApiExplorerSettings(IgnoreApi = true)]
	public class JobBoardController : Controller
	{
		public JobBoardController(JobBoard globalJobBoard)
		{
			GlobalJobBoard = globalJobBoard;
		}

		private JobBoard GlobalJobBoard { get; set; }

		[HttpGet("Get")]
		public IActionResult GetJobboard(string facilityStr = "FG", string board = "All")
		{
			if (GlobalJobBoard.Entries.Any())
			{
				Facility facility = Facility.PM;
				try
				{
					facility = (Facility)Enum.Parse(typeof(Facility), facilityStr);
				}
				catch (Exception)
				{
				}

				JobBoardTypes jobboard = JobBoardTypes.All;
				try
				{
					jobboard = (JobBoardTypes)Enum.Parse(typeof(JobBoardTypes), board);
				}
				catch (Exception)
				{
				}

				dynamic dataBeingSentToBrower = new
				{
					version = GlobalJobBoard.Version,
					data = GlobalJobBoard.GetEntiesAA(facility, jobboard)
				};

				return new OkObjectResult(dataBeingSentToBrower);
			}

			return new OkObjectResult(new { version = 0 });
		}

		[HttpGet("GetJson")]
		public async Task<IActionResult> GetJobboardJson(string facilityStr = "FG", string board = "All", [FromServices] JobBoardDTOHelper jobBoardHelper = null)
		{
			if (GlobalJobBoard.Entries.Any())
			{
				Facility facility = Facility.PM;
				try
				{
					facility = (Facility)Enum.Parse(typeof(Facility), facilityStr);
				}
				catch (Exception)
				{
				}

				JobBoardTypes jobboard = JobBoardTypes.All;
				try
				{
					jobboard = (JobBoardTypes)Enum.Parse(typeof(JobBoardTypes), board);
				}
				catch (Exception)
				{
				}

				// Filter entries by facility and board
				var filteredEntries = GlobalJobBoard.Entries
					.Where(e => e.Facility == facility)
					.Where(e => e.RowType == "1J" )
					.Where(e => board == "All" || e.BoardsToAppear.Contains(jobboard))
					.ToList();

				// Convert BoardsToAppear to string arrays
				foreach (var entry in filteredEntries)
				{
					// Ensure BoardsToAppear is not null
					if (entry.BoardsToAppear == null)
					{
						entry.BoardsToAppear = Array.Empty<JobBoardTypes>();
					}
				}

				// Populate timing states if helper is available
				if (jobBoardHelper != null)
				{
					await jobBoardHelper.PopulateTimingStatesAsync(filteredEntries);
				}

				// Use PascalCase for consistency with SignalR and C# conventions
				var dataBeingSentToBrower = new
				{
					Version = GlobalJobBoard.Version,
					Data = filteredEntries
				};

				return new OkObjectResult(dataBeingSentToBrower);
			}

			return new OkObjectResult(new { version = 0 });
		}

		[HttpGet("Version")]
		public IActionResult Version()
		{
			return new OkObjectResult(GlobalJobBoard.Version);
		}


		[HttpGet("create")]
		[AllowAnonymous]
		public async Task<IActionResult> Create([FromServices] JobBoardDTOHelper _)
		{
			await _.CreateJobBoardAsync();
			return Ok();
		}
	}
}
