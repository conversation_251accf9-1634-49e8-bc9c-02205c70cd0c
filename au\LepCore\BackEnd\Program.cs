using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Serilog;
using System;
using System.IO;
using System.Net.Http;
using System.Security.Cryptography.X509Certificates;

namespace LepCore
{
	public class Program
	{
		public static HttpClient ClientWestPack { get; } = new HttpClient();
		public static string secretKey = @"SecretKeySecretKeySecretKeySecretKeySecretKeySecretKeySecretKeyS";
		public static X509Certificate2 cert;

		public static void Main()
		{
			Log.Logger = new LoggerConfiguration()
			   .Enrich.FromLogContext()
			   .Enrich.WithCorrelationId()
			   .WriteTo.Console()
			   .CreateLogger();


			try
			{
				//https://blogs.perficient.com/2016/04/28/tsl-1-2-and-net-support/
				//System.Net.ServicePointManager.SecurityProtocol |= SecurityProtocolType.Tls |  SecurityProtocolType.Tls11 | SecurityProtocolType.Tls12;

				var basePath = Directory.GetCurrentDirectory();
				var certPath = basePath + "\\lepcore.pfx";
				//cert = new X509Certificate2(certPath, "lepcore");

				var config = new ConfigurationBuilder()
								.AddJsonFile("appsettings.json")
								//.AddCommandLine(args)
								.Build();


				Log.Logger = new LoggerConfiguration()
					.ReadFrom.Configuration(config)
					.CreateLogger();

				var host = new WebHostBuilder()

								.UseDefaultServiceProvider((context, options) =>
								{
									options.ValidateScopes = true;
									// https://docs.microsoft.com/en-us/aspnet/core/fundamentals/host/web-host?view=aspnetcore-2.1#scope-validation
								})
								.UseConfiguration(config)
								.UseKestrel()
								.UseUrls("http://*:5000;https://*:5001")
								.ConfigureKestrel((context, options) =>
								{
									options.Limits.MaxRequestBodySize = null;
								})
								.UseShutdownTimeout(TimeSpan.FromSeconds(10))

								.UseContentRoot(basePath)
								.UseIIS()
								.UseStartup<Startup>()
								.UseSerilog()
								.Build();

				Log.Information("Starting LepCore web host...");
				Log.Information("Application will be available at: http://localhost:5000");

				var startTime = DateTime.Now;
				Log.Information("Host.Run() starting at {StartTime}", startTime);

				host.Run();

				var endTime = DateTime.Now;
				Log.Information("Host.Run() completed at {EndTime}, took {ElapsedMs}ms", endTime, (endTime - startTime).TotalMilliseconds);
			}
			catch (Exception ex)
			{
				Log.Fatal(ex, "Application start-up failed");
				Console.WriteLine($"FATAL ERROR: {ex.Message}");
				Console.WriteLine($"Stack Trace: {ex.StackTrace}");

				// Also write to a simple text file for debugging
				try
				{
					var errorLog = Path.Combine(Directory.GetCurrentDirectory(), "startup_error.txt");
					File.WriteAllText(errorLog, $"Error: {ex.Message}\n\nStack Trace:\n{ex.StackTrace}\n\nInner Exception:\n{ex.InnerException?.ToString()}");
				}
				catch { /* Ignore file write errors */ }
			}
			finally
			{
				Log.CloseAndFlush();
			}
		}
	}
}
